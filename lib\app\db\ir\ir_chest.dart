import 'package:floor/floor.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/logger.dart';


// 红外巡检专用， 列头柜 ，高压柜等
@entity
class IrChest {

  @PrimaryKey()
  final String id;

  final String chestId;

  final String? taskId;

  final String? chestName;

  final String? progress;

  final bool? finish;

  final String userId;

  int deviceCount;

  final int checkedDeviceCount;

  IrChest(this.id ,this.chestId, this.taskId ,this.chestName, this.progress, this.finish, this.userId,
      this.deviceCount, this.checkedDeviceCount);

  String getProgress() {
    var d = checkedDeviceCount/deviceCount;
    var progress = _formatPercentage(d).toString();
    return progress;
  }

  int getIntProgress() {
    var d = checkedDeviceCount/deviceCount;
    var progress = _formatPercentage(d);
    return progress;
  }

  int _formatPercentage(double number) {
    return (number * 100).round();
  }

  @override
  String toString() {
    return 'IrChest{chestId: $chestId, taskId: $taskId, chestName: $chestName, progress: $progress, finish: $finish, userId: $userId, deviceCount: $deviceCount, checkedDeviceCount: $checkedDeviceCount}';
  }
}