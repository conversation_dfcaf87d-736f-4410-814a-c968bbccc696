

import 'package:floor/floor.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest.dart';

import 'ir_chest_device.dart';
import 'ir_chest_state.dart';


/// 红外巡检： 列头柜设备 dao
@dao
abstract class IrChestStateDao {

  // 查询是否有未查成的红外巡检
  @Query('SELECT * FROM IrChestState WHERE userId = :userId AND startTimeStamp != 0 OR endTimeStamp != 0')
  Future<List<IrChestState>> findAllState(String userId);

  // 查询当前红外的巡检状态
  @Query('SELECT * FROM IrChestState WHERE userId = :userId')
  Future<List<IrChestState>> findCurrentState(String userId);

  // 获取ir设备信息的时候就 根据userId插入一条 ，开始/结束时间都为0 的 状态信息；
  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertState(IrChestState device);

  // 更新 ir 设备温度和备注
  @Query('UPDATE IrChestState SET startTimeStamp = :startTimeStamp, endTimeStamp = :endTimeStamp WHERE userId = :userId')
  Future<void> updateProgress(String userId ,int startTimeStamp, int  endTimeStamp);

  @Query('delete FROM IrChestState WHERE userId = :userId')
  Future<void> clear(String userId);
}