import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_scankit/flutter_scankit.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';
import "../../../controllers/socket_server_controller.dart";
import "../../../controllers/socket_client_controller.dart";
import '../../../utils/myTextField.dart';
class SocketPageController extends GetxController {
  SocketServerController socketServerController = Get.find(); // 服务端实例
  SocketClientController socketClientController = Get.find(); // 客户端实例

  //TODO: Implement SocketPageController

  final count = 0.obs;

  var serverIP;

  // 客户端配置
  RxString toServerIP = "".obs;

  // 扫一扫
   late ScanKit scanKit;
  @override
  void onInit() {
    super.onInit();
    // 初始化扫一扫
    scanKit = ScanKit();
    scanKit.onResult.listen((val) {
      Future.delayed(Duration(seconds: 3), () {
        // 在延迟3秒后执行的代码
        getScanKit(val);
      });
      
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() async {
    super.onClose();
  }

  // 服务端配置,获取当前连接网络的IP
  initPlatformState()async{
    NetworkInfo networkInfo = NetworkInfo();
    String? wifi = "";
    try {
      wifi = await networkInfo.getWifiIP();
    }catch(err){
      print("无法获取当前网络信息");
    }
    String ipAddress = wifi ?? "";
    serverIP = ipAddress;
  }

  // 服务端创建
  serverCreateSocket()async{
    await initPlatformState();
    // 判断如果服务端已经建立服务则弹窗IP
    if(socketServerController.serverSocket != null){
      socketServerController.serverInfoAlter(serverIP);
    }else{
      socketServerController.createSocket(serverIP, 8888);
    }
    
  }

  // 服务端发送信息
  serverSend(){
    socketServerController.send("hello world");
  }
  /**扫一扫相关配置 */
  // 相机和外部存储权限(获取安卓权限)
  final permissions = const [
    Permission.storage,
    Permission.camera
  ];
  // 相机和外部存储权限(获取iod权限)
  final permissionGroup = const [
    Permission.camera,
    Permission.photos
  ];

  /**客户端IP输入框 */
  enterIPAlter()async{
    print("扫码连接");
    // 判断是否有相机和外存储的权限,如果没有则申请权限
    var permissionStatus = await permissions.request();
    var hasGrant = true;
    permissionStatus.forEach((key, value){
      if(value.isDenied){
        hasGrant = false;
      }
    });
    if (hasGrant) {
      permissions.request();
    } else {
      try {
        await scanKit.startScan();
      } on PlatformException {
        print("扫一扫报错");
      }
    }
    // Get.dialog(
    //   barrierDismissible:true,
    //   AlertDialog(
    //     title: const Text("输入服务端的IP地址"),
    //     content: Container(
    //       width: MyScreenUtil.width(300),
    //       height: MyScreenUtil.height(200),
    //       child: Column(
    //         children: [
    //           Container(
    //             child: MyTextField(
    //               isPassWord:false,
    //               hintText: "请输入服务端的IP地址",
    //               onChanged: (value) {
    //                 toServerIP.value = value;
    //               }
    //             ),
    //           ),
    //           Container(
    //             margin: EdgeInsets.only(top: MyScreenUtil.height(20)),
    //             child: ElevatedButton(
    //               onPressed: (){
    //                 Get.back();
    //                 socketClientController.connect(toServerIP.value, 8888);
    //               },
    //               child: const Text("连接"),
    //             ),
    //           )
    //         ],
    //       ),
    //     )
    //   )
    // );
  }
  // 获取扫描结果
  getScanKit(val){
    socketClientController.connect(val, 8888);
  }
  /*客户端创建连接 */
  clientCreateConnect(){
    if(socketClientController.clientSocket == null){
      enterIPAlter();
    }else{
      socketClientController.disconnect();
      print("连接已存在");
    }
  }
  /*客户端发送信息*/
  clientSend(message){
    socketClientController.sendData(message);
  }

  closeSocketFun(){
    if(socketClientController.clientSocket != null){
      socketClientController.disconnect();
      toastFun("socket 服务端已清除,可重新创建");
    } else if(socketServerController.serverSocket != null){
      socketServerController.closeSocket();
      toastFun("socket 客户端已清除,可重新创建");
    } else{
      toastFun("当前为空,无需清除");
    }
  }


// toast弹窗
  toastFun(content){
    Fluttertoast.showToast(
      msg: content,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
      timeInSecForIosWeb: 3,
      backgroundColor: Colors.black,
      textColor: Colors.white,
      fontSize: 16.0
    );
  }


}
