import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';

import '../../../../controllers/global_controller.dart';
import '../../../../utils/myTextField.dart';
import '../controllers/uphold_start_uphold_controller.dart';

// 先提条件
class Premise extends GetView<UpholdStartUpholdController> {
  GlobalController globalController = Get.find();
  Premise({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Obx(() => Column(
              children: [
                Container(
                  child: Column(
                    children: controller.premiseDataList
                        .asMap()
                        .entries
                        .map((entry) {
                          int index = entry.key;
                          dynamic item = entry.value;
                          return maintainEntries(
                              premiseItem: item, premiseIndex: index);
                        })
                        .toList()
                        .cast<Widget>(),
                  ),
                ),
                // 签字部分
                // controller.userRole.value == 1
                //     ? Container(
                //         width: double.infinity,
                //         padding: const EdgeInsets.all(10),
                //         margin: const EdgeInsets.only(top: 10),
                //         decoration: BoxDecoration(
                //             color: const Color.fromRGBO(238, 238, 238, 1),
                //             borderRadius: BorderRadius.only(
                //               topLeft: Radius.circular(MyScreenUtil.radius(10)),
                //               topRight:
                //                   Radius.circular(MyScreenUtil.radius(10)),
                //             )),
                //         child: const Text("签字",
                //             style: TextStyle(
                //                 color: Color.fromRGBO(43, 51, 63, 1))))
                //     : Container(),

                // // 签字模块
                // controller.userRole.value == 1
                //     ? Container(
                //         child: Column(
                //             children: controller.userNamesList
                //                 .map((userNameItem) {
                //                   return signatureModule(userNameItem);
                //                 })
                //                 .toList()
                //                 .cast<Widget>()),
                //       )
                //     : Container(),

                Row(
                  children: [
                    Expanded(
                        flex: 1,
                        child: Container(
                          margin: const EdgeInsets.only(top: 20),
                          child: ElevatedButton(
                              onPressed: () {
                                controller.handelComponentIndex(2, "workOrder");
                              },
                              child: const Text("下一步")),
                        ))
                  ],
                )
              ],
            )));
  }

  // 步骤抽离(维护条目)
  maintainEntries({premiseItem, premiseIndex}) {
    return Container(
      key: ValueKey(premiseIndex),
      margin: EdgeInsets.only(bottom: MyScreenUtil.height(20)),
      decoration: BoxDecoration(
          border: Border.all(
              color: controller.premiseComputeStatus(premiseIndex) == "等待维护"
                  ? const Color.fromRGBO(238, 238, 238, 1)
                  : controller.premiseComputeStatus(premiseIndex) == "已完成"
                      ? const Color.fromRGBO(29, 180, 119, 1)
                      : MyScreenUtil.ThemColor(),
              width: 1),
          borderRadius: BorderRadius.circular(MyScreenUtil.radius(10))),
      child: Column(
        children: [
          Container(
              width: double.infinity,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                  color: controller.premiseComputeStatus(premiseIndex) == "等待维护"
                      ? Color.fromRGBO(238, 238, 238, 1)
                      : controller.premiseComputeStatus(premiseIndex) == "已完成"
                          ? Color.fromRGBO(44, 185, 129, 0.259)
                          : const Color.fromRGBO(249, 230, 232, 1),
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(MyScreenUtil.radius(10)),
                      topRight: Radius.circular(MyScreenUtil.radius(10)))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                      flex: 1,
                      child: Container(
                          child: Text("${premiseItem.content}",
                              style: const TextStyle(
                                  color: Color.fromRGBO(43, 51, 63, 1))))),
                  Container(
                    margin: EdgeInsets.only(right: MyScreenUtil.width(20)),
                    child: Text(controller.premiseComputeStatus(premiseIndex),
                        style: TextStyle(
                            color: controller
                                        .premiseComputeStatus(premiseIndex) ==
                                    "等待维护"
                                ? const Color.fromRGBO(43, 51, 63, 1)
                                : controller.premiseComputeStatus(
                                            premiseIndex) ==
                                        "已完成"
                                    ? const Color.fromRGBO(29, 180, 119, 1)
                                    : const Color.fromRGBO(195, 12, 34, 1))),
                  )
                ],
              )),

          /**新增的输入框 */
          premiseItem.fillingValue == 1
              ? Container(
                  padding: EdgeInsets.only(
                      left: MyScreenUtil.width(14),
                      right: MyScreenUtil.width(14)),
                  // height: MyScreenUtil.height(50),
                  child: Wrap(
                    alignment:WrapAlignment.start,
                    children: createInputBox(premiseItem).cast<Widget>(),
                  ),
                )
              : Container(),
          premiseItem.fillingPictureValue == 1
              ? Container(
                  padding: EdgeInsets.only(
                      top: MyScreenUtil.width(20),
                      left: MyScreenUtil.width(14),
                      right: MyScreenUtil.width(14)),
                  child: Row(
                    children: [
                      premiseItem.gapPicture.isNotEmpty
                          ? Container(
                              width: MyScreenUtil.width(100),
                              height: MyScreenUtil.height(100),
                              margin: EdgeInsets.only(
                                  right: MyScreenUtil.width(20)),
                              child: Image.memory(
                                base64Decode(premiseItem.gapPicture),
                                fit: BoxFit.cover, // 调整图像大小以适应容器
                              ))
                          : ElevatedButton(
                              onPressed: () {
                                controller.uploadPhoto(
                                    premiseItem, premiseIndex, '先提条件');
                              },
                              child: Text("上传"),
                            ),
                    ],
                  ),
                )
              : Container(),
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  child: Text("执行人 : ${controller.userNames.value}"),
                ),
                Container(
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.only(right: 10),
                        child: Text("执行结果"),
                      ),
                      Container(
                          child:
                              controller.premiseComputeStatus(premiseIndex) ==
                                      "已完成"
                                  ? const Icon(Icons.expand_more_outlined,
                                      color: Color.fromRGBO(29, 180, 119, 1))
                                  : ElevatedButton(
                                      onPressed: () {
                                        controller.premiseExecute(premiseItem);
                                      },
                                      child: Text("已执行"),
                                    ))
                    ],
                  ),
                ),
                Container(
                  child: Text(
                      "执行时间: ${controller.userRole.value == 0 ? controller.formatTime(premiseItem.operatorTime) : controller.formatTime(premiseItem.supervisorTime)}"),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  // 签字模块
  signatureModule(userNameItem) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
          border: Border(
              bottom: BorderSide(
                  color: Color.fromRGBO(238, 238, 238, 1), width: 1))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            child: Text(
                "${userNameItem.role == 0 ? "执行人" : "监督人"}签字: ${userNameItem.name}"),
          ),
          Container(
              // child: Text("${controller.lignatureViewList.length}"),
              child: controller.lignatureViewList['${userNameItem.uid}'] != null
                  ? Container(
                      width: MyScreenUtil.width(200),
                      height: MyScreenUtil.height(100),
                      child: Image.memory(
                        controller.signatureAnalysis(userNameItem.uid),
                        fit: BoxFit.fill,
                      ),
                    )
                  : const Text("请签字")),
          Container(
              child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(bottom: 10),
                child: ElevatedButton(
                    onPressed:
                        controller.lignatureViewList['${userNameItem.uid}'] !=
                                null
                            ? null
                            : () {
                                controller.signAlter(userNameItem, '先提条件');
                              },
                    child: const Text("电子签名")),
              ),
              Container(
                child: controller.lignatureViewList['${userNameItem.uid}'] !=
                        null
                    ? Text(
                        "${controller.formatTime("${controller.lignatureViewList['${userNameItem.uid}']['date']}")}")
                    : null,
              )
            ],
          ))
        ],
      ),
    );
  }

  // 渲染输入框
  createInputBox(premiseItem) {
    List inputList = [];
    // 执行人模板
    if (premiseItem.status == 0) {
      List inputValueList = json.decode(premiseItem.gapPrice);
      for (var i = 0; i < premiseItem.amount; i++) {
        inputList.add(
          Container(
            width:MyScreenUtil.width(230),
            height: MyScreenUtil.height(50),
            child: Row(
              children: [
                Container(
                  margin: EdgeInsets.only(
                      left: MyScreenUtil.width(10),
                      right: MyScreenUtil.width(10)),
                  child: Text(
                    "数值${i + 1}",
                    style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),
                  ),
                ),
                Container(
                  width: 120,
                  height: MyScreenUtil.height(50),
                  child: MyTextField(
                      keyIndex: "${premiseItem.id}${i}",
                      isFocusName: false, // 是否监听失去焦点
                      initialValue: '',
                      isPassWord: false,
                      hintText: "${inputValueList[i]}",
                      enabled: premiseItem.operatorResult == 1
                          ? false
                          : true, // 是否回显
                      onChanged: (value) {
                        controller.premiseInputValue(premiseItem, value, i);
                      }),
                )
              ],
            ),
          ),
        );
      }
    } else {
      List gapPriceList = json.decode(premiseItem.gapPrice);
      gapPriceList.forEach((item) {
        inputList.add(Container(
          width:MyScreenUtil.width(230),
          height: MyScreenUtil.height(50),
          child: Row(
            children: [
              Container(
                margin: EdgeInsets.only(
                    left: MyScreenUtil.width(10),
                    right: MyScreenUtil.width(10)),
                child: Text(
                  "数值 $item",
                  style: TextStyle(fontSize: MyScreenUtil.fontSize(22)),
                ),
              )
            ],
          ),
        ));
      });
    }

    return inputList;
  }

  createUploadBox(premiseItem) {
    List inputList = [];
    // 执行人模板
    if (premiseItem.status == 0) {
      List inputValueList = json.decode(premiseItem.gapPrice);
      for (var i = 0; i < 2; i++) {
        inputList.add(
          Container(
            height: MyScreenUtil.height(50),
            child: Row(
              children: [
                Container(
                  margin: EdgeInsets.only(
                      left: MyScreenUtil.width(10),
                      right: MyScreenUtil.width(10)),
                  child: Text(
                    "数值${i + 1}",
                    style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),
                  ),
                ),
                Container(
                  width: 120,
                  height: MyScreenUtil.height(50),
                  child: MyTextField(
                      keyIndex: "${premiseItem.id}${i}",
                      isFocusName: false, // 是否监听失去焦点
                      initialValue: '',
                      isPassWord: false,
                      hintText: "${inputValueList[i]}",
                      enabled: premiseItem.operatorResult == 1
                          ? false
                          : true, // 是否回显
                      onChanged: (value) {
                        controller.premiseInputValue(premiseItem, value, i);
                      }),
                )
              ],
            ),
          ),
        );
      }
    } else {
      List gapPriceList = json.decode(premiseItem.gapPrice);
      gapPriceList.forEach((item) {
        inputList.add(Container(
          height: MyScreenUtil.height(50),
          child: Row(
            children: [
              Container(
                margin: EdgeInsets.only(
                    left: MyScreenUtil.width(10),
                    right: MyScreenUtil.width(10)),
                child: Text(
                  "数值 $item",
                  style: TextStyle(fontSize: MyScreenUtil.fontSize(22)),
                ),
              )
            ],
          ),
        ));
      });
    }

    return inputList;
  }
}
