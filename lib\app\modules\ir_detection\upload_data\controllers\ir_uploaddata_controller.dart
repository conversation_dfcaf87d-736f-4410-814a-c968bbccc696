import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:sjzx_patrol_system_mobile/app/db/db_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/toast.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/ir_detection/manager/ir_manager.dart';
import 'package:sjzx_patrol_system_mobile/app/retrofit/entity/ir_upload_req.dart';
import 'package:sjzx_patrol_system_mobile/app/retrofit/ir_datasource.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/file_util.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/loading_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/logger.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/string_util.dart';

import '../../../../../main.dart';
import '../../../../api/env_config.dart';
import '../../../../controllers/global_controller.dart';
import '../../../../controllers/sqflite_controller.dart';
import '../../model/ir_convert.dart';

class IrUploadDataController extends GetxController {
  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();

  List<ToDoUploadUIItem> uiList = [];

  Map userInfo = {}.obs;
  String userId = "";

  @override
  void onInit()async{
    super.onInit();
    userId = globalController.queryCurrentUserId() ?? '';
    _fetchUploadData();
  }

  _fetchUploadData() async {
    await _findUserInfo();
    var hasData = await DBHelper.existIrUpload(userId);
    if(!hasData) {
      // 暂无数据
      return;
    }
    var todoList = await DBHelper.getIrDeviceListByUser(userId);
    var datas = todoList.where((e) => !StringUtil.isEmpty(e.checkTemperature)).toList();
    if(datas.isEmpty) {
      // 暂无数据
      update();
      return;
    }
    var state = await DBHelper.currentIrState(userId);
    var todoItem = ToDoUploadUIItem();
    todoItem.userId = userId;
    todoItem.userName = userInfo['name'];
    todoItem.avatar = userInfo['avatar'];
    todoItem.technicalPost = userInfo['technicalPost'];
    todoItem.departMentName = userInfo['dept'];
    todoItem.position = userInfo['occupation'];
    todoItem.devicesCount = "${datas.length}";
    todoItem.startTime = state?.startTimeStamp ?? 0;
    todoItem.endTime = state?.endTimeStamp ?? 0;
    todoItem.uploadStatus =  0;
    uiList.clear();
    uiList.add(todoItem);
    update();
  }

  // 查找用户信息
  Future _findUserInfo()async{
    var userInfoData = await sqfliteController.findUsers("eid='$userId'");
    if(userInfoData.isEmpty) return;
    var userItem = userInfoData[0];
    userInfo["eid"] = userItem.eid;
    userInfo["name"] = userItem.name;
    userInfo["avatar"] = userItem.avatar;
    userInfo["dept"] = userItem.deptName;
    userInfo["patrolStartTime"] = userItem.patrolStartTime;
    userInfo["patrolEndTime"] = userItem.patrolEndTime;
    userInfo['occupation'] = userItem.occupation;
    userInfo['technicalPost'] = userItem.technicalPost;
    update();
  } 

  // 数据上传 ,重置 各种表状态
  uploadAction() async {
    var state = await DBHelper.currentIrState(userId);
    if(state == null){
      toast('当前巡检任务未结束, 无法上传数据');
      return;
    }

    if(state.startTimeStamp == 0 || state.endTimeStamp == 0){
       toast('当前巡检任务未结束, 无法上传数据');
       return;
    }

    LoadingManager.showLoading();
    var result = await uploadData();
    if(!result){
      LoadingManager.disMiss();
      return;
    }

    /// 上传成功之后 重置 数据
    await IrManager.resetIrData(userId);
    LoadingManager.disMiss();
    Get.back();
  }

  /// 调用上传数据接口 todo
  Future<bool> uploadData() async {
    var userDataSource = IrDataSource(retrofitDio , baseUrl: Host.userApi);
    var allDevices = await DBHelper.getIrDeviceListByUser(userId);
    var todoUploadList = allDevices.where((e) => !StringUtil.isEmpty(e.checkTemperature)).toList();
    if(todoUploadList.isEmpty) {
      // 暂无数据
      return false;
    }

    var state = await DBHelper.currentIrState(userId);
    if(state == null) return false;

    var todoList = todoUploadList.map((e) => convertIrRequestUploadBody(e)).toList();

    await Future.forEach(todoList, (e) async{
      // e.picture = await FileUtil.createBase64FromUrl("/storage/emulated/0/DCIM/Camera/IMG_20211106_204638697.jpg");
      var base64 = await FileUtil.createBase64FromUrl(e.picture ?? '');
      e.picture = base64;
    });

    var reqBody = IrUploadReq()
      ..userId = userId
      ..devicesCount = todoUploadList.length.toString()
      ..endTime = state.endTimeStamp
      ..startTime = state.startTimeStamp
      ..occupation = userInfo['occupation'] ?? ''
      ..deviceList = todoList;

    logger('上传ir： 请求参数====> ${reqBody.toString()}');

    var resp = await userDataSource.commitIrDevices(reqBody);
    if(resp.code == 0){
      toast('${resp.msg}');
    }
    return resp.success();
  }


  @override
  void onClose() {
    FileUtil.delTempImages();
    super.onClose();
  }


}

class ToDoUploadUIItem {
   String? userId;
   String? userName;
   String? avatar;
   String? departMentName;  // floor
   String? position;  // 职位
   String? technicalPost;  // 人员专业
   String? devicesCount;
   int startTime = 0;
   int endTime = 0;
   int uploadStatus = 0;  // 0 待上传， 1 上传中 ， 2 已上传
}
