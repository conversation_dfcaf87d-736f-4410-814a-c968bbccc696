import 'package:floor/floor.dart';
import 'package:flutter/material.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/string_util.dart';


// 红外巡检专用， 柜子下边的设备
@entity
class IrChestDevice {

  @PrimaryKey()
  final String id;

  final String irDeviceId;

  final String? deviceName;

  final String? taskId;

  String? infraredDeviceTypeName;  // chestName

  String? infraredDeviceTypeId;  //属于哪个柜子id 28NQfIapgvU

  String? code;  // 设备编码  01010101
  String? num;  // 1#
  String? installationSite;  // 位置
  String? project;  // A路
  String? upperLimitValue;  // 38 （上限值 ： 度）

  bool? finish;

  //
  String checkTemperature;  // 从设备获取的温度 ！！
  String comments;  // 备注 ！！

  final String userId;

  int detectionTime = 0;  // 检测时间

  @ignore
  bool selected = false;  // 本地使用

  String path = "";

  @ignore
  TextEditingController textEditingController = TextEditingController();


  String secondName() {
     return deviceName ?? '';
     return '${infraredDeviceTypeName}-${num}-${project}';
     // return 'fwo我房间饿哦附件饿哦感觉饿哦个哦就个哦额共计哦额吉给哦额共计哦就';
  }

  String getFullName() {
    return '${deviceName}-${num}';
  }

  // 该设备 是否巡检完成
  bool isFinish() {
    return !StringUtil.isEmpty(checkTemperature) && (!StringUtil.isEmpty(path));
  }


  IrChestDevice(
      this.id,
      this.irDeviceId,
      this.deviceName,
      this.taskId,
      this.infraredDeviceTypeId,
      this.infraredDeviceTypeName,
      this.code,
      this.num,
      this.installationSite,
      this.project,
      this.upperLimitValue,
      this.finish,
      this.checkTemperature,
      this.comments,
      this.userId,
      this.detectionTime,
      this.path
      );
}