<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <SurfaceView
            android:id="@+id/surfaceView"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <Space
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/ver_textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="SDK Version: 02000005" />

            <Space
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/textView3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:text="反馈问题：1版本，2接口，3错误码" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_init"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="初始化" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:id="@+id/btn_cleanup"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="反初始化" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:id="@+id/btn_exit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="退出" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_enum"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="枚举设备" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:id="@+id/btn_login"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="登录" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:id="@+id/btn_logout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="注销" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_upgrade"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="升级" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:id="@+id/btn_upgradeState"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="升级状态" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:id="@+id/btn_upgradeClose"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="停止升级" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_startPreview"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="开始预览" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:id="@+id/btn_fileTransfer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="文件传输" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:id="@+id/btn_config"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5"
                android:text="配置" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <Spinner
                android:id="@+id/spinner_resolution"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Spinner
                android:id="@+id/spinner_transfer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Spinner
                android:id="@+id/spinner_config"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="5" />
        </LinearLayout>



        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <Space
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/max_tmp_tv"
                android:textColor="@color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="最高温：" />

            <Space
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/stream_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:textColor="@color/white"
                android:layout_weight="1"
                android:text="码流类型：" />

            <TextView
                android:id="@+id/min_tmp_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="最低温：" />

        </LinearLayout>

    </LinearLayout>




</androidx.constraintlayout.widget.ConstraintLayout>