
class WorkOrder{
  final String id;
  final String? maintenancePlanNum;
  final String? deptId;
  final String? deptName;
  final String? deviceGroupId;
  final String? deviceGroupName;
  final int? type;
  final String? typeName;
  final String? installationSite;
  final String? model;
  final String? manufacturer;
  final String? deviceNum;
  final String? phone;
  final String? contactPhone;
  final String? factory;
  final String? deviceFlowId;
  final int? planTime;
  final int isFinish;
  final int isUpload;
  final int isPreview;
  final int finishDate;
  final int finishStep;
  final String? week;
  final String? parentGroupId;
  final String? parentGroupName;
  String? userId;


  WorkOrder({
    required this.id,
    required this.maintenancePlanNum,
    required this.deptId,
    required this.deptName,
    required this.deviceGroupId,
    required this.deviceGroupName,
    required this.type,
    required this.typeName,
    required this.installationSite,
    required this.model,
    required this.manufacturer,
    required this.deviceNum,
    required this.phone,
    required this.contactPhone,
    required this.factory,
    required this.deviceFlowId,
    required this.planTime,
    required this.isFinish,
    required this.isUpload,
    required this.isPreview,
    required this.finishDate,
    required this.finishStep,
    required this.week,
    required this.parentGroupId,
    required this.parentGroupName,
    this.userId
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      "id":id,
      "maintenancePlanNum":maintenancePlanNum,
      "deptId":deptId,
      "deptName":deptName,
      "deviceGroupId":deviceGroupId,
      "deviceGroupName":deviceGroupName,
      "type":type,
      "typeName":typeName,
      "installationSite":installationSite,
      "model":model,
      "manufacturer":manufacturer,
      "deviceNum":deviceNum,
      "phone":phone,
      "contactPhone":contactPhone,
      "factory":factory,
      "deviceFlowId":deviceFlowId,
      "planTime":planTime,
      "isFinish":isFinish,
      "isUpload":isUpload,
      "isPreview":isPreview,
      "finishDate":finishDate,
      "finishStep":finishStep,
      "week":week,
      "parentGroupId":parentGroupId,
      "parentGroupName":parentGroupName,
      "userId":userId
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  // @override
  Map<String, dynamic> toJson() {
    return {
      "id":id,
      "maintenancePlanNum":maintenancePlanNum,
      "deptId":deptId,
      "deptName":deptName,
      "deviceGroupId":deviceGroupId,
      "deviceGroupName":deviceGroupName,
      "type":type,
      "typeName":typeName,
      "installationSite":installationSite,
      "model":model,
      "manufacturer":manufacturer,
      "deviceNum":deviceNum,
      "phone":phone,
      "contactPhone":contactPhone,
      "factory":factory,
      "deviceFlowId":deviceFlowId,
      "planTime":planTime,
      "isFinish":isFinish,
      "isUpload":isUpload,
      "isPreview":isPreview,
      "finishDate":finishDate,
      "finishStep":finishStep,
      "week":week,
      "parentGroupId":parentGroupId,
      "parentGroupName":parentGroupName

    };
  }
}