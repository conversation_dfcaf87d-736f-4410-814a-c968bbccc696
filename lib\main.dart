import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:sjzx_patrol_system_mobile/app/controllers/device_manager.dart';
import './app/controllers/global_controller.dart';
import './app/controllers/socket_server_controller.dart';
import './app/controllers/socket_client_controller.dart';
import './app/controllers/sqflite_controller.dart';
import './app/controllers/upholdSqflite_controller.dart';
import 'app/api/env_config.dart';
import 'app/retrofit/app_dio.dart';
import 'app/routes/app_pages.dart';
import 'package:get_storage/get_storage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

final retrofitDio = AppDio.getInstance();

void main() async {
  DeviceConfig.switchDevice(DEVICE.phone);

  EnvConfig.switchEnv(Env.Dev);

  await Get.put(GlobalController());
  await Get.put(SocketServerController());
  await Get.put(SocketClientController());
  await Get.put(SQfliteController());
  await Get.put(UpholdSQLController());
  await GetStorage.init();

  var initRoute = await DeviceManager.initRoutePath();

  const myColor = MaterialColor(0xFFF30022, {
    50: Color.fromRGBO(195, 0, 34, 0.1),
    100: Color.fromRGBO(195, 0, 34, 0.2),
    200: Color.fromRGBO(195, 0, 34, 0.3),
    300: Color.fromRGBO(195, 0, 34, 0.4),
    400: Color.fromRGBO(195, 0, 34, 0.5),
    500: Color.fromRGBO(195, 0, 34, 0.6),
    600: Color.fromRGBO(195, 0, 34, 0.7),
    700: Color.fromRGBO(195, 0, 34, 0.8),
    800: Color.fromRGBO(195, 0, 34, 0.9),
    900: Color.fromRGBO(195, 0, 34, 1.0),
  });


  DeviceConfig.isPad().then((isPad) async{
    if(isPad) {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }else {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown
      ]);
    }
    runApp(ScreenUtilInit(
        designSize: const Size(1440, 870),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return GetMaterialApp(
            localizationsDelegates: const [
              // 解决长安表单复制粘贴改为中文
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              // 解决长安表单复制粘贴改为中文
              Locale('zh', 'CN'),
              Locale('en', 'US'),
            ],
            theme: ThemeData(
              primarySwatch: myColor,
            ),
            title: "数据中心巡检系统",
            // initialBinding: GlobalBinding(),
            defaultTransition: Transition.rightToLeft,
            // initialRoute: AppPages.INITIAL,
            initialRoute: initRoute,
            getPages: AppPages.routes,
            builder: EasyLoading.init(),
          );
        }));

    if(Platform.isAndroid){
      SystemUiOverlayStyle style = const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          ///状态栏的图标和字体的颜色
          statusBarIconBrightness: Brightness.dark
      );
      SystemChrome.setSystemUIOverlayStyle(style);
    }
  });
}

