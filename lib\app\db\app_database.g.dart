// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database.dart';

// **************************************************************************
// FloorGenerator
// **************************************************************************

abstract class $AppDataBaseBuilderContract {
  /// Adds migrations to the builder.
  $AppDataBaseBuilderContract addMigrations(List<Migration> migrations);

  /// Adds a database [Callback] to the builder.
  $AppDataBaseBuilderContract addCallback(Callback callback);

  /// Creates the database and initializes it.
  Future<AppDataBase> build();
}

// ignore: avoid_classes_with_only_static_members
class $FloorAppDataBase {
  /// Creates a database builder for a persistent database.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDataBaseBuilderContract databaseBuilder(String name) =>
      _$AppDataBaseBuilder(name);

  /// Creates a database builder for an in memory database.
  /// Information stored in an in memory database disappears when the process is killed.
  /// Once a database is built, you should keep a reference to it and re-use it.
  static $AppDataBaseBuilderContract inMemoryDatabaseBuilder() =>
      _$AppDataBaseBuilder(null);
}

class _$AppDataBaseBuilder implements $AppDataBaseBuilderContract {
  _$AppDataBaseBuilder(this.name);

  final String? name;

  final List<Migration> _migrations = [];

  Callback? _callback;

  @override
  $AppDataBaseBuilderContract addMigrations(List<Migration> migrations) {
    _migrations.addAll(migrations);
    return this;
  }

  @override
  $AppDataBaseBuilderContract addCallback(Callback callback) {
    _callback = callback;
    return this;
  }

  @override
  Future<AppDataBase> build() async {
    final path = name != null
        ? await sqfliteDatabaseFactory.getDatabasePath(name!)
        : ':memory:';
    final database = _$AppDataBase();
    database.database = await database.open(
      path,
      _migrations,
      _callback,
    );
    return database;
  }
}

class _$AppDataBase extends AppDataBase {
  _$AppDataBase([StreamController<String>? listener]) {
    changeListener = listener ?? StreamController<String>.broadcast();
  }

  IrChestDao? _getChestDaoInstance;

  IrChestDeviceDao? _getChestDeviceDaoInstance;

  IrChestStateDao? _getChestStateDaoInstance;

  IrTaskDao? _getTaskDaoInstance;

  Future<sqflite.Database> open(
    String path,
    List<Migration> migrations, [
    Callback? callback,
  ]) async {
    final databaseOptions = sqflite.OpenDatabaseOptions(
      version: 3,
      onConfigure: (database) async {
        await database.execute('PRAGMA foreign_keys = ON');
        await callback?.onConfigure?.call(database);
      },
      onOpen: (database) async {
        await callback?.onOpen?.call(database);
      },
      onUpgrade: (database, startVersion, endVersion) async {
        await MigrationAdapter.runMigrations(
            database, startVersion, endVersion, migrations);

        await callback?.onUpgrade?.call(database, startVersion, endVersion);
      },
      onCreate: (database, version) async {
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `IrChest` (`id` TEXT NOT NULL, `chestId` TEXT NOT NULL, `taskId` TEXT, `chestName` TEXT, `progress` TEXT, `finish` INTEGER, `userId` TEXT NOT NULL, `deviceCount` INTEGER NOT NULL, `checkedDeviceCount` INTEGER NOT NULL, PRIMARY KEY (`id`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `IrChestDevice` (`id` TEXT NOT NULL, `irDeviceId` TEXT NOT NULL, `deviceName` TEXT, `taskId` TEXT, `infraredDeviceTypeName` TEXT, `infraredDeviceTypeId` TEXT, `code` TEXT, `num` TEXT, `installationSite` TEXT, `project` TEXT, `upperLimitValue` TEXT, `finish` INTEGER, `checkTemperature` TEXT NOT NULL, `comments` TEXT NOT NULL, `userId` TEXT NOT NULL, `detectionTime` INTEGER NOT NULL, `path` TEXT NOT NULL, PRIMARY KEY (`id`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `IrChestState` (`userId` TEXT NOT NULL, `startTimeStamp` INTEGER NOT NULL, `endTimeStamp` INTEGER NOT NULL, PRIMARY KEY (`userId`))');
        await database.execute(
            'CREATE TABLE IF NOT EXISTS `IrTask` (`taskId` TEXT NOT NULL, `inspector` TEXT, `inspectorId` TEXT, `departId` TEXT, `departName` TEXT, `type` INTEGER, `status` INTEGER, `userId` TEXT NOT NULL, `startTime` INTEGER NOT NULL, `endTime` INTEGER NOT NULL, `deviceCount` INTEGER NOT NULL, `checkedDeviceCount` INTEGER NOT NULL, `hasUpLoad` INTEGER, PRIMARY KEY (`taskId`))');

        await callback?.onCreate?.call(database, version);
      },
    );
    return sqfliteDatabaseFactory.openDatabase(path, options: databaseOptions);
  }

  @override
  IrChestDao get getChestDao {
    return _getChestDaoInstance ??= _$IrChestDao(database, changeListener);
  }

  @override
  IrChestDeviceDao get getChestDeviceDao {
    return _getChestDeviceDaoInstance ??=
        _$IrChestDeviceDao(database, changeListener);
  }

  @override
  IrChestStateDao get getChestStateDao {
    return _getChestStateDaoInstance ??=
        _$IrChestStateDao(database, changeListener);
  }

  @override
  IrTaskDao get getTaskDao {
    return _getTaskDaoInstance ??= _$IrTaskDao(database, changeListener);
  }
}

class _$IrChestDao extends IrChestDao {
  _$IrChestDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database, changeListener),
        _irChestInsertionAdapter = InsertionAdapter(
            database,
            'IrChest',
            (IrChest item) => <String, Object?>{
                  'id': item.id,
                  'chestId': item.chestId,
                  'taskId': item.taskId,
                  'chestName': item.chestName,
                  'progress': item.progress,
                  'finish': item.finish == null ? null : (item.finish! ? 1 : 0),
                  'userId': item.userId,
                  'deviceCount': item.deviceCount,
                  'checkedDeviceCount': item.checkedDeviceCount
                },
            changeListener);

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<IrChest> _irChestInsertionAdapter;

  @override
  Future<List<IrChest>> findAllDevices(String userId) async {
    return _queryAdapter.queryList('SELECT * FROM IrChest WHERE userId = ?1',
        mapper: (Map<String, Object?> row) => IrChest(
            row['id'] as String,
            row['chestId'] as String,
            row['taskId'] as String?,
            row['chestName'] as String?,
            row['progress'] as String?,
            row['finish'] == null ? null : (row['finish'] as int) != 0,
            row['userId'] as String,
            row['deviceCount'] as int,
            row['checkedDeviceCount'] as int),
        arguments: [userId]);
  }

  @override
  Future<List<IrChest>> findAllChestByTaskId(
    String userId,
    String taskId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM IrChest WHERE userId = ?1 AND taskId = ?2',
        mapper: (Map<String, Object?> row) => IrChest(
            row['id'] as String,
            row['chestId'] as String,
            row['taskId'] as String?,
            row['chestName'] as String?,
            row['progress'] as String?,
            row['finish'] == null ? null : (row['finish'] as int) != 0,
            row['userId'] as String,
            row['deviceCount'] as int,
            row['checkedDeviceCount'] as int),
        arguments: [userId, taskId]);
  }

  @override
  Future<void> updateProgress(
    String userId,
    String chestId,
    String progress,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrChest SET progress = ?3 WHERE userId = ?1 AND chestId = ?2',
        arguments: [userId, chestId, progress]);
  }

  @override
  Future<void> updateCheckedDeviceCount(
    String userId,
    String taskId,
    String chestId,
    int checkedDeviceCount,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrChest SET checkedDeviceCount = ?4 WHERE userId = ?1 AND chestId = ?3 AND taskId = ?2',
        arguments: [userId, taskId, chestId, checkedDeviceCount]);
  }

  @override
  Future<void> clear(String userId) async {
    await _queryAdapter.queryNoReturn('delete FROM IrChest WHERE userId = ?1',
        arguments: [userId]);
  }

  @override
  Stream<List<IrChest>> listenChestList(String userId) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM IrChest WHERE userId = ?1',
        mapper: (Map<String, Object?> row) => IrChest(
            row['id'] as String,
            row['chestId'] as String,
            row['taskId'] as String?,
            row['chestName'] as String?,
            row['progress'] as String?,
            row['finish'] == null ? null : (row['finish'] as int) != 0,
            row['userId'] as String,
            row['deviceCount'] as int,
            row['checkedDeviceCount'] as int),
        arguments: [userId],
        queryableName: 'IrChest',
        isView: false);
  }

  @override
  Stream<List<IrChest>> listenChestListByTask(
    String userId,
    String taskId,
  ) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM IrChest WHERE userId = ?1 AND taskId = ?2',
        mapper: (Map<String, Object?> row) => IrChest(
            row['id'] as String,
            row['chestId'] as String,
            row['taskId'] as String?,
            row['chestName'] as String?,
            row['progress'] as String?,
            row['finish'] == null ? null : (row['finish'] as int) != 0,
            row['userId'] as String,
            row['deviceCount'] as int,
            row['checkedDeviceCount'] as int),
        arguments: [userId, taskId],
        queryableName: 'IrChest',
        isView: false);
  }

  @override
  Future<void> insertList(List<IrChest> irchestList) async {
    await _irChestInsertionAdapter.insertList(
        irchestList, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertDevice(IrChest device) async {
    await _irChestInsertionAdapter.insert(device, OnConflictStrategy.replace);
  }
}

class _$IrChestDeviceDao extends IrChestDeviceDao {
  _$IrChestDeviceDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database, changeListener),
        _irChestDeviceInsertionAdapter = InsertionAdapter(
            database,
            'IrChestDevice',
            (IrChestDevice item) => <String, Object?>{
                  'id': item.id,
                  'irDeviceId': item.irDeviceId,
                  'deviceName': item.deviceName,
                  'taskId': item.taskId,
                  'infraredDeviceTypeName': item.infraredDeviceTypeName,
                  'infraredDeviceTypeId': item.infraredDeviceTypeId,
                  'code': item.code,
                  'num': item.num,
                  'installationSite': item.installationSite,
                  'project': item.project,
                  'upperLimitValue': item.upperLimitValue,
                  'finish': item.finish == null ? null : (item.finish! ? 1 : 0),
                  'checkTemperature': item.checkTemperature,
                  'comments': item.comments,
                  'userId': item.userId,
                  'detectionTime': item.detectionTime,
                  'path': item.path
                },
            changeListener);

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<IrChestDevice> _irChestDeviceInsertionAdapter;

  @override
  Future<List<IrChestDevice>> findAllDevices(String userId) async {
    return _queryAdapter.queryList(
        'SELECT * FROM IrChestDevice WHERE userId = ?1',
        mapper: (Map<String, Object?> row) => IrChestDevice(
            row['id'] as String,
            row['irDeviceId'] as String,
            row['deviceName'] as String?,
            row['taskId'] as String?,
            row['infraredDeviceTypeId'] as String?,
            row['infraredDeviceTypeName'] as String?,
            row['code'] as String?,
            row['num'] as String?,
            row['installationSite'] as String?,
            row['project'] as String?,
            row['upperLimitValue'] as String?,
            row['finish'] == null ? null : (row['finish'] as int) != 0,
            row['checkTemperature'] as String,
            row['comments'] as String,
            row['userId'] as String,
            row['detectionTime'] as int,
            row['path'] as String),
        arguments: [userId]);
  }

  @override
  Future<List<IrChestDevice>> findAllDevicesByChest(
    String userId,
    String chestId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM IrChestDevice WHERE userId = ?1 AND infraredDeviceTypeId = ?2',
        mapper: (Map<String, Object?> row) => IrChestDevice(row['id'] as String, row['irDeviceId'] as String, row['deviceName'] as String?, row['taskId'] as String?, row['infraredDeviceTypeId'] as String?, row['infraredDeviceTypeName'] as String?, row['code'] as String?, row['num'] as String?, row['installationSite'] as String?, row['project'] as String?, row['upperLimitValue'] as String?, row['finish'] == null ? null : (row['finish'] as int) != 0, row['checkTemperature'] as String, row['comments'] as String, row['userId'] as String, row['detectionTime'] as int, row['path'] as String),
        arguments: [userId, chestId]);
  }

  @override
  Future<List<IrChestDevice>> findAllDevicesByTask(
    String userId,
    String taskId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM IrChestDevice WHERE userId = ?1 AND taskId = ?2',
        mapper: (Map<String, Object?> row) => IrChestDevice(
            row['id'] as String,
            row['irDeviceId'] as String,
            row['deviceName'] as String?,
            row['taskId'] as String?,
            row['infraredDeviceTypeId'] as String?,
            row['infraredDeviceTypeName'] as String?,
            row['code'] as String?,
            row['num'] as String?,
            row['installationSite'] as String?,
            row['project'] as String?,
            row['upperLimitValue'] as String?,
            row['finish'] == null ? null : (row['finish'] as int) != 0,
            row['checkTemperature'] as String,
            row['comments'] as String,
            row['userId'] as String,
            row['detectionTime'] as int,
            row['path'] as String),
        arguments: [userId, taskId]);
  }

  @override
  Future<List<IrChestDevice>> findAllDevicesByTaskAndChest(
    String userId,
    String taskId,
    String chestId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM IrChestDevice WHERE userId = ?1 AND taskId = ?2 AND infraredDeviceTypeId = ?3',
        mapper: (Map<String, Object?> row) => IrChestDevice(row['id'] as String, row['irDeviceId'] as String, row['deviceName'] as String?, row['taskId'] as String?, row['infraredDeviceTypeId'] as String?, row['infraredDeviceTypeName'] as String?, row['code'] as String?, row['num'] as String?, row['installationSite'] as String?, row['project'] as String?, row['upperLimitValue'] as String?, row['finish'] == null ? null : (row['finish'] as int) != 0, row['checkTemperature'] as String, row['comments'] as String, row['userId'] as String, row['detectionTime'] as int, row['path'] as String),
        arguments: [userId, taskId, chestId]);
  }

  @override
  Future<void> updateProgress(
    String userId,
    String deviceId,
    String temperature,
    String path,
    String comments,
    int detectionTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrChestDevice SET comments = ?5, checkTemperature = ?3, path = ?4, detectionTime = ?6 WHERE userId = ?1 AND irDeviceId = ?2',
        arguments: [
          userId,
          deviceId,
          temperature,
          path,
          comments,
          detectionTime
        ]);
  }

  @override
  Future<void> updateProgressByTaskAndChest(
    String userId,
    String deviceId,
    String taskId,
    String chestId,
    String temperature,
    String path,
    String comments,
    int detectionTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrChestDevice SET comments = ?7, checkTemperature = ?5, path = ?6, detectionTime = ?8 WHERE userId = ?1 AND irDeviceId = ?2 AND taskId = ?3 AND infraredDeviceTypeId = ?4',
        arguments: [
          userId,
          deviceId,
          taskId,
          chestId,
          temperature,
          path,
          comments,
          detectionTime
        ]);
  }

  @override
  Future<void> updateTemperatureAndPath(
    String userId,
    String deviceId,
    String temperature,
    String path,
    int detectionTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrChestDevice SET checkTemperature = ?3, path = ?4, detectionTime = ?5 WHERE userId = ?1 AND irDeviceId = ?2',
        arguments: [userId, deviceId, temperature, path, detectionTime]);
  }

  @override
  Future<void> updateDeviceComments(
    String userId,
    String deviceId,
    String comments,
    int detectionTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrChestDevice SET comments = ?3, detectionTime = ?4 WHERE userId = ?1 AND irDeviceId = ?2',
        arguments: [userId, deviceId, comments, detectionTime]);
  }

  @override
  Future<void> updateDeviceCommentsByTaskAndChest(
    String userId,
    String deviceId,
    String taskId,
    String chestId,
    String comments,
    int detectionTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrChestDevice SET comments = ?5, detectionTime = ?6 WHERE userId = ?1 AND irDeviceId = ?2 AND taskId = ?3 AND infraredDeviceTypeId = ?4',
        arguments: [
          userId,
          deviceId,
          taskId,
          chestId,
          comments,
          detectionTime
        ]);
  }

  @override
  Future<void> clear(String userId) async {
    await _queryAdapter.queryNoReturn(
        'delete FROM IrChestDevice WHERE userId = ?1',
        arguments: [userId]);
  }

  @override
  Future<List<IrChestDevice>> getDeviceById(
    String userId,
    String irDeviceId,
  ) async {
    return _queryAdapter.queryList(
        'SELECT * FROM IrChestDevice WHERE userId = ?1 AND irDeviceId = ?2',
        mapper: (Map<String, Object?> row) => IrChestDevice(
            row['id'] as String,
            row['irDeviceId'] as String,
            row['deviceName'] as String?,
            row['taskId'] as String?,
            row['infraredDeviceTypeId'] as String?,
            row['infraredDeviceTypeName'] as String?,
            row['code'] as String?,
            row['num'] as String?,
            row['installationSite'] as String?,
            row['project'] as String?,
            row['upperLimitValue'] as String?,
            row['finish'] == null ? null : (row['finish'] as int) != 0,
            row['checkTemperature'] as String,
            row['comments'] as String,
            row['userId'] as String,
            row['detectionTime'] as int,
            row['path'] as String),
        arguments: [userId, irDeviceId]);
  }

  @override
  Stream<List<IrChestDevice>> listenDeviceList(String userId) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM IrChestDevice WHERE userId = ?1',
        mapper: (Map<String, Object?> row) => IrChestDevice(
            row['id'] as String,
            row['irDeviceId'] as String,
            row['deviceName'] as String?,
            row['taskId'] as String?,
            row['infraredDeviceTypeId'] as String?,
            row['infraredDeviceTypeName'] as String?,
            row['code'] as String?,
            row['num'] as String?,
            row['installationSite'] as String?,
            row['project'] as String?,
            row['upperLimitValue'] as String?,
            row['finish'] == null ? null : (row['finish'] as int) != 0,
            row['checkTemperature'] as String,
            row['comments'] as String,
            row['userId'] as String,
            row['detectionTime'] as int,
            row['path'] as String),
        arguments: [userId],
        queryableName: 'IrChestDevice',
        isView: false);
  }

  @override
  Future<void> insertDevice(IrChestDevice device) async {
    await _irChestDeviceInsertionAdapter.insert(
        device, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertList(List<IrChestDevice> irchestDeviceList) async {
    await _irChestDeviceInsertionAdapter.insertList(
        irchestDeviceList, OnConflictStrategy.replace);
  }
}

class _$IrChestStateDao extends IrChestStateDao {
  _$IrChestStateDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database),
        _irChestStateInsertionAdapter = InsertionAdapter(
            database,
            'IrChestState',
            (IrChestState item) => <String, Object?>{
                  'userId': item.userId,
                  'startTimeStamp': item.startTimeStamp,
                  'endTimeStamp': item.endTimeStamp
                });

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<IrChestState> _irChestStateInsertionAdapter;

  @override
  Future<List<IrChestState>> findAllState(String userId) async {
    return _queryAdapter.queryList(
        'SELECT * FROM IrChestState WHERE userId = ?1 AND startTimeStamp != 0 OR endTimeStamp != 0',
        mapper: (Map<String, Object?> row) => IrChestState(row['userId'] as String, row['startTimeStamp'] as int, row['endTimeStamp'] as int),
        arguments: [userId]);
  }

  @override
  Future<List<IrChestState>> findCurrentState(String userId) async {
    return _queryAdapter.queryList(
        'SELECT * FROM IrChestState WHERE userId = ?1',
        mapper: (Map<String, Object?> row) => IrChestState(
            row['userId'] as String,
            row['startTimeStamp'] as int,
            row['endTimeStamp'] as int),
        arguments: [userId]);
  }

  @override
  Future<void> updateProgress(
    String userId,
    int startTimeStamp,
    int endTimeStamp,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrChestState SET startTimeStamp = ?2, endTimeStamp = ?3 WHERE userId = ?1',
        arguments: [userId, startTimeStamp, endTimeStamp]);
  }

  @override
  Future<void> clear(String userId) async {
    await _queryAdapter.queryNoReturn(
        'delete FROM IrChestState WHERE userId = ?1',
        arguments: [userId]);
  }

  @override
  Future<void> insertState(IrChestState device) async {
    await _irChestStateInsertionAdapter.insert(
        device, OnConflictStrategy.replace);
  }
}

class _$IrTaskDao extends IrTaskDao {
  _$IrTaskDao(
    this.database,
    this.changeListener,
  )   : _queryAdapter = QueryAdapter(database, changeListener),
        _irTaskInsertionAdapter = InsertionAdapter(
            database,
            'IrTask',
            (IrTask item) => <String, Object?>{
                  'taskId': item.taskId,
                  'inspector': item.inspector,
                  'inspectorId': item.inspectorId,
                  'departId': item.departId,
                  'departName': item.departName,
                  'type': item.type,
                  'status': item.status,
                  'userId': item.userId,
                  'startTime': item.startTime,
                  'endTime': item.endTime,
                  'deviceCount': item.deviceCount,
                  'checkedDeviceCount': item.checkedDeviceCount,
                  'hasUpLoad':
                      item.hasUpLoad == null ? null : (item.hasUpLoad! ? 1 : 0)
                },
            changeListener);

  final sqflite.DatabaseExecutor database;

  final StreamController<String> changeListener;

  final QueryAdapter _queryAdapter;

  final InsertionAdapter<IrTask> _irTaskInsertionAdapter;

  @override
  Future<List<IrTask>> findAllTasks(String userId) async {
    return _queryAdapter.queryList('SELECT * FROM IrTask WHERE userId = ?1',
        mapper: (Map<String, Object?> row) => IrTask(
            row['taskId'] as String,
            row['inspector'] as String?,
            row['inspectorId'] as String?,
            row['departId'] as String?,
            row['departName'] as String?,
            row['type'] as int?,
            row['status'] as int?,
            row['userId'] as String,
            row['startTime'] as int,
            row['endTime'] as int,
            row['deviceCount'] as int,
            row['checkedDeviceCount'] as int,
            row['hasUpLoad'] == null ? null : (row['hasUpLoad'] as int) != 0),
        arguments: [userId]);
  }

  @override
  Future<void> updateCheckedDeviceCount(
    String userId,
    String taskId,
    int checkedDeviceCount,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrTask SET checkedDeviceCount = ?3 WHERE userId = ?1 AND taskId = ?2',
        arguments: [userId, taskId, checkedDeviceCount]);
  }

  @override
  Future<void> startCheck(
    String userId,
    String taskId,
    int startTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrTask SET startTime = ?3 WHERE userId = ?1 AND taskId = ?2',
        arguments: [userId, taskId, startTime]);
  }

  @override
  Future<void> endCheck(
    String userId,
    String taskId,
    int endTime,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrTask SET endTime = ?3 WHERE userId = ?1 AND taskId = ?2',
        arguments: [userId, taskId, endTime]);
  }

  @override
  Future<void> updateCheckedCount(
    String userId,
    String taskId,
    int checkedDeviceCount,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrTask SET checkedDeviceCount = ?3 WHERE userId = ?1 AND taskId = ?2',
        arguments: [userId, taskId, checkedDeviceCount]);
  }

  @override
  Future<void> updateUploadStatus(
    String userId,
    String taskId,
    int hasUpLoad,
  ) async {
    await _queryAdapter.queryNoReturn(
        'UPDATE IrTask SET hasUpLoad = ?3 WHERE userId = ?1 AND taskId = ?2',
        arguments: [userId, taskId, hasUpLoad]);
  }

  @override
  Future<void> clear(String userId) async {
    await _queryAdapter.queryNoReturn('delete FROM IrTask WHERE userId = ?1',
        arguments: [userId]);
  }

  @override
  Stream<List<IrTask>> listenChestList(String userId) {
    return _queryAdapter.queryListStream(
        'SELECT * FROM IrTask WHERE userId = ?1',
        mapper: (Map<String, Object?> row) => IrTask(
            row['taskId'] as String,
            row['inspector'] as String?,
            row['inspectorId'] as String?,
            row['departId'] as String?,
            row['departName'] as String?,
            row['type'] as int?,
            row['status'] as int?,
            row['userId'] as String,
            row['startTime'] as int,
            row['endTime'] as int,
            row['deviceCount'] as int,
            row['checkedDeviceCount'] as int,
            row['hasUpLoad'] == null ? null : (row['hasUpLoad'] as int) != 0),
        arguments: [userId],
        queryableName: 'IrTask',
        isView: false);
  }

  @override
  Future<void> insertList(List<IrTask> taskList) async {
    await _irTaskInsertionAdapter.insertList(
        taskList, OnConflictStrategy.replace);
  }

  @override
  Future<void> insertDevice(IrTask task) async {
    await _irTaskInsertionAdapter.insert(task, OnConflictStrategy.replace);
  }
}
