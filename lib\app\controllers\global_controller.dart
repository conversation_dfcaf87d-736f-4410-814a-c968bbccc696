import 'dart:convert';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/logger.dart';
import "../model/login/loginModel.dart";
import "../utils/storage.dart";
import '../model/login/loginModel.dart';
class GlobalController extends GetxController {

  /**接口数据 */
  final Rx<LoginModel?> userInfo = Rx<LoginModel?>(null);
  /**socket 状态 */
  RxString socketStatus = "socket未连接".obs;
  RxInt socketType = 0.obs; // 0未连接 1连接中

  /**全局页面刷新，通过监听变量达到数据的更新*/
  RxInt checkRoom = 0.obs;
  RxInt patrolStart = 0.obs;  

  /**维护工单变量监听，通过监听变量变化查库更新视图 */
  RxInt worlOrderIndex = 0.obs; // 工单信息
  RxInt deviceIndex = 0.obs; // 设备信息
  RxInt premiseIndex = 0.obs; // 先提条件
  RxInt guaranteeIndex = 0.obs; // 安全保障
  RxInt toolIndex = 0.obs; // 工具及备件要求
  RxInt recallIndex = 0.obs; // 回退计划
  RxInt processIndex = 0.obs; // 操作流程
  RxInt signIndex = 0.obs; //签字模块的回显和更新

  /**工单审核人签字 */
  RxMap peSign = {}.obs;
  RxInt examineIndex = 0.obs; // 工单审核通过监听变化更新视图请求接口
  

  /**模拟数据 */
  final count = 0.obs;
  // 判断是否登录,用于判断路由守卫的重定向
  RxBool isLogin = false.obs;
  // 巡检数据表单模拟
  RxList formData = [
    {
      "name":"",
      "id":"1",
    },
    {
      "name":"",
      "id":"2",
    },
  ].obs;

  // 模拟维护工单中的执行人数据  
  RxList<RxMap<String,dynamic>> mockUphold = RxList<RxMap<String, dynamic>>([
    RxMap<String, dynamic>({'name': 'John', 'userSign': null}),
    RxMap<String, dynamic>({'name': 'Alice', 'userSign': null}),
  ]);


  @override
  void onInit() async {
    super.onInit();
    /**监听全局变化，查表更新视图 */
    ever(deviceIndex, (newValue) {
      print('Count changed to $newValue');
    });


    // 从本地存储中获取用户信息
    var data = await Storage.getData("userInfo");
    if(data != null){
      LoginModel initData = LoginModel.fromJson(data);
      setUserInfo(initData);
    }

  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  /**设置用户信息 */
  setUserInfo(data){
    userInfo.value = data;
    print("当前用户数据 ${jsonEncode(userInfo)}");
    update();
  }

  // 根据缓存的userinfo 查询当前登录用户的uid
  String? queryCurrentUserId() {
      return userInfo.value?.data?.eid;
  }


  // 更新登录状态
  setIsLogin(status){
    isLogin.value = status;
    update();
  }

  // 设置页面的标识变量
  setCheckRoom(value){
    checkRoom.value = value;
    update();
  }
  setPatrolStart(value){
    patrolStart.value = value;
    update();
  }

  // 工单数据项监听
  setDeviceIndex(newValue){
    deviceIndex.value = newValue;
    update();
  }
  // 前提条件数据更新
  setPremiseIndex(newValue){
    premiseIndex.value = newValue;
    update();
  }
  // 安全保障数据更新
  setGuaranteeIndex(newValue){
    guaranteeIndex.value = newValue;
    update();
  }
  // 工具及备件要求数据更新
  setToolIndex(newValue){
    toolIndex.value = newValue;
    update();
  }
  // 回退计划
  setRecallIndex(newValue){
    recallIndex.value = newValue;
    update();
  }
  // 操作流程
  setProcessIndex(newValue){
    processIndex.value = newValue;
    update();
  }
  // 签字回显
  setSignIndex(newValue){
    signIndex.value = newValue;
    update();
  }
  //工单信息
  setWorlOrderIndex(newValue){
    worlOrderIndex.value = newValue;
    update();
  }
  // 工单审核
  setExamineIndex(newValue){
    examineIndex.value = newValue;
    update();
  }




  // 设置表单数据
  setFormData(item,value){
    for(int i =0 ;i<formData.length;i++){
      if(formData[i]["id"] == item['id']){
        formData[i]['name'] = value;
        break;
      }
    }
    logger("打印巡检设备的表单值,数据响应式${item}-${value}");
    update();
  }

  // 更改签名数据
  setMockUphold(index,value){
    mockUphold[index]['userSign'] = value;
    update();
  }

  void increment() => count.value++;
}
