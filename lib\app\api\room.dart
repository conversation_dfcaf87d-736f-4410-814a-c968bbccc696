import 'dart:convert';

import '../utils/HttpsClient.dart';
import '../utils/storage.dart';
import 'env_config.dart';

class RoomApi {
  // static const String baseUrl = "http://*********:8884";// 开发
  // static const String baseUrl = "http://test.inspection.ddbes.com:8080";// 开发
  // static const String baseUrl = "http://*************:80/inspection"; // 内网
  
  late HttpsClient httpsClient;
  RoomApi(){
    httpsClient = HttpsClient();
  }
  // 检查更新版本
  getVersionApi() async {
    var response = await httpsClient.get("${Host.roomApi}/device/version/v1");
    return response;
  }

  // 获取全部房间信息
  getRoomApi() async {
    var userInfo = await Storage.getData('userInfo'); 
      var response = await httpsClient.get("${Host.roomApi}/room/v3/${userInfo['data']['floor']}/${userInfo['data']['companyName']}");
      return response;
    
  }
  
  // 获取全部设备信息
  getDeviceApi() async {
    var userInfo = await Storage.getData('userInfo'); 
    var response = await httpsClient.get("${Host.roomApi}/device/v3/${userInfo['data']['floor']}/${userInfo['data']['companyName']}");
    return response;
    
  }

  // 获取全部巡检设备数据
  getDeviceParams()async{
    var userInfo = await Storage.getData('userInfo');
    var response = await httpsClient.get("${Host.roomApi}/form/v2/${userInfo['data']['floor']}/${userInfo['data']['companyName']}");
    return response;
    
  }

  // 获取所有专业信息
  getAllSpeciality()async{
    var userInfo = await Storage.getData('userInfo');
    var response = await httpsClient.get("${Host.roomApi}/room/v4/${userInfo['data']['floor']}/${userInfo['data']['companyName']}");
    return response;
  }

  // 上传数据
  uploadData(data)async{
    var response = await httpsClient.post("${Host.roomApi}/inspection/v3",data);
    return response;
  }

  // login([queryParameters]) async {
  //   var response = await httpsClient.get("/inspection/api/httpGet");
  //   return response;
  // }
  


}