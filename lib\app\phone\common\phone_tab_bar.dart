

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';

class PhoneTabBar extends StatelessWidget {

  final String? title;
  final Widget? body;
  final List<String> tabs;

  const PhoneTabBar( {super.key,this.title,this.body,required this.tabs});

  @override
  Widget build(BuildContext context) {
    SystemUiOverlayStyle overlayStyle =  const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    );

    return WillPopScope(
        onWillPop: () async {
          return true;
        },
        child: DefaultTabController(
          length: tabs.length,
          child: Scaffold(
              appBar: AppBar(
                leadingWidth: 40,
                leading: InkWell(
                  onTap: (){
                    Get.back();
                  },
                  child: Container(
                    color: Colors.transparent,
                    padding: const EdgeInsets.only(left: 20 ,right: 10),
                    child: Image.asset(AssetsRes.PHONE_TOOL_BACK),
                  ),
                ),
                centerTitle: true,
                title: Text('$title' , style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16, color: Color(0xff1D2129)),),
                elevation: 0,
                backgroundColor: Colors.white,
                systemOverlayStyle: overlayStyle,
                bottom: TabBar(
                  dividerColor: Colors.transparent,
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicatorPadding: const EdgeInsets.all(10),
                  indicator: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: const Color(0xffe8edff)
                  ),
                  tabs: tabs.map((e) => Tab(text: e)).toList(),
                  labelColor: const Color(0xff4F70FD),
                  labelStyle: const TextStyle(
                      fontSize: 15 ,
                      color: Color(0xff4F70FD),
                  ),
                  unselectedLabelColor:  const Color(0xff4e5969),
                  unselectedLabelStyle:  const TextStyle(
                    fontSize: 15 ,
                    color: Color(0xff4e5969),
                  ),
                ),
              ), body: Container(
                  color: Color(0xfff2f3f5),
               child: body ?? Container(),
          )),
        ));
  }

}