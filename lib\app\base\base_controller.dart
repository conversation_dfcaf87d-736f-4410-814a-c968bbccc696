
import 'package:get/get.dart';

import '../controllers/global_controller.dart';

abstract class BaseGetController extends GetxController {

  GlobalController globalController = Get.find();

  String userId = '';

  @override
  void onInit() {
    super.onInit();
    userId = globalController.queryCurrentUserId() ?? '';
  }

  @override
  void onReady() {
    super.onReady();
  }
  @override
  void onClose() {
    super.onClose();
  }

}