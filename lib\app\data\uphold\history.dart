
class UpholdHistory{
  final String? id;
  final String deptId;
  final String? deptName;
  final String? deviceGroupId;
  final String? deviceGroupName;
  final int? type;
  final String? typeName;
  final String? installationSite;
  final String?  operator;
  final String?  supervision;
  final String?  pe;
  final String?  manufacturer;
  final String?  factory;
  final String? deviceFlowId;
  final String? deviceNum;
  final String? phone;
  final String? contactPhone;
  final String? planTime;
  final String? week;
  final String? maintenancePlanNum;


  const UpholdHistory({
    required this.id,
    required this.deptId,
    required this.deptName,
    required this.deviceGroupId,
    required this.deviceGroupName,
    required this.type,
    required this.typeName,
    required this.installationSite,
    required this.operator,
    required this.supervision,
    required this.pe,
    required this.manufacturer,
    required this.factory,
    required this.deviceFlowId,
    required this.deviceNum,
    required this.phone,
    required this.contactPhone,
    required this.planTime,
    required this.week,
    required this.maintenancePlanNum,
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      "id":id,
      "deptId":deptId,
      "deptName":deptName,
      "deviceGroupId":deviceGroupId,
      "deviceGroupName":deviceGroupName,
      "type":type,
      "typeName":typeName,
      "installationSite":installationSite,
      "operator":operator,
      "supervision":supervision,
      "pe":pe,
      "manufacturer":manufacturer,
      "factory":factory,
      "deviceFlowId":deviceFlowId,
      "deviceNum":deviceNum,
      "phone":phone,
      "contactPhone":contactPhone,
      "planTime":planTime,
      "week":week,
      "maintenancePlanNum":maintenancePlanNum,
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  // @override
  Map<String, dynamic> toJson() {
    return {
      "id":id,
      "deptId":deptId,
      "deptName":deptName,
      "deviceGroupId":deviceGroupId,
      "deviceGroupName":deviceGroupName,
      "type":type,
      "typeName":typeName,
      "installationSite":installationSite,
      "operator":operator,
      "supervision":supervision,
      "pe":pe,
      "manufacturer":manufacturer,
      "factory":factory,
      "deviceFlowId":deviceFlowId,
      "deviceNum":deviceNum,
      "phone":phone,
      "contactPhone":contactPhone,
      "planTime":planTime,
      "week":week,
      "maintenancePlanNum":maintenancePlanNum,
    };
  }
}