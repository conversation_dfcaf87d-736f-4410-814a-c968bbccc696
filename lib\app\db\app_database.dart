
import 'dart:async';

import 'package:floor/floor.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_dao.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_device.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_device_dao.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_state.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_state_dao.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir_task/ir_task.dart';
// import 'package:contractor/app/database/conversation/dao/*';
//
import 'package:sqflite/sqflite.dart' as sqflite;

import 'ir_task/ir_task_dao.dart';

part 'app_database.g.dart';   // build之前收到引入 part， 名字必须和原db名字一致

@Database(version: 3, entities: [IrChest , IrChestDevice , IrChestState , IrTask])
abstract class AppDataBase extends FloorDatabase {

  // 红外巡检相关
  IrChestDao get getChestDao;
  IrChestDeviceDao get getChestDeviceDao;
  IrChestStateDao get getChestStateDao;

  // 任务模式新添加
  IrTaskDao get getTaskDao;

}