import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:sjzx_patrol_system_mobile/app/data/uphold/user.dart';
import '../../../../api/upholdApi.dart';
import '../../../../controllers/global_controller.dart';
import "../../../../controllers/upholdSqflite_controller.dart";
import '../../../../data/uphold/history.dart';
import '../../../../utils/screenutil.dart';
import '../../../../model/workOrder/MaintainItem.dart';

class UpholdWorlOrderController extends GetxController {
  //TODO: Implement UpholdWorlOrderController
  UpholdSQLController upholdSQLController = Get.find();
  GlobalController globalController = Get.find();

  // 维护工单实例
  UpholdApi upholdApi = UpholdApi();

  // 创建变量用于出发控制器
  RxString titleName = "维护工单".obs;

  /**数据响应式 */
  RxList userRole = [].obs; //用户角色列表
  RxList workList = [].obs;
  RxList deviceList = [
    {"name": "全部", "id": '1'}
  ].obs; //专业列表
  RxList deviceNameList = ["全部"].obs; //专业列表

  TextEditingController textController = TextEditingController();
  RxString searchValue = ''.obs;
  RxString selectedOption = '1'.obs;
  RxString selectedNameOption = '全部'.obs;

  @override
  void onInit() {
    super.onInit();
    findWorkOrder();
    ever(globalController.worlOrderIndex, (newValue) => findWorkOrder());
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  // 查找 workOrder 表中所有数据
  findWorkOrder() async {
    // 先清空 workList 变量
    workList.value = [];
    var workData = [];
    var workOrderResult = await upholdSQLController.findWorkOrder("isUpload=0");
    final List<Map<String, dynamic>> workOrderData =
        workOrderResult.map((item) => item.toJson()).toList();
    for (var workItem in workOrderResult) {
      // 获取用户角色
      // handelRole(workItem.id);
      var userResult =
          await upholdSQLController.findUser("woid='${workItem.id}'");
      final List<Map<String, dynamic>> userJson =
          userResult.map((item) => item.toJson()).toList();
      List operatorList = [];
      String? supervisionName = "";
      String? peName = "";
      String? pePhone = "";
      String? supervisionId = "";
      for (var userItem in userResult) {
        if (userItem.role == 0) {
          operatorList.add(userItem.name);
        }
        if (userItem.role == 1) {
          supervisionName = userItem.name;
          supervisionId = userItem.uid;
        }
        if (userItem.role == 2) {
          peName = userItem.name;
          pePhone = userItem.phone;
        }
      }
      // DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(workItem.planTime as int);
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(1688540812030);
      String planTime = DateFormat('yyyy/MM').format(dateTime);
      var uid = globalController.userInfo.value!.data.eid;
      workData.add({
        "id": workItem.id,
        "maintenancePlanNum": workItem.maintenancePlanNum,
        "deptId": workItem.deptId,
        "deptName": workItem.deptName,
        "deviceGroupId": workItem.deviceGroupId,
        "deviceGroupName": workItem.deviceGroupName,
        "type": workItem.type,
        "typeName": workItem.typeName,
        "installationSite": workItem.installationSite,
        "model": workItem.model,
        "manufacturer": workItem.manufacturer,
        "factory": workItem.factory,
        "deviceFlowId": workItem.deviceFlowId,
        "deviceNum": workItem.deviceNum,
        "phone": workItem.phone,
        "contactPhone": workItem.contactPhone,
        "planTime": planTime,
        "isFinish": workItem.isFinish,
        "isUpload": workItem.isUpload,
        "isPreview": workItem.isPreview,
        "operator": operatorList.join("、"),
        "supervision": supervisionName,
        "pe": peName,
        "pePhone": pePhone,
        "finishDate": workItem.finishDate,
        "itemShow": uid == supervisionId ? 1 : 0,
        "week": workItem.week,
        "parentGroupId": workItem.parentGroupId,
        "parentGroupName": workItem.parentGroupName,
      });
    }
    workList.value = workData.reversed.toList();
    var parentGroupIdList = [];
    var parentGroupList = [];
    parentGroupList.add({"name": "全部", "id": 1});
    workList.value.forEach((item) => {
          if (!parentGroupIdList.contains(item['parentGroupId']))
            {
              // 如果ID不存在，将其添加到Set和新的列表中
              parentGroupIdList.add(item['parentGroupId']),
              parentGroupList.add({
                "name": item['parentGroupName'],
                "id": item['parentGroupId']
              })
            },
        });
    deviceList.value = parentGroupList
        .map((item) => {
              "name": item["name"].toString(),
              "id": item["id"].toString(),
            })
        .toList();
    //
    var nameList = ["全部"];
    workList.value.forEach((item) => {
          // print(item)
          nameList.add(item['deviceGroupName'])
        });

    List<String> stringList = nameList.map((item) => item.toString()).toList();
    deviceNameList.value = stringList.toSet().toList();
    // print(deviceNameList.value);
    update();
  }

  // 查找 workOrder 表中所有数据
  findWork() async {
    // 先清空 workList 变量
    workList.value = [];
    var workData = [];
    var query = 'SELECT * FROM workOrder WHERE isUpload=0';
    List<dynamic> args = [];
    if (selectedOption.value != '1') {
      query += ' AND parentGroupId LIKE ?';
      args.add('%${selectedOption.value}%');
    }
    if (selectedNameOption.value != '全部') {
      query += ' AND deviceGroupName LIKE ?';
      args.add('%${selectedNameOption.value}%');
    }
    if (searchValue.value != '') {
      query += ' AND deviceNum LIKE ?';
      args.add('%${searchValue.value}%');
    }
    query += ' ORDER BY isFinish DESC';
      print(query);
    var workOrderResult = await upholdSQLController.findWork(query, args);
    final List<Map<String, dynamic>> workOrderData =
        workOrderResult.map((item) => item.toJson()).toList();
    for (var workItem in workOrderResult) {
      // 获取用户角色
      // handelRole(workItem.id);
      var userResult =
          await upholdSQLController.findUser("woid='${workItem.id}'");
      final List<Map<String, dynamic>> userJson =
          userResult.map((item) => item.toJson()).toList();
      List operatorList = [];
      String? supervisionName = "";
      String? peName = "";
      String? pePhone = "";
      String? supervisionId = "";
      for (var userItem in userResult) {
        if (userItem.role == 0) {
          operatorList.add(userItem.name);
        }
        if (userItem.role == 1) {
          supervisionName = userItem.name;
          supervisionId = userItem.uid;
        }
        if (userItem.role == 2) {
          peName = userItem.name;
          pePhone = userItem.phone;
        }
      }
      // DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(workItem.planTime as int);
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(1688540812030);
      String planTime = DateFormat('yyyy/MM').format(dateTime);
      var uid = globalController.userInfo.value!.data.eid;
      workData.add({
        "id": workItem.id,
        "maintenancePlanNum": workItem.maintenancePlanNum,
        "deptId": workItem.deptId,
        "deptName": workItem.deptName,
        "deviceGroupId": workItem.deviceGroupId,
        "deviceGroupName": workItem.deviceGroupName,
        "type": workItem.type,
        "typeName": workItem.typeName,
        "installationSite": workItem.installationSite,
        "model": workItem.model,
        "manufacturer": workItem.manufacturer,
        "factory": workItem.factory,
        "deviceFlowId": workItem.deviceFlowId,
        "deviceNum": workItem.deviceNum,
        "phone": workItem.phone,
        "contactPhone": workItem.contactPhone,
        "planTime": planTime,
        "isFinish": workItem.isFinish,
        "isUpload": workItem.isUpload,
        "isPreview": workItem.isPreview,
        "operator": operatorList.join("、"),
        "supervision": supervisionName,
        "pe": peName,
        "pePhone": pePhone,
        "finishDate": workItem.finishDate,
        "itemShow": uid == supervisionId ? 1 : 0,
        "week": workItem.week,
      });
    }
    workList.value = workData.reversed.toList();
    update();
  }

  /**判断用户是执行人还是监督人 */
  // handelRole() async {
  //   var uid = globalController.userInfo.value!.data.eid;
  //   for (var item in workList) {
  //     var roleResult = await upholdSQLController
  //         .findUser("woid='${item['id']}' AND uid='${uid}'");
  //     userRole.value.add(roleResult[0].role);
  //   }
  //   print(userRole.value);
  //   update();
  // }

  /**时间格式化 */
  formatTime(timeStamp, {format = "yyyy-MM-dd HH:mm:ss"}) {
    if (timeStamp != null && timeStamp != "") {
      DateTime dateTime =
          DateTime.fromMillisecondsSinceEpoch(int.parse(timeStamp));
      String planTime = DateFormat(format).format(dateTime);
      return planTime;
    } else {
      return "正在执行";
    }
  }

  /**数据上传 */
  workSubmit(workItem) async {
    loadingFun();
    Map paramsData = {};
    paramsData['id'] = workItem["id"];
    var maintainItemResult =
        await upholdSQLController.findMaintainItem("woid='${workItem['id']}'");
    var maintainSignResult =
        await upholdSQLController.findStepTake("woid='${workItem['id']}'");
    // 处理设备信息
    var deviceInfoArr = [];
    // 处理先提条件
    var conditionArr = [];
    var conditionSign = [];
    // 处理安全保障
    var guaranteeArr = [];
    var guaranteeSign = [];
    // 处理工具及备件条件
    var instrumentArr = [];
    var instrumentSign = [];
    // 处理会回退计划
    var rollbackPlanArr = [];
    // 处理操作流程
    var flowArr = [];
    var flowSign = [];
    // 工单维护数据
    maintainItemResult.forEach((item) {
      print(item);
      switch (item.stepName) {
        case "设备信息":
          deviceInfoArr.add(item);
          break;
        case "先提条件":
          conditionArr.add(item);
          break;
        case "安全保障":
          // obj.entries.map((entry) => {print('123${entry.key}')});
          guaranteeArr.add(item);
          break;
        case "工具及备件要求":
          instrumentArr.add(item);
          break;
        case "回退计划":
          rollbackPlanArr.add(item);
          break;
        default:
          flowArr.add(item);
      }
    });
    // 工单签字数据
    maintainSignResult.forEach((signItem) {
      switch (signItem.stepName) {
        case "先提条件":
          // conditionSign.add(signItem);
          break;
        case "安全保障":
          // guaranteeSign.add(signItem);
          break;
        case "工具及备件要求":
          // instrumentSign.add(signItem);
          break;
        case "操作流程":
          flowSign.add(signItem);
          break;
      }
    });
    paramsData["deviceInfo"] = formatNoSign(deviceInfoArr);
    paramsData["rollbackPlan"] = formatNoSign(rollbackPlanArr);
    // paramsData["condition"] = formatSign(conditionArr, conditionSign);
    // paramsData["guarantee"] = formatSign(guaranteeArr, guaranteeSign);
    // paramsData["instrument"] = formatSign(instrumentArr, instrumentSign);
    paramsData["condition"] = formatSign(conditionArr, null);
    paramsData["guarantee"] = formatSign(guaranteeArr, null);
    paramsData["instrument"] = formatSign(instrumentArr, null);
    paramsData["flow"] = submitFormatFlow(flowArr, flowSign);
    var response = await upholdApi.workUploadData(paramsData);
    if (response.data['msg'] == "请求成功") {
      toastFun("上传成功");
      insertHistory(workItem);
      updateDB(workItem["id"]); // 更新本地库已上传的状态
      findWorkOrder(); // 重新查库更新视图
      Get.back();
    } else {
      toastFun("上传失败,${response.data['msg']}");
      Get.back();
    }
  }

  //格式化设备信息和回退计划数据
  formatNoSign(listData) {
    var formatNoSignData = {};
    formatNoSignData["id"] = listData[0].id;
    formatNoSignData["details"] = [];
    formatNoSignData["operateSignature"] = null;
    formatNoSignData["operateTime"] = 0;
    formatNoSignData["supervisorSignature"] = '';
    formatNoSignData["supervisorTime"] = 0;
    listData.forEach((item) => {
          formatNoSignData["details"].add({
            "content": item.content,
            "operation": item.operation,
            "operatorId": item.operatorId,
            "operatorName": item.operatorName,
            "operatorResult": item.operatorResult,
            "operatorTime": item.operatorTime,
            "supervisorId": item.supervisorId,
            "supervisorName": item.supervisorName,
            "supervisorResult": item.supervisorResult,
            "supervisorTime": item.supervisorTime,
            "fillingValue": item.fillingValue,
            "amount": item.amount,
            "gapPrice": item.gapPrice == null ? [] : json.decode(item.gapPrice),
            "gapPrice": item.gapPrice == null ? [] : json.decode(item.gapPrice),
            "gapPrice": item.gapPrice == null ? [] : json.decode(item.gapPrice),
          })
        });
    return formatNoSignData;
  }

  // 格式化签字数据
  formatSign(listData, listSign) {
    var formatSignData = {};
    formatSignData["id"] = listData[0].id;
    formatSignData["details"] = [];
    // 收集维护工单项
    listData.forEach((item) => {
          formatSignData["details"].add({
            "content": item.content,
            "operation": item.operation,
            "operatorId": item.operatorId,
            "operatorName": item.operatorName,
            "operatorResult": item.operatorResult,
            "operatorTime": item.operatorTime,
            "supervisorId": item.supervisorId,
            "supervisorName": item.supervisorName,
            "supervisorResult": item.supervisorResult,
            "supervisorTime": item.supervisorTime,
            "fillingValue": item.fillingValue,
            "amount": item.amount,
            "gapPrice": json.decode(item.gapPrice),
            "gapPicture": item.gapPicture != '' && item.gapPicture != null
                ? [item.gapPicture]
                : [],
            "fillingPictureValue": item.fillingPictureValue
          })
        });
    // 收集签字项
    if (listSign != null) {
      formatSignData["operateSignature"] = [];
      formatSignData["operateTime"] = listSign[0].operateTime;
      formatSignData["supervisorSignature"] =
          '${json.decode(listSign[0].supervisorSignature)}'; // 解析后的字符串dio传输中不会在包裹""
      formatSignData["supervisorTime"] =
          listSign[0].supervisorTime != '' ? listSign[0].supervisorTime : 0;
      // // 执行人签字
      Map operateSignatureData = {};
      var operateSignatureMap = json.decode(listSign[0].operateSignature);
      operateSignatureMap.forEach((signItem) {
        operateSignatureData[signItem["user"]['uid']] =
            '${json.decode(signItem['baseImg'])}';
      });
      formatSignData["operateSignature"].add(operateSignatureData);
    }
    return formatSignData;
  }

  // 操作流程数据工单数据和签字单独处理
  submitFormatFlow(listData, listSign) {
    // 将listData根据parentNam分成二级对象
    var resultList = submitFormatFlowTo(listData);
    // 收集工单项目转成二级对象对应接口格式
    var formatFlowData = {};
    formatFlowData["id"] = listData[0].id;
    formatFlowData["items"] = [];
    resultList.forEach((resultItem) {
      Map resultMap = {};
      resultMap["name"] = resultItem["parentName"];
      resultMap["id"] = resultItem["parentId"] ?? "";
      resultMap["details"] = [];
      resultItem["child"].forEach((childItem) {
        var childItemObj = {};
        childItemObj["content"] = childItem.content;
        childItemObj["operation"] = childItem.operation;
        childItemObj["operatorId"] = childItem.operatorId;
        childItemObj["operatorName"] = childItem.operatorName;
        childItemObj["operatorResult"] = childItem.operatorResult;
        childItemObj["operatorTime"] = childItem.operatorTime;
        childItemObj["supervisorId"] = childItem.supervisorId;
        childItemObj["supervisorName"] = childItem.supervisorName;
        childItemObj["supervisorResult"] = childItem.supervisorResult;
        childItemObj["supervisorTime"] = childItem.supervisorTime;
        childItemObj["fillingValue"] = childItem.fillingValue;
        childItemObj["amount"] = childItem.amount;
        childItemObj["gapPrice"] = json.decode(childItem.gapPrice);
        childItemObj["gapPicture"] =
            childItem.gapPicture != '' && childItem.gapPicture != null
                ? [childItem.gapPicture]
                : [];
        childItemObj["fillingPictureValue"] = childItem.fillingPictureValue;
        resultMap["details"].add(childItemObj);
      });
      formatFlowData["items"].add(resultMap);
    });

    // 收集工单项目的签字
    // 收集签字项
    formatFlowData["operateSignature"] = [];
    formatFlowData["operateTime"] = listSign[0].operateTime;
    formatFlowData["supervisorSignature"] =
        '${json.decode(listSign[0].supervisorSignature)}';
    formatFlowData["supervisorTime"] =
        listSign[0].supervisorTime != '' ? listSign[0].supervisorTime : 0;
    // // 执行人签字
    Map operateSignatureData = {};
    var operateSignatureMap = json.decode(listSign[0].operateSignature);
    operateSignatureMap.forEach((signItem) {
      operateSignatureData[signItem["user"]['uid']] =
          '${json.decode(signItem['baseImg'])}';
    });
    formatFlowData["operateSignature"].add(operateSignatureData);
    return formatFlowData;
  }

  // 插入到 history 表
  insertHistory(workItem) async {
    var userData = await upholdSQLController
        .findUser("woid='${workItem['id']}' AND role=0");
    var nameList = [];
    userData.forEach((userItem) {
      nameList.add(userItem.name);
    });
    List<UpholdHistory> workData = [
      UpholdHistory(
          id: workItem['id'],
          deptId: workItem['deptId'],
          deptName: workItem['deptName'],
          deviceGroupId: workItem['deviceGroupId'],
          deviceGroupName: workItem['deviceGroupName'],
          type: workItem['type'],
          typeName: workItem['typeName'],
          installationSite: workItem['installationSite'],
          operator: nameList.join('、'),
          supervision: workItem['supervision'],
          pe: workItem['pe'],
          manufacturer: workItem['manufacturer'],
          factory: workItem['factory'],
          deviceFlowId: workItem['deviceFlowId'],
          deviceNum: workItem['deviceNum'],
          phone: workItem['phone'],
          contactPhone: workItem['contactPhone'],
          planTime: workItem['planTime'],
          week: workItem['week'],
          maintenancePlanNum: workItem['maintenancePlanNum'])
    ];
    upholdSQLController.insertHistory(workData);
  }

  // 格式化二级对象
  submitFormatFlowTo(listData) {
    List<Map<String, dynamic>> resultList = [];
    listData.forEach((item) {
      String parentName = item.parentName;
      bool parentFound = false;
      // 查找是否已经存在该 parentName 的数据
      for (int i = 0; i < resultList.length; i++) {
        Map<String, dynamic> resultItem = resultList[i];
        if (resultItem['parentName'] == parentName) {
          resultItem['child'].add(item);
          parentFound = true;
          break;
        }
      }
      // 如果不存在该 parentName 的数据，则新建一组
      if (!parentFound) {
        Map<String, dynamic> newItem = {
          'parentName': parentName,
          'parentId': item.parentId,
          'child': [item],
        };
        resultList.add(newItem);
      }
    });
    return resultList;
  }

  // 上传成功后更改数据库状态
  updateDB(id) {
    upholdSQLController.updateTable("workOrder", {"isUpload": 1}, "id='${id}'");
    findWorkOrder();
  }

  // 预览工单
  previewWorl(workItem) async {
    var maintainResult =
        await upholdSQLController.findMaintainItem("woid='${workItem["id"]}'");
    final List<Map<String, dynamic>> maintainJson =
        maintainResult.map((form) => form.toJson()).toList();
    var newData = [];
    for (var resultItem in maintainResult) {
      bool isExist = false;
      for (var newItem in newData) {
        if (newItem['stepName'] == resultItem.stepName) {
          isExist = true;
          newItem['child'].add(resultItem);
          break;
        }
      }
      if (!isExist) {
        newData.add({
          'stepName': resultItem.stepName,
          'child': [resultItem]
        });
      }
    }
    var flowData;

    newData.forEach((item) {
      if (item['stepName'] == "操作流程") {
        print(item);
        //   print("操作流程 ,${item['stepName']},${item}");
        flowData = formatFlow(item);
      }
    });

    // 遍历格式化的数据,向数组中添加响应的渲染函数
    List previewWorlInner = [];
    newData.forEach((item) {
      previewWorlInner.add(previewWorl_title(item['stepName']));
      if (item["stepName"] != '操作流程') {
        item['child'].forEach((childItem) {
          if (item["stepName"] == "设备信息") {
            previewWorlInner.add(previewWorl_biserial(
                childItem.content, childItem.operation ?? ""));
          } else {
            previewWorlInner.add(previewWorl_singleRow(childItem.content));
          }
        });
      }
    });

    // 循环 flowData 渲染操作流程
    flowData['child'].forEach((flowItem) {
      previewWorlInner.add(previewWorl_title(flowItem['parentName']));
      flowItem['items'].forEach((flowChildItem) {
        previewWorlInner.add(previewWorl_singleRow(flowChildItem.content));
      });
    });

    // 渲染预览完成按钮
    previewWorlInner.add(previewEnd(workItem));
    previewWorlView(previewWorlInner);
  }

  /**格式化操作流程数据格式 */
  formatFlow(data) {
    Map<String, dynamic> formatflowData = {
      "stepName": data["stepName"],
      "child": [],
    };
    var groupByParent = {};
    print(data);
    for (var item in data['child']) {
      var parentName = item.parentName;
      if (groupByParent.containsKey(parentName)) {
        groupByParent[parentName]?.add(item);
      } else {
        groupByParent[parentName] = [item];
      }
    }
    for (var parentName in groupByParent.keys) {
      var items = groupByParent[parentName];
      var child = {"parentName": parentName, "items": items};
      formatflowData["child"].add(child);
    }
    return formatflowData;
  }

  /**预览弹窗 */
  // 预览工单
  previewWorlView(previewData) async {
    Get.dialog(AlertDialog(
      content: Container(
        padding: EdgeInsets.zero,
        width: MyScreenUtil.width(1080),
        height: MyScreenUtil.height(800),
        child: ListView(
          children: [
            Container(
                child: Column(
              children: previewData.cast<Widget>(),
            )),
          ],
        ),
      ),
    ));
  }

  previewWorl_title(title) {
    return Container(
        alignment: Alignment.center,
        width: MyScreenUtil.width(1080),
        padding: const EdgeInsets.all(10),
        margin: EdgeInsets.only(top: MyScreenUtil.width(10)),
        decoration: BoxDecoration(
            color: const Color.fromRGBO(249, 230, 232, 1),
            borderRadius: BorderRadius.circular(MyScreenUtil.radius(10))),
        child: Text(title,
            style: TextStyle(
                color: Color.fromRGBO(43, 51, 63, 1),
                fontSize: MyScreenUtil.fontSize(20))));
  }

  // 工单预览_双列布局
  previewWorl_biserial(title, doc) {
    return Container(
      padding: EdgeInsets.only(
          top: MyScreenUtil.height(16), bottom: MyScreenUtil.height(16)),
      decoration: const BoxDecoration(
          border: Border(
              bottom: BorderSide(
                  color: Color.fromRGBO(231, 231, 231, 1), width: 1.0))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.only(right: 10),
            child: Text(
              title,
              style: TextStyle(fontSize: MyScreenUtil.fontSize(20)),
            ),
          ),
          Expanded(
              flex: 1,
              child: Container(
                alignment: Alignment.centerRight,
                child: Text(
                  doc,
                  style: TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                ),
              ))
        ],
      ),
    );
  }

  // 巡检预览单列
  previewWorl_singleRow(doc) {
    return Container(
        width: MyScreenUtil.width(1080),
        padding: EdgeInsets.only(
            top: MyScreenUtil.height(16), bottom: MyScreenUtil.height(16)),
        decoration: const BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    color: Color.fromRGBO(231, 231, 231, 1), width: 1.0))),
        child: Text(
          doc,
          style: TextStyle(fontSize: MyScreenUtil.fontSize(20)),
        ));
  }

  // 预览完成
  previewEnd(workData) {
    return Row(
      children: [
        Expanded(
            flex: 1,
            child: Container(
                height: MyScreenUtil.height(60),
                margin: EdgeInsets.only(top: MyScreenUtil.height(20)),
                child: ElevatedButton(
                  onPressed: () {
                    upholdSQLController.updateTable("workOrder",
                        {"isPreview": 1}, "id='${workData['id']}'");
                    findWorkOrder();
                    Get.back();
                  },
                  style: ButtonStyle(backgroundColor:
                      MaterialStateProperty.resolveWith((states) {
                    return MyScreenUtil.ThemColor();
                  })),
                  child: Text(
                    "预览结束",
                    style: TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                  ),
                ))),
      ],
    );
  }

  // toast弹窗
  toastFun(content) {
    Fluttertoast.showToast(
        msg: content,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 3,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  /**体验优化 */
  loadingFun() {
    Get.dialog(
      barrierDismissible: false,
      const SpinKitFadingCircle(
        color: Colors.white,
        size: 50.0,
      ),
    );
  }
}
