import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import '../../../../controllers/global_controller.dart';
import "../../../../controllers/sqflite_controller.dart";
import "../../../../data/user.dart";
import '../../../../utils/screenutil.dart';

class PatrolEndTaskController extends GetxController {
  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();

  // 用户基本信息
  Rx<UserInfo> userInfo = Rx<UserInfo>(const UserInfo(
    eid:"",
    avatar:"",
    name:"",
    phone:"",
    passWorld:"",
    deptName:"",
    technicalPost:"",
    livingPlace:"",
    gender:0,
    occupation:"",
    roomTypeName:"",
    createTime:0,
    floor:"",
    shifts:"",
    roomType:"",
    isUpload:0,
    patrolStartTimeStamp:0,
    patrolEndTimeStamp:0,
    companyId:"",
    companyName:"",
    deviceMaintainPer:0,
    
  ));

  // 房间类型列表
  RxList roomTypeInfoList = [].obs;


  final count = 0.obs;
  // 巡检时间
  RxString currentTime = "".obs;

  @override
  void onInit()async{
    super.onInit();
    await findUserInfo();
    await findRoomTypeInfo();
    ever(globalController.patrolStart,(_)async{
      await findRoomData();
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  // 根据storage中的用户id 查询user表中的用户信息
  findUserInfo()async{
    var userInfoData = await sqfliteController.findUsers("eid='${globalController.userInfo.value!.data.eid}'");
    final List<Map<String, dynamic>> userInfolist = userInfoData.map((userInfoItem) => userInfoItem.toJson()).toList();
    userInfo.value = userInfoData[0];
    update();
  }

  // 查询房间类型数据
  findRoomTypeInfo()async{
    var roomTypeInfoData = await sqfliteController.findRoomTypeInfo("");
    final List<Map<String, dynamic>> roomTypeInfoDataList = roomTypeInfoData.map((roomInfoItem) => roomInfoItem.toJson()).toList();
    roomTypeInfoList.value = roomTypeInfoData;
    update();
  }

  // 查询房间类型下面内室的巡检进度
  findRoomData()async{
    var roomTypeInfoData = await sqfliteController.findRoomTypeInfo("");
    for(var roomTypeInfoItem in roomTypeInfoData){
      var roomData = await sqfliteController.findRoomData("type='${roomTypeInfoItem.roomType}'");
      final List<Map<String, dynamic>> roomDataList = roomData.map((item) => item.toJson()).toList();
      int accomplishCount = roomData.where((item) => item.isFinish == 1).length;
      var progress = (accomplishCount / roomData.length * 100).toStringAsFixed(0);
      sqfliteController.updateTable(
        "roomTypeInfo", 
        {"progress":progress}, 
        "roomType='${roomTypeInfoItem.roomType}'"
      );
    }
    await findRoomTypeInfo();
  }

  // 结束巡检
  endPatol()async{
    var isStart = await sqfliteController.findUsers("eid='${globalController.userInfo.value!.data.eid}'");
    if(isStart[0].patrolStartTime == null){
      Fluttertoast.showToast(
        msg: "检测到当前巡检未开始，无法结束",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0
      );
    }else{
      var roomTypeInfoData = await sqfliteController.findRoomTypeInfo("");
      var isFinish =  roomTypeInfoData.every((item) => item.progress == '100');
      await inspectFinishBtn(isFinish);
    }
  }

  // 结束巡检提示弹窗
  inspectFinishBtn(isFinish)async{
    var message;
    if(isFinish){
      message = "此次巡检全部完成,是否结束巡检";
    }else{
      message = "此次巡检未完成,是否结束巡检";
    }
    Get.dialog(
      barrierDismissible: false,
      AlertDialog(
        title:Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [
            Text("提示")
          ],
        ),
        content: Row(
          children: [
            Text(message)
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: (){
              DateTime date = DateTime.now();
              var month = date.month<10?"0${date.month}":"${date.month}";
              var day = date.day<10?"0${date.day}":"${date.day}";
              var hour = date.hour<10?"0${date.hour}":"${date.hour}";
              var minute = date.minute<10?"0${date.minute}":"${date.minute}";
              var second = date.second<10?"0${date.second}":"${date.second}";
              sqfliteController.updateUserTable(
                "user", 
                {
                  "patrolEndTime":"${date.year}-${month}-${day} ${hour}:${minute}:${second}",
                  "patrolEndTimeStamp":date.millisecondsSinceEpoch
                }, 
                "eid='${globalController.userInfo.value!.data.eid}'"
              );
              Get.back();
              Get.back();
            }, 
            child: const Text("确定"),
            style: ButtonStyle(
              backgroundColor:MaterialStateProperty.resolveWith((states){
                return MyScreenUtil.ThemColor();
              })
            )
          ),
          ElevatedButton(
            style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all<Color>(Color.fromRGBO(143,147,153,1)),
            ),
            onPressed: (){
              Get.back();
            }, 
            child: const Text("我在想想")
          ),
        ],
      )
    );
  }

  // 进入巡检时的校验
  toCheckRoom(item)async{
    var isStart = await sqfliteController.findUsers("eid='${globalController.userInfo.value!.data.eid}'");
    if(isStart[0].patrolStartTime == null){
      Fluttertoast.showToast(
        msg: "检测到当前巡检未开始，请前往首页开始巡检",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0
      );
    }else{
      Get.toNamed("/check-room",arguments:{"roomTypeName":item.roomTypeName,"roomType":item.roomType});
    }
  }

  void increment() => count.value++;

}
