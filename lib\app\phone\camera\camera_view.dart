import 'package:flutter/material.dart';
import 'package:flutter_ir_plugin/ir/surface_platform_plugin.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/camera/camera_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/common/phone_tool_bar.dart';

class CameraView extends StatelessWidget {

  final String? deviceId;

  const CameraView({super.key , this.deviceId});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CameraController>(builder: (_){
      return PhoneToolBar(
        title: _.getTitle(),
        body: Stack(
          children: [
            Container(
              child: SurfacePlatformPlugin().irPlatformView(),
            ),
            // Container(
            //   margin: EdgeInsets.all(10),
            //   alignment: Alignment.bottomRight,
            //   child: MaterialButton(onPressed: () => _.takePhoto()
            //     ,child: const Text('拍照',style: TextStyle(fontSize: 15,color: Colors.white))),
            // )
          ],
        ),
      );
    });
  }
}