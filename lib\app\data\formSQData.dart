

// 存放设备的所有巡检项
class FormSQData {
  final String name;
  final String deviceTypeId;
  final String inputType;
  final String outputType;
  final String inspectionName;
  final String? inspectionRangeBegin;
  final String? inspectionRangeEnd;
  String? userId;

  FormSQData({
    required this.name,
    required this.deviceTypeId,
    required this.inputType,
    required this.outputType,
    required this.inspectionName, 
    required this.inspectionRangeBegin, 
    required this.inspectionRangeEnd,
    this.userId
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      "name" : name,
      "deviceTypeId" : deviceTypeId,
      "inputType" : inputType,
      "outputType" : outputType,
      "inspectionName" : inspectionName,
      "inspectionRangeBegin" : inspectionRangeBegin,
      "inspectionRangeEnd" : inspectionRangeEnd,
      "userId" : userId
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  Map<String, dynamic> toJson() {
    return {
      "name" : name,
      "deviceTypeId" : deviceTypeId,
      "inputType" : inputType,
      "outputType" : outputType,
      "inspectionName" : inspectionName,
      "inspectionRangeBegin" : inspectionRangeBegin,
      "inspectionRangeEnd" : inspectionRangeEnd,
      "userId" : userId
    };
  }
}