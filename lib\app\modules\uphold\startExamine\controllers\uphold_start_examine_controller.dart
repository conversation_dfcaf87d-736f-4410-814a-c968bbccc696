import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../api/upholdApi.dart';
import '../../../../controllers/global_controller.dart';
import '../../../../utils/mySigature.dart';
import '../../../../utils/screenutil.dart';
import "../views/deviceInfo.dart";
import '../views/premise.dart';
import '../views/ensure.dart';
import '../views/toolPage.dart';
import '../views/recall.dart';
import '../views/process.dart';

class UpholdStartExamineController extends GetxController {
  //TODO: Implement UpholdStartExamineController
  GlobalController globalController = Get.find();
  
  // 维护工单实例
  UpholdApi upholdApi = UpholdApi();

  final count = 0.obs;

   //左侧内容区的映射
  RxMap componentArr = {
    0:DeviceInfo(), // 设备信息
    1:Premise(), // 前提条件
    2:Ensure(), // 安全保障
    3:ToolPage(), // 工具及备份条件
    4:Recall(), // 回退计划
    5:Process() // 操作流程
  }.obs;
  RxInt componentIndex = 0.obs;
  // 接口数据的映射
  RxMap workDataMapping = {
    0:"deviceInfo", // 设备信息
    1:"condition", // 前提条件
    2:"guarantee", // 安全保障
    3:"instrument", // 工具及备份条件
    4:"rollbackPlan", // 回退计划
    5:"flow" // 操作流程
  }.obs;

  // 该项审核的全部数据
  Map workData = {}.obs;
  RxString titleName = "".obs;
  Map deviceInfoData = {}.obs; // 设备信息
  Map conditionData = {}.obs; // 前提条件
  Map guaranteeData = {}.obs; // 安全保障
  Map instrumentData = {}.obs; // 工具及备件要求
  Map rollbackPlanData = {}.obs; // 工具及备件要求
  Map flowData = {}.obs; // 工具及备件要求

  RxList signDataList = [].obs; // 签字模块

  @override
  void onInit() {
    super.onInit();
    workData = Get.arguments['workData'];
    titleName.value = "${workData['deptName']}-${workData['deviceGroupName']}-${workData['typeName']}维护: ${workData["installationSite"]}审核";
    deviceInfoData = workData['deviceInfo'];
    conditionData = workData['condition'];
    guaranteeData = workData['guarantee'];
    instrumentData = workData['instrument'];
    rollbackPlanData = workData['rollbackPlan'];
    flowData = workData['flow'];
    print(flowData['items'][0]['details'][0]['gapPicture']);

  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }



  // 解析执行人
  parseOperator(operatorData){
    var operatorList = [].obs;
    operatorData.forEach((operatorItem){
      operatorList.add(operatorItem['name']);
    });
    return operatorList.join('、');
  } 
  /**时间格式化 */
  formatTime(timeStamp,{format="yyyy-MM-dd HH:mm:ss"}){
    if(timeStamp != null && timeStamp != "" && timeStamp != "null"){
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(int.parse(timeStamp));
      String planTime = DateFormat(format).format(dateTime);
      return planTime;
    }else{
      return "暂无";
    }
    
  } 
  /**收集签字模块 */
  formatSignFun(index){
    var signData = [];
    // 收集执行人
    var operatorData =  workData['operator'];
    var operateSignatureArr = workData[workDataMapping[index]]["operateSignature"];
    Map<String, String> operateSignatureMap = {};
    for (var item in operateSignatureArr) {
      String key = item.keys.first;
      String value = item.values.first;
      operateSignatureMap[key] = value;
    }
    // 如果存在 additionalProp3 则判断这个签字模块为空
    if(operateSignatureMap['additionalProp3'] == "string"){
      signDataList.value =[];
      return;
    }
    operatorData.forEach((item){
      signData.add({
        "role":"执行人",
        "name":item['name'],
        "date":workData[workDataMapping[index]]["operateTime"],
        "imageUrl":operateSignatureMap[item['uid']]
      });
    });
    // 收集监督人
    signData.add({
      "role":"监督人",
      "name":workData['supervision']['name'],
      "date":workData[workDataMapping[index]]["supervisorTime"],
      "imageUrl": workData[workDataMapping[index]]["supervisorSignature"]
    });
    print("000,${jsonEncode(signData)}");
    signDataList.value = signData;
    update();
  }

  /**上传审核 */
  uploadExamine()async{
    if(globalController.peSign['baseImage'] == null){
      toastFun("审核人需签字后确认完成");
      return;
    }
    var data = {
      "id":workData['id'],
      "signature":'${globalController.peSign['baseImage']}'
    };
    var response = await upholdApi.uploadExamine(data);
    if(response.data['msg'] == "请求成功"){
      toastFun("审核提交完成");
      globalController.peSign.value = {};
      DateTime d = DateTime.now();
      globalController.setExamineIndex(d.millisecondsSinceEpoch);
      Get.back();
    }else{
      toastFun("审核提交失败 错误信息${response.data['msg']}");
    }
  }



  // 下一步切换步骤模块
  handelComponentIndex(int key){
    componentIndex.value = key;
    if(key !=0 || key != 4){
      formatSignFun(key);
    }
    update();
  }

  // 电子签名回显
  signatureEcho(baseImage){
    // 需要将存储的字符串传承二进制流
    final Uint8List decodedData = base64Decode(baseImage);
    return decodedData;
  }

  // 电子签名弹窗
  signAlter(){
    Get.dialog(
      // barrierDismissible:false, // 禁止点击空白处退出
      AlertDialog(
        title:const Text("电子签名"), // 标题
        content:Container(
          width: MyScreenUtil.width(500),
          height: MyScreenUtil.height(500),
          child: MySigature(onClone: signAlterClone,isDB:false),
        ), // 签名区
      )
    );
  }
  // 关闭弹窗
  signAlterClone(){
    Get.back();
  }

  // toast弹窗
  toastFun(content){
    Fluttertoast.showToast(
      msg: content,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
      timeInSecForIosWeb: 3,
      backgroundColor: Colors.black,
      textColor: Colors.white,
      fontSize: 16.0
    );
  }
}
