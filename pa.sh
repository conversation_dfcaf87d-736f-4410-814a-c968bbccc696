#!/bin/bash
# 适用于3.13.9

# 初始化记录项目pwd
projectDir=`pwd`

# step1 git pull ,pub get
echo '1 pull 代码，拉取依赖'
git pull
#flutter pub get

echo '2 执行 build apk --release'
flutter build apk --release

echo  '读取版本号'
v=`grep version pubspec.yaml|cut -d'=' -f2`
vv=$(echo $v|tr -d 'version: ')
echo "版本号为 $vv"

# step3 复制release apk 文件到指定目录 并修改文件名
mkdir ${projectDir}/app_out/


if [ -f "${projectDir}/build/app/outputs/flutter-apk/app-release.apk" ]; then
  echo ""
else
  echo "文件不存在"
  exit
fi

time=$(date "+%Y-%m-%d-%H:%M:%S")
cp "${projectDir}/build/app/outputs/flutter-apk/app-release.apk" "${projectDir}/app_out/${time}_release_${vv}.apk"

echo "打包完成"
exit
