import 'dart:async';
import 'dart:ffi';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_ir_plugin/ir/surface_platform_plugin.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/base/base_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/db/db_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_device.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/string_extension.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/logger.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/string_util.dart';

import '../../utils/screenutil.dart';


/// ir 红外 设备管理页面
class IrDeviceListController extends BaseGetController {

  TextEditingController imageNameTextEditingController = TextEditingController();
  TextEditingController searchController = TextEditingController();
  StreamSubscription? deviceStream;
  List<IrChestDevice> uiDevices = [];

  String chestId = "";
  String taskId = "";

  String imagePathPrefix = ''; // 图片路径前缀


  List<DeviceTab> tabList = [
    DeviceTab(0 , '全部设备', true),
    DeviceTab(1 , '已检设备', false),
    DeviceTab(2 , '未检设备', false),
  ];

  bool isChangeText = false;

  @override
  void onInit() {
    super.onInit();
    chestId = Get.arguments['chestId'] ?? '';
    taskId = Get.arguments['taskId'] ?? '';
    fetchDeviceList();
  }

  resetScreenType(int value) {
    tabList.forEach((element) => element.selected = element.index == value);
    update();
  }

  List<IrChestDevice> getScreenDeviceList(int index) {
    String searchText = searchController.text;
    if(index == 0){
      return searchText.isNotEmpty? uiDevices.where((element)=> element.deviceName?.contains(searchText) == true).toList() :uiDevices;
    }else if(index == 1){
      return uiDevices.where((element) => searchText.isNotEmpty? element.isFinish() && element.deviceName?.contains(searchText) == true  : element.isFinish()).toList();
    }
    return uiDevices.where((element) => searchText.isNotEmpty? (!element.isFinish()) &&  element.deviceName?.contains(searchText) == true : !element.isFinish()).toList();
  }

  // static int lastCount = 0;
  // testAdd(){
  //   lastCount++;
  //   DBHelper.updateChestCheckedDeviceCount(userId, taskId, chestId, lastCount);
  //   DBHelper.updateCheckedDeviceCount(userId, taskId, lastCount);
  // }

  @override
  void onReady() {
    super.onReady();
    
    // 监听变化
    deviceStream = DBHelper.listenDeviceList(userId).listen((deviceList) {
      fetchDeviceList();
    });
    
  }
  @override
  void onClose(){
    super.onClose();
    deviceStream?.cancel();
  }

  String realFileName(String path) {
    if(StringUtil.isEmpty(path)) return '';
    var picName = path.split('/').lastOrNull ?? '';
    return picName;
  }

  String joinPath(String oldPath ,String fileName) {
    int lastSlashIndex = oldPath.lastIndexOf('/');
    String directoryPath = oldPath.substring(0, lastSlashIndex);
    String newPath = directoryPath + fileName;
    logger('新路径$newPath');
    return newPath;
  }

  fetchDeviceList() async {
    if(isChangeText){
      isChangeText = false;
      return;
    }
    uiDevices.clear();

    var deviceList = await DBHelper.findAllDevicesByTaskAndChest(userId ,taskId,chestId);
    uiDevices.addAll(deviceList);

    uiDevices.forEach((e) {
      e.textEditingController.text = e.comments;

      e.textEditingController.value = TextEditingValue(
          text: e.comments,
          selection: TextSelection.fromPosition(TextPosition(
              affinity: TextAffinity.downstream,
              offset: e.comments.length))
      );
    });

    update();
  }

  // 调用 native 拍照方法 todo
  void takePhoto(BuildContext context , VoidCallback showDialogF) async {
    try{
      var temperature = await SurfacePlatformPlugin.takePhoto();
    }catch(e){
      logger('takePhoto...$e');
    }
  }

  void updateCurrentTemperature(int temperature) {
    update();
  }


  updateComments(String deviceId ,String comments) async {
    if(StringUtil.isEmpty(comments)) return;
    var t = DateTime.now().millisecondsSinceEpoch;
    DBHelper.updateDeviceCommentsByTaskAndChest(userId, deviceId, taskId, chestId,comments, t);
  }

  /// 有温度和备注， 1 更新当前数据库， 2 更新进度
  // _updateIrDevice2Db() async {
  //   if(StringUtil.isEmpty(currentDevice?.checkTemperature) || StringUtil.isEmpty(currentDevice?.path)) return;
  //   var detectionTime = DateTime.now().millisecondsSinceEpoch;
  //   await DBHelper.updateIrDevice(userId ,currentDevice!.irDeviceId , currentDevice!.checkTemperature , currentDevice!.comments ?? '',
  //       currentDevice!.path,
  //       detectionTime);
  //   var chestDeviceList = await DBHelper.getIrDeviceList(userId, chestId);
  //   if(chestDeviceList.isNotEmpty){
  //     var finishCount = chestDeviceList.where((e) => !StringUtil.isEmpty(e.checkTemperature)).toList().length;
  //     if(finishCount == 0) return;
  //     var d = finishCount/chestDeviceList.length;
  //     var progress = _formatPercentage(d).toString();
  //
  //     DBHelper.updateIrChestProgress(userId, chestId, progress);
  //   }
  // }

  // 备注
  void onCommentChanged(IrChestDevice device ,String comments) async {
     device.comments = comments;
     isChangeText = true;
     // device.textEditingController.value = TextEditingValue(
     //   text: comments,
     //   selection: TextSelection.fromPosition(TextPosition(
     //       affinity: TextAffinity.downstream,
     //       offset: comments.length))
     // );
     updateComments(device.irDeviceId, comments);
  }

  var oldImagePath = '';
}

class DeviceTab {
  int index = 0;
  String label = '';
  bool selected = false;

  DeviceTab(this.index, this.label, this.selected);
}