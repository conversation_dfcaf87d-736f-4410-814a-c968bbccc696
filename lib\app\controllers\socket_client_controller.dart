import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'dart:io';

import 'package:sjzx_patrol_system_mobile/app/api/user.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';
import "./upholdMessage.dart";
import "./global_controller.dart";

class SocketClientController extends GetxController {
  GlobalController globalController = Get.find<GlobalController>();
  //TODO: Implement GlobalControllerController
  final count = 0.obs;

  late Socket? clientSocket = null; // 实例

  RxString sniff = "".obs; // 用于接受服务端的嗅探信息
  late UpholdMessage upholdMessage;
  
  @override
  void onInit() {
    super.onInit();
    upholdMessage = UpholdMessage();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }


  // 连接服务端的 socket 服务
  Future<void> connect(IP,clientPort) async {
    try {
      clientSocket = await Socket.connect(IP, clientPort);
      print("连接成功 ${IP}:${clientPort}");
      clientStatusAlter();
      // 向服务端发送嗅探消息
      var canaryContent = {"canary":"hello server"};
      var canaryJson =  jsonEncode(canaryContent);
      sendData(utf8.encode(canaryJson));

      clientSocket?.listen((data) async {
        // 因 Data自身是二进制流，需要转两次才可以获取到最终的流
        var str = utf8.decode(data);
        List<dynamic> jsonList = jsonDecode(str);
        List<int> intList = List<int>.from(jsonList.map((item) => item as int));
        Uint8List bytes = Uint8List.fromList(intList);
        var jsonMap = json.decode(utf8.decode(bytes));


        print('接受服务端信息: ${jsonMap}');
        // 判断如果是 hello user则回显到弹窗提示中，负责走中间层操作数据库和视图
        if(jsonMap['canary'] == "hello user"){
          sniff.value = jsonMap['canary'];
          globalController.socketStatus.value = "socket已连接";
          globalController.socketType.value = 1;
        }else{
          // 判断模块调用不同的方法将收到的信息更新本地库
          if(jsonMap['stepName'] == "设备信息"){
            await upholdMessage.deviceInfoFun(jsonMap);
          }
          if(jsonMap['stepName'] == "先提条件"){
            // if(jsonMap['tabelName']=="stepTake"){
            //   await upholdMessage.signFun(jsonMap);
            //   return;
            // }
            await upholdMessage.premiseFun(jsonMap);
          }
          if(jsonMap['stepName'] == "安全保障"){
            await upholdMessage.guaranteeFun(jsonMap);
          }
          if(jsonMap['stepName'] == "工具及备件要求"){
            await upholdMessage.toolFun(jsonMap);
          }
          if(jsonMap['stepName'] == "回退计划"){
            await upholdMessage.recallFun(jsonMap);
          }
          if(jsonMap['stepName'] == "操作流程"){
            await upholdMessage.processFun(jsonMap);
          }

        }
      }).onDone(() {
        Fluttertoast.showToast(
          msg: "检测到当前socket已断开请重新连接",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 5,
          backgroundColor: Colors.black,
          textColor: Colors.white,
          fontSize: 16.0
        );
        globalController.socketStatus.value = "socket未连接";
        globalController.socketType.value = 0;
        sniff.value = "";
        print("监听到服务端断了");
        clientSocket?.close();
      });
    } catch (e) {
      print(e.toString());
    }
  }

  // 发送消息
  Future<void> sendData(data) async {
    try {
      clientSocket?.write(data);
    } catch (e) {
      print(e.toString());
    }
  }

  // 清除连接实例
  Future<void> disconnect() async {
    try {
      await clientSocket?.close();
      clientSocket = null;
      globalController.socketStatus.value = "socket未连接";
      globalController.socketType.value = 0;
      sniff.value = "";
    } catch (e) {
      print(e.toString());
    }
  }

  // 连接成功提示
  clientStatusAlter(){
    Get.dialog(
      barrierDismissible:true,
      AlertDialog(
        content:Obx((){
          return Container(
            width: MyScreenUtil.width(400),
            height: MyScreenUtil.height(240),
            child: Column(
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  child:Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(right: MyScreenUtil.width(10)),
                        child: Icon(
                          Icons.error,
                          size: MyScreenUtil.fontSize(26),
                        ),
                      ),
                      Text(
                        "提示信息",
                        style:TextStyle(
                          fontSize: MyScreenUtil.fontSize(24),
                          fontWeight: FontWeight.w500
                        ),
                      )
                    ],
                  )
                ),
                Container(
                  margin: EdgeInsets.only(top: MyScreenUtil.height(20),bottom: MyScreenUtil.height(20)),
                  child:sniff.value != ""?Text(
                    "服务端连接成功,接收到服务端的嗅探信息",
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(20)
                    ),
                  ):Text(
                    "正在连接中...",
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(18)
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.centerRight,
                  child: ElevatedButton(
                    onPressed: (){
                      Get.back();
                    }, 
                    child:Text(
                      "关闭弹窗",
                      style: TextStyle(
                        fontSize: MyScreenUtil.fontSize(18)
                      ),
                    )
                  ),
                )
              ],
            ),
          );
        }),
      )

    );
  }

  void increment() => count.value++;
}
