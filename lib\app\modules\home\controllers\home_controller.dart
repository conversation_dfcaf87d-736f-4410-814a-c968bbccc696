import 'dart:io';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:intl/intl.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:open_filex/open_filex.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sjzx_patrol_system_mobile/app/api/env_config.dart';
import 'package:sjzx_patrol_system_mobile/app/db/db_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_state.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/toast.dart';
import 'package:sjzx_patrol_system_mobile/app/model/speciality/speciality.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/ir_detection/manager/ir_manager.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/ir_detection/model/ir_convert.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/ir_detection/widget/ir_dialog.dart';
import 'package:sjzx_patrol_system_mobile/app/repository/patrol_repository.dart';
import 'package:sjzx_patrol_system_mobile/app/retrofit/entity/ir_permission/IrDeviceListItem.dart';
import 'package:sjzx_patrol_system_mobile/app/retrofit/ir_datasource.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/loading_helper.dart';
import 'package:sjzx_patrol_system_mobile/main.dart';
import "dart:convert";
import '../../../api/room.dart';
import '../../../api/upholdApi.dart';
import '../../../api/user.dart';
import "../../../controllers/global_controller.dart";
import '../../../controllers/socket_client_controller.dart';
import '../../../controllers/socket_server_controller.dart';
import '../../../controllers/sqflite_controller.dart';
import '../../../controllers/upholdSqflite_controller.dart';
import '../../../data/device.dart';
import '../../../data/deviceForm.dart';
import '../../../data/formSQData.dart';
import '../../../data/room.dart';
import '../../../data/roomTypeInfo.dart';
import '../../../data/uphold/maintainData.dart';
import '../../../data/uphold/stepTakeData.dart';
import '../../../data/uphold/user.dart';
import '../../../data/uphold/workOrder.dart';
import "../../../data/user.dart";
import '../../../model/device/device.dart';
import '../../../model/login/loginModel.dart';
import '../../../model/room/roomModel.dart';
import '../../../utils/screenutil.dart';
import '../../../utils/storage.dart';

class HomeController extends GetxController {
  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();
  UpholdSQLController upholdSQLController = Get.find();
  SocketServerController socketServerController = Get.find(); // 服务端实例
  SocketClientController socketClientController = Get.find(); // 客户端实例

  PatrolRepository patrolRepository = PatrolRepository();

  /*接口初始化 */
  // 初始化房间的接口实例
  RoomApi roomApi = RoomApi();
  UserApi userApi = UserApi();
  // 维护工单实例
  UpholdApi upholdApi = UpholdApi();

  /*响应式数据 */
  // 用户基本信息
  Rx<UserInfo> userInfo = Rx<UserInfo>(const UserInfo(
      eid: "",
      avatar: "",
      name: "",
      phone: "",
      passWorld: "",
      deptName: "",
      technicalPost: "",
      livingPlace: "",
      gender: 0,
      occupation: "",
      roomTypeName: "",
      createTime: 0,
      floor: "",
      shifts: "",
      roomType: "",
      isUpload: 0,
      patrolStartTimeStamp: 0,
      patrolEndTimeStamp: 0,
      companyId: "",
      deviceMaintainPer: 0,
      companyName: ""));

  // 当前设备连接的 WIFI 名称
  RxString wifiName = "".obs;

  /**页面权限 */
  RxBool isInspection = false.obs;
  RxBool isWork = false.obs;

  // 监听再按一次退出APP
  var lastPopTime = DateTime.now().obs;

  // 监听版本号
  RxString version = "".obs;

  @override
  void onInit() async {
    super.onInit();
    // 监听网络变化
    await checkLocationPermission();
    final connectivity = Connectivity();
    connectivity.onConnectivityChanged.listen((event) async {
      if (event == ConnectivityResult.wifi) {
        await checkLocationPermission();
      }
    });
    // 延迟一秒后查找 user 表,不然会找不到库
    await Future.delayed(const Duration(seconds: 1));
    await findUserInfo();
    /**检查用户的权限 */
    getPurviewFun();

    // 获取应用版本号
    await checkAppUpgrade(showToast: false);

    _checkOldVersionUserId();

    _fetchIrPermission();

    // _fetchIrDeviceList();
  }

  // 检查老版本 覆盖安装新版本 没有UserId的问题
  _checkOldVersionUserId() async {
    print('_checkOldVersionUserId......');
    // loadingFun();
    var userId = globalController.queryCurrentUserId();
    patrolRepository.checkDeviceWithOutUserId("device", userId);
    patrolRepository.checkDeviceWithOutUserId("deviceForm", userId);
    patrolRepository.checkDeviceWithOutUserId("form", userId);
    patrolRepository.checkDeviceWithOutUserId("room", userId);
    patrolRepository.checkDeviceWithOutUserId("roomTypeInfo", userId);
    // Get.back();
  }


  checkAppUpgrade({bool? showToast}) async {
    // if(!EnvConfig.isProduct()) return;
    version.value = await getAppVersion();
    var localVersing = globalController.userInfo.value!.data.number;
    // 页面初始化对比版本号提示更新APP
    print(version.value);
    print(localVersing);
    if (version.value != localVersing) {
      downLoadAppAlter();
    }
  }

  @override
  void onReady() async {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  // 确认退出APP
  setLastPopTime() {
    lastPopTime.value = DateTime.now();
    update();
  }

  // 退出账号
  logout() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Container(
          width: MyScreenUtil.width(440),
          height: MyScreenUtil.height(208),
          padding:EdgeInsets.all(MyScreenUtil.width(16)),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    child: Row(
                      children: [
                        Container(
                          width: MyScreenUtil.width(5),
                          height: MyScreenUtil.height(20),
                          margin: const EdgeInsets.only(right: 13),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(14.0),
                            color: Color(0xFF5777FF),
                          ),
                        ),
                        Text(
                          "设置",
                          style: TextStyle(
                            color: Color(0xFF2F303A),
                            fontSize: MyScreenUtil.fontSize(18),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    child: InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Icon(
                        Icons.close,
                        size: MyScreenUtil.fontSize(20),
                        color: MyScreenUtil.ThemColor(),
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  InkWell(
                    onTap: () {
                      inspectUpDate(false);
                    },
                    child: Container(
                      margin: EdgeInsets.only(top: MyScreenUtil.height(30)),
                      padding: EdgeInsets.only(
                        left: MyScreenUtil.width(40),
                        right: MyScreenUtil.width(40),
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 1,
                          color: MyScreenUtil.FontColor(),
                        ),
                        borderRadius:
                        BorderRadius.circular(MyScreenUtil.width(8)),
                      ),
                      child: Text(
                        '重新下载',
                        style: TextStyle(
                          color: MyScreenUtil.FontColor(),
                          fontSize: MyScreenUtil.fontSize(16),
                        ),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      inspectUpDate(true);
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                        left: MyScreenUtil.width(40),
                        right: MyScreenUtil.width(40),
                      ),
                      margin: EdgeInsets.only(top: MyScreenUtil.height(30)),
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 1,
                          color: MyScreenUtil.FontColor(),
                        ),
                        borderRadius:
                        BorderRadius.circular(MyScreenUtil.width(8)),
                      ),
                      child: Text(
                        '检查更新',
                        style: TextStyle(
                          color: MyScreenUtil.FontColor(),
                          fontSize: MyScreenUtil.fontSize(16),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              InkWell(
                onTap: () {
                  Get.back();
                  // closeSocketFun(); // 退出时清空socket实例
                  Storage.removeData("userInfo");
                  Get.offNamed("/login");
                },
                child: Container(
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(top: MyScreenUtil.height(50)),
                  height: MyScreenUtil.height(40),
                  decoration: BoxDecoration(
                    color: Color(0xFFFF5757),
                    borderRadius: BorderRadius.circular(MyScreenUtil.width(8)),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: MyScreenUtil.width(24),
                        height: MyScreenUtil.height(24),
                        child: Image.asset("assets/images/icon/Power.png"),
                      ),
                      Text(
                        "退出登陆",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: MyScreenUtil.fontSize(18),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
    // Storage.removeData("userInfo");
    // Get.offNamed("/login");
  }

  // 退出账号时清空 scoket
  closeSocketFun() {
    if (socketClientController.clientSocket != null) {
      socketClientController.disconnect();
    }
    if (socketServerController.serverSocket != null) {
      socketServerController.closeSocket();
    }
  }

  // 根据storage中的用户id 查询user表中的用户信息
  findUserInfo() async {
    var userInfoData = await sqfliteController.findUsers(
        "eid='${globalController.userInfo.value!.data.eid}' OR phone='${globalController.userInfo.value!.data.phone}'");
    final List<Map<String, dynamic>> userInfolist =
    userInfoData.map((userInfoItem) => userInfoItem.toJson()).toList();
    userInfo.value = userInfoData[0];
    update();
  }

  // 更新数据清空所用表单,清空钱查询改用户是否上传数据
  resetAllData() async {
    var isUpdate = await sqfliteController.findUsers(
        "eid='${globalController.userInfo.value!.data.eid}' OR phone='${globalController.userInfo.value!.data.phone}'");
    var message = "";

    int updateStatus = 0;

    if (isUpdate[0].eid == "") {
      message = "检测到当前没有巡检任务,是否更新数据";
      updateStatus = 1;
    } else if (isUpdate[0].isUpload == 0 &&
        isUpdate[0].patrolStartTime != null) {
      message = "检测到当前数据未上传, 请完成此次巡检后再进行数据更新";
      updateStatus = -1;
    } else if (isUpdate[0].patrolStartTime == null) {
      message = "检测当前未开始巡检,确认更新数据吗?";
      updateStatus = 2;
    } else {
      message = "检测到当前数据已完成,确认更新数据吗?";
      updateStatus = 3;
    }
    Get.dialog(
        barrierDismissible: false,
        AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: const [Text("提示")],
          ),
          content: Row(
            children: [Text("${message}")],
          ),
          actions: [
            updateStatus == -1  ? Container(): ElevatedButton(
                onPressed: () async {
                  loadingFun();
                  /*清空user表中的巡检时间和上传状态 */
                  /*目前的立即更新只更新了用户下面的室内房间信息\设备信息\设备巡检项信息 */
                  await login();  // 用户及其巡检进度状态
                  await resetUserStatus();
                  await updateRoomData();
                  await updateDeviceData();
                  await updateDeviceParamsData();
                  await relevanceDeviceANDRoom();
                  await getPurviewFun();
                  await getSpeciality();


                  Get.back();
                  Get.back();
                  Fluttertoast.showToast(
                      msg: "数据更新完成",
                      toastLength: Toast.LENGTH_SHORT,
                      gravity: ToastGravity.BOTTOM,
                      timeInSecForIosWeb: 1,
                      backgroundColor: Colors.black,
                      textColor: Colors.white,
                      fontSize: 16.0);
                },
                child: const Text("立即更新"),
                style: ButtonStyle(backgroundColor:
                MaterialStateProperty.resolveWith((states) {
                  return MyScreenUtil.ThemColor();
                }))),
            ElevatedButton(
              onPressed: () {
                Get.back();
              },
              child: const Text("稍后更新"),
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all<Color>(
                    Color.fromRGBO(143, 147, 153, 1)),
              ),
            ),
          ],
        ));
  }

  // 更新全部数据
  login() async {
    var response = await userApi.login({
      "user": globalController.userInfo.value!.data.phone,
      "password": globalController.userInfo.value!.data.passWorld
    });
    response.data["data"]["passWorld"] =
        globalController.userInfo.value!.data.passWorld;
    LoginModel res = LoginModel.fromJson(response.data);
    await resetUserData(res.data);
    await resetRoomTypeInfo(res.data.roomTypeInfoList);
  }

  /**插入用户信息到数据库 */
  resetUserData(userInfo) async {
    List<UserInfo> userList = [];
    userList.add(UserInfo(
        eid: userInfo.eid ?? "",
        name: userInfo.name,
        phone: userInfo.phone,
        passWorld: globalController.userInfo.value!.data.passWorld,
        deptName: userInfo.deptName,
        technicalPost: userInfo.deptPost,
        gender: userInfo.gender,
        occupation: userInfo.occupation,
        roomTypeName: userInfo.roomTypeName ?? "",
        createTime: userInfo.createTime ?? 0,
        floor: userInfo.floor ?? "",
        shifts: userInfo.shifts ?? "",
        roomType: userInfo.roomType ?? "",
        livingPlace: userInfo.livingPlace ?? "",
        avatar: "",
        isUpload: 0,
        patrolStartTimeStamp: 0,
        patrolEndTimeStamp: 0,
        companyId: userInfo.companyId ?? "",
        deviceMaintainPer:userInfo.deviceMaintainPer,
        companyName: userInfo.companyName ?? ""));
    sqfliteController.insertUsers(userList);
  }

  // 插入房间类型数据到数据库
  resetRoomTypeInfo(roomTypeInfo) async {
    bool exist = await sqfliteController.isExistData('roomTypeInfo', globalController.queryCurrentUserId() ?? '');
    if(exist){
      await sqfliteController.clearTableByUserId("roomTypeInfo" , globalController.queryCurrentUserId());
    }

    List<RoomTypeInfo> roomTypeInfoList = [];
    roomTypeInfo.forEach((item) {
      roomTypeInfoList.add(RoomTypeInfo(
        roomType: item.roomType,
        roomTypeName: item.roomTypeName,
        roomCount: item.roomCount,
        deviceCount: item.deviceCount,
        progress: "0",
      ));
    });
    sqfliteController.insertRoomTypeInfo(roomTypeInfoList , userId: globalController.userInfo.value?.data?.eid);
  }

  /**拉取数据 */
  // 将用户巡检和上传归零
  resetUserStatus() async {
    sqfliteController.updateUserTable(
        "user",
        {
          "isUpload": 0,
          "patrolStartTime": null,
          "patrolEndTime": null,
          "patrolStartTimeStamp": 0,
          "patrolEndTimeStamp": 0,
        },
        "eid='${globalController.userInfo.value!.data.eid}'");
  }

  // 获取更新房间数据
  updateRoomData() async {
    var result  = await sqfliteController.isExistData('room' , globalController.queryCurrentUserId() ?? '');
    if(result){
      return;
      // await sqfliteController.clearTable("room");
    }
    var response = await roomApi.getRoomApi();
    RoomModule res = RoomModule.fromJson(response.data);
    // 将数据存入room表中
    List<Room> initData = [];
    res.data.forEach((item) {
      item.rooms.forEach((roomsItem) {
        initData.add(Room(
          roomId: roomsItem.roomId,
          name: roomsItem.name,
          storey: int.parse(roomsItem.roomId[2]),
          deviceCount: roomsItem.deviceCount,
          note: roomsItem.note,
          type: roomsItem.type,
          isFinish: 0,
          isTaste: null,
          isSound: null,
          goTime:null,
        ));
      });
    });

    var userId = globalController.userInfo.value!.data.eid;
    sqfliteController.insertRooms(initData , userId: userId);


  }

  // 获取更新的设备信息
  updateDeviceData() async {
    var result = await sqfliteController.isExistData('device' , globalController.queryCurrentUserId() ?? '');
    if (result) {
      await sqfliteController.clearTableByUserId("device" , globalController.queryCurrentUserId() ?? '');
    }
    var response = await roomApi.getDeviceApi();
    DeviceModule res = DeviceModule.fromJson(response.data);
    // 将数据更新到 device 表中
    List<Device> DeviceInitData = [];
    res.data.forEach((item) {
      item.devices.forEach((devicesItem) {
        DeviceInitData.add(Device(
            deviceId: devicesItem.deviceId,
            formId:
            "${devicesItem.deviceId}${devicesItem.roomId}${devicesItem.roomType}",
            deviceTypeName: devicesItem.deviceTypeName,
            deviceType: devicesItem.deviceType,
            deviceCode: devicesItem.deviceCode,
            roomId: devicesItem.roomId,
            roomType: devicesItem.roomType,
            isFinish: 0,
            isOpen: 1,
            userId: globalController.queryCurrentUserId()
        ));
      });
    });
    sqfliteController.insertDevices(DeviceInitData);
  }

  // 获取更新设备的参数信息
  updateDeviceParamsData() async {
    var result = await sqfliteController.isExistData('form' , globalController.queryCurrentUserId());
    if (result) {
      await sqfliteController.clearTableByUserId("form" , globalController.queryCurrentUserId());
    }
    var response = await roomApi.getDeviceParams();
    List<FormSQData> formInitData = [];
    response.data["data"].forEach((key, value) {
      value.forEach((item) {
        item['inspectionData'].forEach((inspectionItem) {
          // print(inspectionItem["rangeEnd"]);
          formInitData.add(FormSQData(
            name: item['name'],
            deviceTypeId: item["deviceTypeId"],
            inputType: "",
            outputType: "",
            inspectionName: inspectionItem["name"],
            inspectionRangeBegin: inspectionItem["rangeBegin"] is String
                ? inspectionItem["rangeBegin"]
                : inspectionItem["rangeBegin"].toString(),
            inspectionRangeEnd: inspectionItem["rangeEnd"] is String
                ? inspectionItem["rangeEnd"]
                : inspectionItem["rangeEnd"].toString(),
          ));
        });
      });
    });
    sqfliteController.insertForms(formInitData);
    // print("更新设备参数数据 ${response.data["data"]}");
  }

  // 关联设备和巡检项
  relevanceDeviceANDRoom() async {
    var result = await sqfliteController.isExistData('deviceForm' , globalController.queryCurrentUserId());
    if (result) {
      await sqfliteController.clearTableByUserId("deviceForm" , globalController.queryCurrentUserId());
    }
    var deviceData = await sqfliteController.findDevices("");
    final List<Map<String, dynamic>> deviceDataList =
    deviceData.map((device) => device.toJson()).toList();
    List<DeviceForm> DeviceFormList = [];
    // 遍历每一个设备的数据根据 deviceType 查找响应的巡检项
    for (var item in deviceData) {
      var formDataList = await sqfliteController
          .findForms("deviceTypeId = '${item.deviceType}'");
      final List<Map<String, dynamic>> formData =
      formDataList.map((form) => form.toJson()).toList();
      // 得到巡检项后循环巡检项放到DeviceFormList的数组中
      for (var formItem in formDataList) {
        DeviceFormList.add(DeviceForm(
            name: formItem.name,
            formId: "${item.deviceId}${item.roomId}${item.roomType}",
            deviceTypeId: formItem.deviceTypeId,
            inputType: formItem.inputType,
            outputType: formItem.outputType,
            inspectionName: formItem.inspectionName,
            inspectionRangeBegin: formItem.inspectionRangeBegin,
            inspectionRangeEnd: formItem.inspectionRangeEnd,
            inputValue: null,
            inputActive: null));
      };
    };
    sqfliteController.insertDeviceForm(DeviceFormList);
  }

  /**工单维护 */
  updateUphold() async {
    var noUpload =
    await upholdSQLController.findWorkOrder("isFinish=1 AND isUpload=0");
    var executing = await upholdSQLController.findWorkOrder("isFinish=1");
    var message = "";
    if (noUpload.isNotEmpty) {
      message = "检测到存在未上传的工单,数据更新后会清除所有本地数据";
    } else if (executing.isNotEmpty) {
      message = "检测到存在进行中的工单,数据更新后会清除所有本地数据";
    } else {
      message = "数据更新后会清除所有本地数据";
    }
    Get.dialog(
        barrierDismissible: false,
        AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: const [Text("提示")],
          ),
          content: Row(
            children: [Text("${message}")],
          ),
          actions: [
            ElevatedButton(
                onPressed: () async {
                  loadingFun();
                  // await upholdSQLController.clearTable('workOrder');
                  // await upholdSQLController.clearTable('user');
                  // await upholdSQLController.clearTable('stepTake');
                  // await upholdSQLController.clearTable('maintainItem');
                  getUpholdData();
                },
                child: const Text("立即更新"),
                style: ButtonStyle(backgroundColor:
                MaterialStateProperty.resolveWith((states) {
                  return MyScreenUtil.ThemColor();
                }))),
            ElevatedButton(
              onPressed: () {
                Get.back();
              },
              child: const Text("我在想想"),
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all<Color>(
                    Color.fromRGBO(143, 147, 153, 1)),
              ),
            ),
          ],
        ));
  }

  // 获取维护工单数据
  getUpholdData() async {
    //清除相关数据
    // print(globalController.userInfo.value!.data.eid);
    var workOrderResult = await upholdSQLController.findWorkOrder("userId='${globalController.userInfo.value!.data.eid}'");
    print(jsonEncode(workOrderResult));
    for (var item in workOrderResult){
      await upholdSQLController.clearTableByOrderId('user',item.id);
      await upholdSQLController.clearTableByOrderId('stepTake',item.id);
      await upholdSQLController.clearTableByOrderId('maintainItem',item.id);
      await upholdSQLController.clearTableByUserId('workOrder',globalController.userInfo.value!.data.eid);
    }

    var response = await upholdApi.getAllData(
        globalController.userInfo.value!.data.eid, 0);
    await insertWorkOrder(response.data,globalController.userInfo.value!.data.eid);
    await insertStepTake(response.data);
    Get.back();
    Get.back();
  }

  // workOrder & user 表中插入工单数据和用户数据
  insertWorkOrder(workOrderData,userId) async {
    var result = await upholdSQLController.isExist('user');
    var workOrderResult = await upholdSQLController.isExist('workOrder');
    // if (result) {
    //   await upholdSQLController.clearTable("user");
    // }
    // if (workOrderResult) {
    //   await upholdSQLController.clearTable("workOrder");
    // }
    var data = workOrderData["data"];
    // workOrder的数据格式
    List<WorkOrder> workOrderList = [];
    // user 的数据格式
    List<User> userList = [];
    data.forEach((item) {
      // workOrder格式处理
      var workOrderItem = WorkOrder(
          id: item["id"],
          maintenancePlanNum:item['maintenancePlanNum'],
          deptId: item["deptId"],
          deptName: item["deptName"],
          deviceGroupId: item["deviceGroupId"],
          deviceGroupName: item["devicePlanName"],
          type: item["type"],
          typeName: item["typeName"],
          installationSite: item["installationSite"],
          model: item["model"],
          manufacturer: item["manufacturer"],
          deviceNum: item["deviceNum"],
          phone: item["phone"],
          contactPhone: item["contactPhone"],
          factory: item["factory"],
          deviceFlowId: item["deviceFlowId"],
          planTime: item["planTime"],
          week:item['week'],
          parentGroupId:item['parentGroupId'],
          parentGroupName:item['parentGroupName'],
          isFinish: 0,
          isUpload: 0,
          isPreview: 0,
          finishStep: 0,
          finishDate: 0);
      workOrderList.add(workOrderItem);
      // user 执行人格式处理
      item["operator"].forEach((operatorItem) {
        userList.add(User(
          woid: item["id"],
          uid: operatorItem["uid"],
          name: operatorItem["name"],
          gender: operatorItem["gender"],
          phone: operatorItem["phone"],
          status: operatorItem["status"],
          role: 0,
        ));
      });
      // user 监督人格式处理
      userList.add(User(
        woid: item["id"],
        uid: item["supervision"]["uid"],
        name: item["supervision"]["name"],
        gender: item["supervision"]["gender"],
        phone: item["supervision"]["phone"],
        status: item["supervision"]["status"],
        role: 1,
      ));
      // user 审核人格式处理
      userList.add(User(
        woid: item["id"],
        uid: item["pe"]["uid"],
        name: item["pe"]["name"],
        gender: item["pe"]["gender"],
        phone: item["pe"]["phone"],
        status: item["pe"]["status"],
        role: 2,
      ));
    });
    // 插入到 workOrder
    upholdSQLController.insertWorkOrder(workOrderList,userId);
    // 插入到 user
    upholdSQLController.insertUser(userList);
  }

  // stepTake 表中插入数据
  insertStepTake(response) async {
    var stepTakeResult = await upholdSQLController.isExist('stepTake');
    var maintainItemResult = await upholdSQLController.isExist('maintainItem');
    // if (stepTakeResult) {
    //   await upholdSQLController.clearTable("stepTake");
    // }
    // if (maintainItemResult) {
    //   await upholdSQLController.clearTable("maintainItem");
    // }

    var res = response["data"];
    List<StepTakeData> stepTakeList = [];
    List<MaintainData> maintainList = [];
    res.forEach((item) {
      stepTakeList.add(StepTakeData(
        woid: item["id"],
        stepName: "设备信息",
        stepField: "deviceInfo",
        id: item["deviceInfo"]["id"],
        operateTime: item["deviceInfo"]["operateTime"],
        operateSignature: "",
        supervisorTime: item["deviceInfo"]["supervisorTime"],
        supervisorSignature: null,
      ));
      stepTakeList.add(StepTakeData(
        woid: item["id"],
        stepName: "先提条件",
        stepField: "condition",
        id: item["condition"]["id"],
        operateTime: item["condition"]["operateTime"],
        operateSignature: "",
        supervisorTime: item["condition"]["supervisorTime"],
        supervisorSignature: null,
      ));
      stepTakeList.add(StepTakeData(
        woid: item["id"],
        stepName: "安全保障",
        stepField: "guarantee",
        id: item["guarantee"]["id"],
        operateTime: item["guarantee"]["operateTime"],
        operateSignature: "",
        supervisorTime: item["guarantee"]["supervisorTime"],
        supervisorSignature: null,
      ));
      stepTakeList.add(StepTakeData(
        woid: item["id"],
        stepName: "工具及备件要求",
        stepField: "guarantee",
        id: item["instrument"]["id"],
        operateTime: item["instrument"]["operateTime"],
        operateSignature: "",
        supervisorTime: item["instrument"]["supervisorTime"],
        supervisorSignature: null,
      ));
      stepTakeList.add(StepTakeData(
        woid: item["id"],
        stepName: "回退计划",
        stepField: "rollbackPlan",
        id: item["rollbackPlan"]["id"],
        operateTime: item["rollbackPlan"]["operateTime"],
        operateSignature: "",
        supervisorTime: item["rollbackPlan"]["supervisorTime"],
        supervisorSignature: null,
      ));
      stepTakeList.add(StepTakeData(
        woid: item["id"],
        stepName: "操作流程",
        stepField: "flow",
        id: item["flow"]["id"],
        operateTime: item["flow"]["operateTime"],
        operateSignature: "",
        supervisorTime: item["flow"]["supervisorTime"],
        supervisorSignature: null,
      ));

      // 整理维护项目数据
      item["deviceInfo"]["details"]?.forEach((deviceInfoItem) {
        maintainList.add(MaintainData(
          woid: item["id"],
          stepName: "设备信息",
          id: item["deviceInfo"]["id"],
          content: deviceInfoItem["content"],
          operation: deviceInfoItem["operation"],
          operatorId: deviceInfoItem["operatorId"],
          operatorName: deviceInfoItem["operatorName"],
          operatorResult: deviceInfoItem["operatorResult"],
          operatorTime: deviceInfoItem["operatorTime"] is String
              ? 0
              : deviceInfoItem["operatorTime"],
          supervisorId: deviceInfoItem["supervisorId"],
          supervisorName: deviceInfoItem["supervisorName"],
          supervisorResult: deviceInfoItem["supervisorResult"],
          supervisorTime: deviceInfoItem["supervisorTime"] is String
              ? 0
              : deviceInfoItem["supervisorTime"],
          status: deviceInfoItem["supervisorResult"],
        ));
      });
      item["condition"]["details"].forEach((conditionItem) {
        var init_gapPrice =
        List.generate(conditionItem["amount"] ?? 0, (index) => "");
        maintainList.add(MaintainData(
          woid: item["id"],
          stepName: "先提条件",
          id: item["condition"]["id"],
          content: conditionItem["content"],
          operation: conditionItem["operation"],
          operatorId: conditionItem["operatorId"],
          operatorName: conditionItem["operatorName"],
          operatorResult: conditionItem["operatorResult"],
          operatorTime: conditionItem["operatorTime"] is String
              ? 0
              : conditionItem["operatorTime"],
          supervisorId: conditionItem["supervisorId"],
          supervisorName: conditionItem["supervisorName"],
          supervisorResult: conditionItem["supervisorResult"],
          supervisorTime: conditionItem["supervisorTime"] is String
              ? 0
              : conditionItem["supervisorTime"],
          fillingValue: conditionItem["fillingValue"] ?? 0,
          amount: conditionItem["amount"],
          gapPrice: json.encode(init_gapPrice),
          status: conditionItem["supervisorResult"],
          gapPicture: conditionItem["gapPicture"] != null &&
              conditionItem["gapPicture"].isNotEmpty
              ? conditionItem["gapPicture"][0]
              : '',
          fillingPictureValue: conditionItem["fillingPictureValue"] ?? 0,
        ));
      });
      item["guarantee"]["details"].forEach((guaranteeItem) {
        var init_gapPrice =
        List.generate(guaranteeItem["amount"] ?? 0, (index) => "");
        maintainList.add(MaintainData(
          woid: item["id"],
          stepName: "安全保障",
          id: item["guarantee"]["id"],
          content: guaranteeItem["content"],
          operation: guaranteeItem["operation"],
          operatorId: guaranteeItem["operatorId"],
          operatorName: guaranteeItem["operatorName"],
          operatorResult: guaranteeItem["operatorResult"],
          operatorTime: guaranteeItem["operatorTime"] is String
              ? 0
              : guaranteeItem["operatorTime"],
          supervisorId: guaranteeItem["supervisorId"],
          supervisorName: guaranteeItem["supervisorName"],
          supervisorResult: guaranteeItem["supervisorResult"],
          supervisorTime: guaranteeItem["supervisorTime"] is String
              ? 0
              : guaranteeItem["supervisorTime"],
          fillingValue: guaranteeItem["fillingValue"] ?? 0,
          amount: guaranteeItem["amount"],
          gapPrice: json.encode(init_gapPrice),
          status: guaranteeItem["supervisorResult"],
          gapPicture: guaranteeItem["gapPicture"] != null &&
              guaranteeItem["gapPicture"].isNotEmpty
              ? guaranteeItem["gapPicture"][0]
              : '',
          fillingPictureValue: guaranteeItem["fillingPictureValue"] ?? 0,
        ));
      });
      item["instrument"]["details"].forEach((instrumentItem) {
        var init_gapPrice =
        List.generate(instrumentItem["amount"] ?? 0, (index) => "");
        maintainList.add(MaintainData(
          woid: item["id"],
          stepName: "工具及备件要求",
          id: item["instrument"]["id"],
          content: instrumentItem["content"],
          operation: instrumentItem["operation"],
          operatorId: instrumentItem["operatorId"],
          operatorName: instrumentItem["operatorName"],
          operatorResult: instrumentItem["operatorResult"],
          operatorTime: instrumentItem["operatorTime"] is String
              ? 0
              : instrumentItem["operatorTime"],
          supervisorId: instrumentItem["supervisorId"],
          supervisorName: instrumentItem["supervisorName"],
          supervisorResult: instrumentItem["supervisorResult"],
          supervisorTime: instrumentItem["supervisorTime"] is String
              ? 0
              : instrumentItem["supervisorTime"],
          fillingValue: instrumentItem["fillingValue"] ?? 0,
          amount: instrumentItem["amount"],
          gapPrice: json.encode(init_gapPrice),
          status: instrumentItem["supervisorResult"],
          gapPicture: instrumentItem["gapPicture"] != null &&
              instrumentItem["gapPicture"].isNotEmpty
              ? instrumentItem["gapPicture"][0]
              : '',
          fillingPictureValue: instrumentItem["fillingPictureValue"] ?? 0,
        ));
      });
      item["rollbackPlan"]["details"].forEach((rollbackPlanItem) {
        // 初始化 gapPrice为map
        var init_gapPrice =
        List.generate(rollbackPlanItem["amount"] ?? 0, (index) => "");
        maintainList.add(MaintainData(
          woid: item["id"],
          stepName: "回退计划",
          id: item["rollbackPlan"]["id"],
          content: rollbackPlanItem["content"],
          operation: rollbackPlanItem["operation"],
          operatorId: rollbackPlanItem["operatorId"],
          operatorName: rollbackPlanItem["operatorName"],
          operatorResult: rollbackPlanItem["operatorResult"],
          operatorTime: rollbackPlanItem["operatorTime"] is String
              ? 0
              : rollbackPlanItem["operatorTime"],
          supervisorId: rollbackPlanItem["supervisorId"],
          supervisorName: rollbackPlanItem["supervisorName"],
          supervisorResult: rollbackPlanItem["supervisorResult"],
          supervisorTime: rollbackPlanItem["supervisorTime"] is String
              ? 0
              : rollbackPlanItem["supervisorTime"],
          fillingValue: rollbackPlanItem["fillingValue"] ?? 0,
          amount: rollbackPlanItem["amount"],
          gapPrice: json.encode(init_gapPrice),
          status: rollbackPlanItem["supervisorResult"],
          gapPicture: rollbackPlanItem["gapPicture"] != null &&
              rollbackPlanItem["gapPicture"].isNotEmpty
              ? rollbackPlanItem["gapPicture"][0]
              : '',
          fillingPictureValue: rollbackPlanItem["fillingPictureValue"] ?? 0,
        ));
      });
      // 操作流程
      item["flow"]["items"].forEach((flowItem) {
        flowItem["details"].forEach((flowDetailItem) {
          // 初始化 gapPrice为map
          var init_gapPrice =
          List.generate(flowDetailItem["amount"] ?? 0, (index) => "");
          maintainList.add(MaintainData(
            woid: item["id"],
            stepName: "操作流程",
            id: item["flow"]["id"],
            parentName: flowItem["name"],
            parentId: flowItem["id"],
            content: flowDetailItem["content"],
            operation: flowDetailItem["operation"],
            operatorId: flowDetailItem["operatorId"],
            operatorName: flowDetailItem["operatorName"],
            operatorResult: flowDetailItem["operatorResult"],
            operatorTime: flowDetailItem["operatorTime"] is String
                ? 0
                : flowDetailItem["operatorTime"],
            supervisorId: flowDetailItem["supervisorId"],
            supervisorName: flowDetailItem["supervisorName"],
            supervisorResult: flowDetailItem["supervisorResult"],
            supervisorTime: flowDetailItem["supervisorTime"] is String
                ? 0
                : flowDetailItem["supervisorTime"],
            fillingValue: flowDetailItem["fillingValue"] ?? 0,
            amount: flowDetailItem["amount"],
            gapPrice: json.encode(init_gapPrice),
            status: flowDetailItem["supervisorResult"],
            gapPicture: flowDetailItem["gapPicture"] != null &&
                flowDetailItem["gapPicture"].isNotEmpty
                ? flowDetailItem["gapPicture"][0]
                : '',
            fillingPictureValue: flowDetailItem["fillingPictureValue"] ?? 0,
          ));
        });
      });
    });
    upholdSQLController.insertStepTake(stepTakeList);
    upholdSQLController.insertMaintainItem(maintainList);
  }

  /*授权获取位置权限用于获取无线名称 */
  checkLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        print('位置授权被拒了');
      }
    }
    if (permission == LocationPermission.deniedForever) {
      print("位置授权被永久拒了");
    }
    // 授权通过
    getCurrentWifiName();
  }

  /**获取当前无线名称 */
  getCurrentWifiName() async {
    final info = NetworkInfo();
    final getWifiName = await info.getWifiName();
    var formatWifiName = getWifiName?.replaceAll('"', '');
    wifiName.value = formatWifiName ?? '请检查网络连接';
    update();
  }

  /**体验优化 */
  loadingFun() {
    Get.dialog(
      barrierDismissible: false,
      const SpinKitFadingCircle(
        color: Colors.white,
        size: 50.0,
      ),
    );
  }

  /**菜单权限判断巡检菜单*/
  // 如果 roomTypeInfo 表为空,则允许进入有关巡检的页面
  handelPatrolMenu(String routePath) async {
    var result = await sqfliteController.findRoomTypeInfo("");
    if (result.isEmpty) {
      Fluttertoast.showToast(
          msg: "当前用户没有巡检任务",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 2,
          backgroundColor: Colors.black,
          textColor: Colors.white,
          fontSize: 16.0);
    } else {
      Get.toNamed(routePath);
    }
  }

  // 是否展示巡检模块
  getPurviewFun() async {
    var result = await sqfliteController.findRoomTypeInfo("");
    isInspection.value = result.isEmpty ? false : true;

    // var workResult = await upholdSQLController.findWorkOrder("");
    // var workExamine = await upholdApi.getAllData(
    //     globalController.userInfo.value!.data.eid, 2);
    // var workExamineLength = workExamine?.data['data']?.length ?? [];
    // isWork.value = workResult.isEmpty && workExamineLength == 0 ? false : true;
    update();
  }
  getSpeciality() async {
    var response = await roomApi.getAllSpeciality();
    SpecialityModule res = SpecialityModule.fromJson(response.data);
    // 将数据存入room表中
    sqfliteController.clearTable('speciality');
    List<Speciality> initData = [];
    res.data?.forEach((specialityItem) {
      initData.add(Speciality(
        roomType: specialityItem.roomType,
        roomTypeName: specialityItem.roomTypeName,
        deviceCount: specialityItem.deviceCount,
      ));
    });
    var userId = globalController.userInfo.value!.data.eid;
    sqfliteController.insertspeciality(initData);
  }

  /**提示优化 */
  // toast弹窗
  toastFun(content) {
    Fluttertoast.showToast(
        msg: content,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 3,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  // 时间格式化
  formatTime(timeStamp, {format = "yyyy-MM-dd HH:mm:ss"}) {
    var timeStampInt = timeStamp is String ? int.parse(timeStamp) : timeStamp;
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timeStampInt);
    String planTime = DateFormat(format).format(dateTime);
    return planTime;
  }

  /**获取app版本号和路径 */
  getAppVersion() async {
    /*获取当前APP版本信息*/
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;
    return version;
  }

  /**获取APP存储路径 */
  getAppPath() async {
    Directory? appDocDir = await getExternalStorageDirectory();
    final appDocPath = appDocDir?.path;
    return appDocPath;
  }

  /**app自动更新检查相关权限 */
  checkPermission() async {
    if (Theme.of(Get.context!).platform == TargetPlatform.android) {
      final status = await Permission.storage.status;
      if (status != PermissionStatus.granted) {
        final result = await Permission.storage.request();
        if (result == PermissionStatus.granted) {
          return true;
        }
      } else {
        return true;
      }
    }
    return false;
  }

  // app下载
  downLoadApp() async {
    var flag = await checkPermission();
    var version = await getAppVersion();
    var localPath = await getAppPath();
    if (flag) {
      String appName = globalController.userInfo.value!.data.apkName as String;
      String savePath = "$localPath/$appName";
      String appUrl = globalController.userInfo.value!.data.url as String;
      Dio dio = Dio();
      try {
        await dio.download(appUrl, savePath,
            onReceiveProgress: (received, total) {
              if (total != -1) {
                ///当前下载的百分比例
                print((received / total * 100).toStringAsFixed(0) + "%");
              }
            });
        await OpenFilex.open(savePath,
            type: "application/vnd.android.package-archive");
      } catch (e) {
        toastFun("服务器开小差了");
      }
    }
  }

  // app 下载弹窗
  downLoadAppAlter() {
    // 非正式环境不提示
    // if(kDebugMode) return;
    Get.dialog(
        barrierDismissible: false, // 禁止点击空白处退出
        AlertDialog(
          contentPadding: EdgeInsets.zero,
          content: Container(
            color: Colors.transparent,
            padding: EdgeInsets.zero,
            width: MyScreenUtil.width(400),
            height: MyScreenUtil.height(400),
            child: Stack(
              children: [
                Positioned(
                    left: 0,
                    right: 0,
                    child: Transform.translate(
                      offset: Offset(0, -40),
                      child: Container(
                        child: Image.asset('assets/images/3.0x/update.png'),
                      ),
                    )),
                Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: Text(
                              "新版本升级",
                              style: TextStyle(
                                  fontSize: MyScreenUtil.fontSize(20)),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: Text(
                              "检测到新版本 ${globalController.userInfo.value!.data.number}",
                              style: TextStyle(
                                  color: const Color.fromRGBO(134, 144, 156, 1),
                                  fontSize: MyScreenUtil.fontSize(14)),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(bottom: 16),
                            child: Text(
                              "${globalController.userInfo.value!.data.description}",
                              // "阿拉巴拉巴拉巴拉巴拉巴啦啦拉巴拉巴拉巴拉啊b阿拉巴拉巴拉巴拉巴拉巴啦啦拉巴拉巴拉巴拉啊b阿拉巴拉巴拉巴拉巴拉巴啦啦拉巴拉巴拉巴拉啊",
                              style: TextStyle(
                                  color: Color.fromRGBO(78, 89, 105, 1),
                                  fontSize: MyScreenUtil.fontSize(18)),
                            ),
                          ),
                          Container(
                              padding: EdgeInsets.only(left: 16, right: 16),
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 1,
                                    child: ElevatedButton(
                                      onPressed: () {
                                        downLoadApp();
                                      },
                                      style: ButtonStyle(
                                        elevation: MaterialStateProperty.all(0),
                                      ),
                                      child: Text("立即升级"),
                                    ),
                                  )
                                ],
                              )),
                          Container(
                              padding: EdgeInsets.only(left: 16, right: 16),
                              margin: EdgeInsets.only(bottom: 16),
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 1,
                                    child: ElevatedButton(
                                      style: ButtonStyle(
                                        elevation: MaterialStateProperty.all(0),
                                        backgroundColor:
                                        MaterialStateProperty.all<Color>(
                                            const Color.fromRGBO(
                                                242, 243, 245, 1)),
                                      ),
                                      onPressed: () {
                                        Get.back();
                                      },
                                      child: const Text(
                                        "以后再说",
                                        style: TextStyle(
                                            color:
                                            Color.fromRGBO(21, 22, 25, 1)),
                                      ),
                                    ),
                                  )
                                ],
                              ))
                        ],
                      ),
                    ))
              ],
            ),
          ), // 主内容区
        ));
  }

  // 检查更新
  /**
   * status true验证是否需要更新 flase 直接下载重新安装
   */
  inspectUpDate(bool status) async {
    var localVersing = globalController.userInfo.value!.data.number;
    var version = await getAppVersion();
    if (status) {
      if (localVersing == version) {
        toastFun("当前已经是最新版");
      } else {
        downLoadAppAlter();
      }
    } else {
      downLoadApp();
    }
  }

  RxBool hasIrPermission = false.obs;

  _fetchIrPermission() async {
    bool hasPermission = await DBHelper.existIrData(globalController.queryCurrentUserId() ?? '');
    if(hasPermission){
      hasIrPermission.value = true;
      update();
    }
  }

  /// 点击检测、结束检测、上传数据之前都先检查下 ir 数据是否存在（不能为空）
  Future preCheckIrData(VoidCallback action) async {
    var userId = globalController.queryCurrentUserId()!;
    var chest = await DBHelper.getIrChestList(userId);
    if(chest.isEmpty){
      IrDialog.showDialog("提示", "红外检测数据未更新,需要点击更新", "更新", "取消", confirmCall: () async {
        LoadingManager.showLoading();
        IrManager.resetIrData(userId);
        LoadingManager.disMiss();
        action.call();
      });
    }else {
      action.call();
    }
  }

  // 红外检测： 【更新按钮点击】
  resetIrDeviceData() async {
    var userId = globalController.queryCurrentUserId();
    if(userId == null || userId.isEmpty){
      toast('当前未登录');
      return;
    }

    int updateStatus = 0;
    var message = "";

    bool hasUnUpload = await DBHelper.existIrUpload(globalController.queryCurrentUserId()!);
    if(hasUnUpload){
      updateStatus = 0;
      message = "检测到当前数据未上传, 请完成此次巡检后再进行数据更新";
    }else {
      updateStatus = 1;
      message = "确定要更新巡检数据么";
    }

    IrDialog.showDialog('提示', message, '立即更新', '稍后更新', confirmCall: () async {
      LoadingManager.showLoading();
      await IrManager.resetIrData(userId);
      LoadingManager.disMiss();
    });
  }

}
