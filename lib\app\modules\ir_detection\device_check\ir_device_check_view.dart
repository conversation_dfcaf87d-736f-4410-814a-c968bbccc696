import 'package:flutter/material.dart';
import 'package:flutter_ir_plugin/ir/surface_platform_plugin.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_device.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';

import '../../../utils/screenutil.dart';
import 'ir_device_check_controller.dart';


/// 红外巡检， 设备检测 ，（包含红外温度扫描的原生UI，）
class IrDeviceCheckView extends StatelessWidget {

  IrDeviceCheckView({super.key});

  final _scroller = ScrollController();

  @override
  Widget build(BuildContext context) {
    return WillPopScope(onWillPop: () async {
      return true;
    }, child: GetBuilder<IrDeviceCheckController>(builder: (controller) {
      return SafeArea(
          child: Scaffold(
              resizeToAvoidBottomInset: false,
              body: Container(
                padding: EdgeInsets.all(MyScreenUtil.width(24)),
                color: const Color.fromRGBO(246, 248, 250, 1),
                child: Column(
          children: [
            InkWell(
              onTap: () => {Get.back()},
              child: Container(
                padding: EdgeInsets.only(
                    left: MyScreenUtil.width(24),
                    right: MyScreenUtil.width(24)
                ),
                // alignment: Alignment.centerLeft, // 设置垂直居中
                height: MyScreenUtil.height(60),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  children: [
                    Container(
                      width: MyScreenUtil.width(24),
                      height: MyScreenUtil.height(24),
                      child: Image.asset(
                        "assets/images/icon/left.png",
                        fit: BoxFit.cover,
                      ),
                    ),
                    const Text('设备管理')
                  ],
                ),
              ),
            ),
            controller.uiDevices.isEmpty ? Expanded(child: Container(
              alignment: Alignment.center,
              child: const Text('没有设备'),
            )):
            Expanded(
                child: Container(
              margin: const EdgeInsets.only(top: 20),
              child: Container(
                padding: EdgeInsets.only(top: 24 , bottom: 24),
                width: MyScreenUtil.getScreenWidth(),
                decoration: BoxDecoration(
                  // color: Color(0xfff6f8fa),
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Container(
                  child: Row(
                    children: [

                      Expanded(flex: 1, child: buildLeftDeviceList(controller)),

                      20.gap,

                      Expanded(
                        flex: 3,
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              // 设备详情
                              Container(
                                decoration: BoxDecoration(
                                      color: Color(0xfff5f7f9),
                                      border: Border.all(color: Color(0xffE5E6EB) , width: 0.5),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                 child: _buildCurrentDeviceInfo(controller),
                              ),

                              16.gap,

                              // 热区 + 温度备注
                              Expanded(child: Container(
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Expanded(
                                      flex: 5,
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Color(0xfff5f7f9),
                                            border: Border.all(color: Color(0xffE5E6EB) , width: 0.5),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                      child: SurfacePlatformPlugin().irPlatformView(),
                                    )),

                                  ],
                                ),
                              )),
                            ],
                          )),

                      20.gap,

                      Expanded(
                        flex: 1,
                        child: Container(
                          decoration: BoxDecoration(
                            // color: Color(0xfff6f8fa),
                            color: Colors.white,
                            border: Border.all(color: Color(0xffE5E6EB) , width: 1),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: _buildCurrentTemperatureInfo(context ,controller)
                      ),)
                    ],
                  ),
                ),
              ),
            )),
          ],
        ),
      )));
    }));
  }

  Widget _buildCurrentDeviceInfo(IrDeviceCheckController controller) {
    var item = controller.currentDevice;
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Expanded(flex: 2 ,child: _buildTopDeviceItem("设备编码" , item?.code ?? ''),),
        // Expanded(flex: 3 , child: _buildTopDeviceItem("全称" , item?.secondName() ?? ''),),
        Expanded(flex: 2 ,child: _buildTopDeviceItem("设备位置" , item?.installationSite ?? '')),
        Expanded(flex: 2 ,child: _buildTopDeviceItem("类型名称" , item?.infraredDeviceTypeName ?? '')),
        Expanded(flex: 2 ,child: _buildTopDeviceItem("设备编号" , item?.num ?? '')),
        // Expanded(flex: 2 ,child: _buildTopDeviceItem("检测项目" , item?.project ?? ''),),
        Expanded(flex: 2 ,child: _buildTopDeviceItem("上限值" , item?.upperLimitValue ?? ''),),
      ],
    );
  }

  _buildTopDeviceItem(String title , String value) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        6.gap,
        Container(
          alignment: Alignment.center,
          child: Text(title , style: const TextStyle(fontSize: 16, color: Color(0xff2F303A) , fontWeight: FontWeight.w600),),
        ),
        16.gap,
        Container(
          alignment: Alignment.center,
          child: Text(value , overflow: TextOverflow.ellipsis ,style: const TextStyle(fontSize: 14, color: Color(0xff86909C)),maxLines: 1, ),
        ),
        6.gap
      ],
    );
  }

  /// 红外屏幕右侧 的温度， 备注信息等
  Widget _buildCurrentTemperatureInfo(BuildContext context ,IrDeviceCheckController controller) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Container(
          padding: EdgeInsets.all(18),
          margin: EdgeInsets.all(16),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            // color: Color(0xfff6f8fa),
            color: Color(0xffE5E6EB),
            border: Border.all(color: Color(0xffE5E6EB) , width: 1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            // crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
               Text(controller.currentDevice?.checkTemperature ?? '', style: const TextStyle(fontSize: 14 ,color: Colors.black),),
               5.gap,
               Text("(阈值: ${controller.currentDevice?.upperLimitValue ?? 0})" , style: TextStyle(fontSize: 14 ,color: Color(0xffFF5757)),)
            ],
          ),
        ),

        Expanded(child: Container(
          decoration: BoxDecoration(
            // color: Color(0xfff6f8fa),
            color: Colors.white,
            border: Border.all(color: Color(0xffE5E6EB) , width: 0.5),
            borderRadius: BorderRadius.circular(8),
          ),
          margin: EdgeInsets.symmetric(horizontal: 10),
          child: TextField(
              keyboardType: TextInputType.multiline,
              maxLines: 5,
              style: TextStyle(
                  fontSize: MyScreenUtil.fontSize(16),
                  height: 1.5
              ),
              controller: controller.textEditingController,
              decoration: InputDecoration(
                  hintText: "添加备注...",
                  hintStyle: TextStyle(fontSize: 14 , color: Color(0xff86909C)),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.only(
                    top: MyScreenUtil.height(10),
                    bottom:MyScreenUtil.height(10),
                    left:MyScreenUtil.height(10),
                    right: MyScreenUtil.height(10),
                  )
              ),
              onChanged: (value) {
                controller.onCommentChanged(value);
              }
          ),
        )),

        // 点击拍照按钮
        InkWell(
          // onTap: () => _takePictureAction(Get.context! ,controller),
          onTap: () => controller.takePhoto(context , (){
            // controller.imageNameTextEditingController.text = '112.png';
            // controller.oldImagePath = '112.png';
            // _takePictureAction(context , controller);
          }),
          child: Icon(Icons.takeout_dining),
        ),

        6.gap,

        // 上一/下一设备 两个按钮
        Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            10.gap,
            Expanded(child: InkWell(
              onTap: () {
                controller.lastDevice();
                var current = controller.getCurrentIndex();
                if(current > -1){
                  _scrollToMiddle(controller, current);
                }
              },
              child: Image.asset(AssetsRes.IR_LAST_DEVICE, fit: BoxFit.fill,),
            )),
            10.gap,
            Expanded(child: InkWell(
              onTap: () {
                controller.nextDevice();
                var current = controller.getCurrentIndex();
                if(current > -1){
                  _scrollToMiddle(controller ,current);
                }
              },
              child: Image.asset(AssetsRes.IR_NEXT_DEVICE , fit: BoxFit.fill,),
            ),),

            10.gap,
          ],
        ),

        16.gap,
      ],
    );
  }

  // 确认拍照图片名称
  _takePictureAction(BuildContext context, IrDeviceCheckController controller) async {
    Get.dialog(
        barrierDismissible: false,
        AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: const [Text("提示")],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text("确认图片名称，可编辑修改"),
              TextField(
                onChanged: (value){

                },
                controller: controller.imageNameTextEditingController,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
              )
            ],

          ),
          actions: [
            ElevatedButton(
                onPressed: () {
                  Get.back();
                  controller.reNameFile(controller.imageNameTextEditingController.text);
                },
                style: ButtonStyle(backgroundColor:
                MaterialStateProperty.resolveWith((states) {
                  return MyScreenUtil.ThemColor();
                })),
                child: const Text("确认")),

            ElevatedButton(
                onPressed: () {
                  Get.back();
                },
                style: ButtonStyle(backgroundColor:
                MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                })),
                child: const Text("取消", style: TextStyle(color: Colors.black),)),
          ],
        ));
  }

  /// 左侧 设备列表
  Widget buildLeftDeviceList(IrDeviceCheckController controller) {
    return Container(
      decoration: BoxDecoration(
          color: Color(0xfff6f8fa), borderRadius: BorderRadius.circular(16.0)),
      child: ListView.builder(
        controller: _scroller,
          itemCount: controller.uiDevices.length,
          itemBuilder: (ctx, index) {
            var item = controller.uiDevices[index];
            return deviceListItem(item , controller , index);
          }),
    );
  }

  _scrollToMiddle(IrDeviceCheckController controller, int currentIndex) async {
    // await Future.delayed(Duration.zero);
    // final context = controller.itemKeys[currentIndex].currentContext!;
    // Scrollable.ensureVisible(
    //   context,
    //   alignment: 0.5,  // 控制出现的位置
    //   duration: const Duration(milliseconds: 300),  // 动画时长
    // );

    if(currentIndex <= -1) return;
    _scroller.animateTo(currentIndex * 135, duration: const Duration(microseconds: 200), curve: Curves.ease);
  }

  /// 设备列表 item
  Widget deviceListItem(IrChestDevice item , IrDeviceCheckController controller , int index) {
    return InkWell(
      onTap: (){
        // 选中
        controller.selectItem(item);
      },
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: item.selected ? [Color(0xff7992fe), Color(0xFF5777FF)] : [Color(0xFFFFFFFF), Color(0xFFFFFFFF)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(16.0)),

        margin: EdgeInsets.all(10),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(child: Text(item.installationSite ?? '' , style: TextStyle(fontSize: 18 , color: item.selected ? Colors.white : Colors.black),),),
                5.gap,
                Container(
                  width: 6,
                  height: 6,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      color: item.isFinish() ? Colors.green : Colors.red, borderRadius: BorderRadius.circular(100.0)),
                ),
                10.gap,
                Text(item.isFinish() ? '已完成': '未完成' , style: TextStyle(fontSize: 12 , color: item.selected ? Colors.white : Colors.black),),
              ],
            ),
            47.gap,

            Container(
              alignment: Alignment.centerLeft,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16 , vertical: 4),
                decoration : BoxDecoration(
                    color: item.selected ? Color(0xffa2b4fd) : Color(0xffeef2ff), borderRadius: BorderRadius.circular(26.0)),
                child: Text(item.secondName(), style: TextStyle(fontSize: 12 , color: item.selected ? Colors.white : Color(0xff5777FF)),),
              ),
            ),

          ],
        ),
      ),
    );
  }
}
