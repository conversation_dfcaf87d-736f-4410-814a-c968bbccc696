import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';

import '../../../controllers/global_controller.dart';
import '../../../routes/app_pages.dart';
import '../../../routes/route_helper.dart';
import '../../../utils/myIcon.dart';
import "../../../utils/screenutil.dart";
import '../../ir_detection/ir_detection_enter.dart';
import '../../ir_detection/ir_detection_task_enter.dart';
import '../controllers/home_controller.dart';

class HomeView extends GetView<HomeController> {
  // GlobalController myHome = Get.find();
  GlobalController globalController = Get.find();

  HomeView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return WillPopScope(
          onWillPop: () async {
            if (controller.lastPopTime.value == null ||
                DateTime.now().difference(controller.lastPopTime.value) >
                    Duration(seconds: 2)) {
              controller.setLastPopTime();
              Fluttertoast.showToast(
                  msg: "在按一次退出应用",
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.CENTER,
                  timeInSecForIosWeb: 1,
                  backgroundColor: Colors.black,
                  textColor: Colors.white,
                  fontSize: 16.0);
              return false;
            } else {
              controller.setLastPopTime();
              // 退出app
              await SystemChannels.platform.invokeMethod('SystemNavigator.pop');
              return false;
            }
          },
          child: Scaffold(
              body: Container(
            padding: EdgeInsets.only(top: MyScreenUtil.getStatusBarHeight()),
            decoration: BoxDecoration(
              color: Color.fromRGBO(47, 48, 58, 1),
            ),
            child: Row(
              children: [
                // 左侧栏
                Container(
                    padding: EdgeInsets.all(20),
                    width: MyScreenUtil.width(MyScreenUtil.width(362)),
                    // height: MyScreenUtil.height(774),
                    decoration:
                        BoxDecoration(color: Color.fromRGBO(47, 48, 58, 1)),
                    child: Container(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // 用户信息
                          userInfo(),
                          // 操作
                          // userOperate(),
                          // 检查更新
                          Container(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                // Container(
                                //   margin: EdgeInsets.only(right: MyScreenUtil.height(10)),
                                //   child: ElevatedButton(
                                //     onPressed: (){controller.inspectUpDate(true);},
                                //     style: ButtonStyle(
                                //       backgroundColor: MaterialStateProperty.all<Color>(Colors.blue),
                                //     ),
                                //     child: const Text("检查更新")
                                //   ),
                                // ),
                                // Container(
                                //   margin: EdgeInsets.only(
                                //       right: MyScreenUtil.height(10)),
                                //   child: ElevatedButton(
                                //       onPressed: () {
                                //         controller.inspectUpDate(false);
                                //       },
                                //       child: const Text("重新下载")),
                                // ),
                                InkWell(
                                  onTap: () async {
                                    controller.logout();
                                  },
                                  child: Container(
                                    width: MyScreenUtil.width(40),
                                    height: MyScreenUtil.height(40),
                                    child: Image.asset(
                                        "assets/images/icon/set.png"),
                                  ),
                                ),
                                Text(
                                  "当前版本号 ${controller.version.value}",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: MyScreenUtil.fontSize(12),
                                  ),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    )),
                // 右侧栏
                Expanded(
                  flex: 1,
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 25, top: 28, right: 25, bottom: 28),
                    decoration: const BoxDecoration(
                        color: Color.fromRGBO(246, 246, 251, 1)),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          child: Column(
                            children: [
                              controller.isInspection.value
                                        ?
                              Container(
                                padding: EdgeInsets.only(
                                    left: MyScreenUtil.width(24),
                                    right: MyScreenUtil.width(24)),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(14.0),
                                  color: Color(0xFFFFFFFF),
                                ),
                                child: Column(
                                  children: [
                                    controller.isInspection.value
                                        ? Container(
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Container(
                                                  child: Row(
                                                    children: [
                                                      Container(
                                                        width:
                                                            MyScreenUtil.width(
                                                                5),
                                                        height:
                                                            MyScreenUtil.height(
                                                                20),
                                                        margin: const EdgeInsets
                                                            .only(right: 13),
                                                        decoration:
                                                            BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      14.0),
                                                          color:
                                                              Color(0xFF5777FF),
                                                        ),
                                                      ),
                                                      Text(
                                                        "机房巡检",
                                                        style: TextStyle(
                                                            color: Color(
                                                                0xFF2F303A),
                                                            fontSize:
                                                                MyScreenUtil
                                                                    .fontSize(
                                                                        18),
                                                            fontWeight:
                                                                FontWeight
                                                                    .w500),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                                Container(
                                                  child: TextButton.icon(
                                                    onPressed: () {
                                                      controller.resetAllData();
                                                    },
                                                    icon: Icon(
                                                      Icons.refresh_sharp,
                                                      size:
                                                          MyScreenUtil.fontSize(
                                                              20),
                                                      color: MyScreenUtil
                                                          .ThemColor(),
                                                    ),
                                                    label: Text(
                                                      "更新",
                                                      style: TextStyle(
                                                          fontSize: MyScreenUtil
                                                              .fontSize(16),
                                                          color: MyScreenUtil
                                                              .ThemColor()),
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          )
                                        : Container(),
                                    // 巡检菜单
                                    Container(
                                      margin: EdgeInsets.only(
                                          bottom: MyScreenUtil.width(24)),
                                      width: double.infinity,
                                      child: controller.isInspection.value
                                          ? patrolMenu()
                                          : null,
                                    ),
                                  ],
                                ),
                              ):Container(),
                              // 维护工单
                              controller.userInfo.value.deviceMaintainPer == 1
                                  ? Container(
                                      padding: EdgeInsets.only(
                                          left: MyScreenUtil.width(24),
                                          right: MyScreenUtil.width(24),
                                          bottom: MyScreenUtil.width(24)),
                                      margin: EdgeInsets.only(
                                          top: MyScreenUtil.width(24)),
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(14.0),
                                        color: Color(0xFFFFFFFF),
                                      ),
                                      child: Column(
                                        children: [
                                          Container(
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Container(
                                                  child: Row(
                                                    children: [
                                                      Container(
                                                        width:
                                                            MyScreenUtil.width(
                                                                5),
                                                        height:
                                                            MyScreenUtil.height(
                                                                20),
                                                        margin: const EdgeInsets
                                                            .only(right: 13),
                                                        decoration:
                                                            BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      14.0),
                                                          color:
                                                              Color(0xFF5777FF),
                                                        ),
                                                      ),
                                                      Text(
                                                        "维护工单",
                                                        style: TextStyle(
                                                            color: Color(
                                                                0xFF2F303A),
                                                            fontSize:
                                                                MyScreenUtil
                                                                    .fontSize(
                                                                        18),
                                                            fontWeight:
                                                                FontWeight
                                                                    .w500),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                                Container(
                                                  child: TextButton.icon(
                                                    onPressed: () {
                                                      controller.updateUphold();
                                                    },
                                                    icon: Icon(
                                                      Icons.refresh_sharp,
                                                      size:
                                                          MyScreenUtil.fontSize(
                                                              20),
                                                      color: MyScreenUtil
                                                          .ThemColor(),
                                                    ),
                                                    label: Text(
                                                      "更新",
                                                      style: TextStyle(
                                                          fontSize: MyScreenUtil
                                                              .fontSize(16),
                                                          color: MyScreenUtil
                                                              .ThemColor()),
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                          // 维护工单
                                          Container(
                                            width: double.infinity,
                                            child: upholdMenu(),
                                          ),
                                        ],
                                      ))
                                  : Container(),

                              controller.hasIrPermission.value ?
                              IrTaskEnter(refreshAction: (){
                                // 更新红外检测的数据
                                controller.resetIrDeviceData();

                              }, startAction: (){

                                controller.preCheckIrData(() {

                                  route(Routes.IR_TASK);

                                  // route(Routes.IR_START_PATROL , params: {'isStartModel': true});
                                });
                              }, recordAction: (){
                                controller.preCheckIrData(() {
                                  route(Routes.IR_UPLOAD_DATA);
                                });
                              },) : Container()

                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          )));
    });
  }

  // 用户信息
  Widget userInfo() {
    return Obx(() => Container(
          padding: EdgeInsets.fromLTRB(1, 24, 1, 1),
          decoration: BoxDecoration(
            border: Border.all(
                color: const Color.fromRGBO(237, 234, 254, 1), width: 1.0),
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.white.withOpacity(0.3),
                Colors.white.withOpacity(0.3),
                Colors.white.withOpacity(0.4),
                Color.fromRGBO(148, 205, 255, 1).withOpacity(0.8),
                Color.fromRGBO(148, 205, 255, 1).withOpacity(0.9),
                Color.fromRGBO(112, 143, 250, 1).withOpacity(1),
              ],
              stops: [0.0, 0.4, 0.45, 0.55, 0.6, 1.0],
            ),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 33),
                child: Text(
                  controller.userInfo.value.companyName,
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: MyScreenUtil.fontSize(14),
                      fontWeight: FontWeight.bold),
                ),
              ),
              Container(
                width: MyScreenUtil.width(80),
                height: MyScreenUtil.height(80),
                decoration: BoxDecoration(
                    // color: MyScreenUtil.ThemColor(),
                    borderRadius: BorderRadius.circular(100),
                    image: controller.userInfo.value.avatar == ''
                        ? const DecorationImage(
                            image: AssetImage("assets/images/userLogo.png"),
                            fit: BoxFit.fill)
                        : DecorationImage(
                            image:
                                NetworkImage(controller.userInfo.value.avatar),
                            fit: BoxFit.fill)),
              ),
              // 用户名
              Container(
                margin: const EdgeInsets.only(top: 10),
                child: Text(controller.userInfo.value.name,
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: MyScreenUtil.fontSize(16),
                        fontWeight: FontWeight.w600)),
              ),
              // 联系方式
              Container(
                margin: const EdgeInsets.only(top: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(
                      child: Icon(Icons.phone, color: Colors.white ,size: 15,),
                    ),
                    Text(
                      controller.userInfo.value.phone,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: MyScreenUtil.fontSize(16),
                      ),
                    )
                  ],
                ),
              ),
              // Container(
              //   height: ,
              // ),
              // 职工信息
              Container(
                margin: EdgeInsets.only(
                    top: MyScreenUtil.height(26), left: MyScreenUtil.width(22)),
                child: Row(children: [
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                            margin: const EdgeInsets.only(bottom: 15),
                            child: Row(
                              children: [
                                Container(
                                  width: MyScreenUtil.width(24),
                                  height: MyScreenUtil.height(24),
                                  child: Image.asset(
                                      "assets/images/icon/zhiwei.png"),
                                ),
                                Text(
                                  " ${controller.userInfo.value.occupation ?? '暂无'}",
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: MyScreenUtil.fontSize(16)),
                                ),
                              ],
                            )),
                        Container(
                            margin: const EdgeInsets.only(bottom: 15),
                            child: Row(
                              children: [
                                Container(
                                  width: MyScreenUtil.width(24),
                                  height: MyScreenUtil.height(24),
                                  child: Image.asset(
                                      "assets/images/icon/bumen.png"),
                                ),
                                Text(
                                  " ${controller.userInfo.value.deptName ?? '暂无'}",
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: MyScreenUtil.fontSize(16)),
                                ),
                              ],
                            )),
                        Container(
                            margin: const EdgeInsets.only(bottom: 15),
                            child: Row(
                              children: [
                                Container(
                                  width: MyScreenUtil.width(24),
                                  height: MyScreenUtil.height(24),
                                  child: Image.asset(
                                      "assets/images/icon/zhuanye.png"),
                                ),
                                Text(
                                  " ${controller.userInfo.value.technicalPost ?? '暂无'}",
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: MyScreenUtil.fontSize(16)),
                                ),
                              ],
                            )),
                      ]),
                ]),
              ),
            ],
          ),
        ));
  }

  // 用户操作
  Widget userOperate() {
    return Container(
      margin: EdgeInsets.only(
          bottom: MyScreenUtil.height(48), top: MyScreenUtil.height(48)),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              controller.logout();
            },
            child: Container(
              margin: const EdgeInsets.only(top: 10, bottom: 10),
              width: MyScreenUtil.width(156),
              height: MyScreenUtil.fontSize(47),
              decoration: BoxDecoration(
                  border: Border.all(
                      color: const Color.fromRGBO(237, 234, 254, 1),
                      width: 1.0),
                  borderRadius: BorderRadius.circular(MyScreenUtil.radius(8))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    margin: EdgeInsets.only(right: 6),
                    child: Icon(MyIcon.out,
                        color: Colors.white, size: MyScreenUtil.fontSize(18)),
                  ),
                  Text(
                    "退出",
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: MyScreenUtil.fontSize(16)),
                  )
                ],
              ),
            ),
          ),
          Container(
              child: controller.wifiName.value == '请检查网络连接'
                  ? Text(
                      "请检查网络连接",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: MyScreenUtil.fontSize(18)),
                    )
                  : Text(
                      "当前网络 ${controller.wifiName.value}",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: MyScreenUtil.fontSize(18)),
                    ))
        ],
      ),
    );
  }

  // 巡检菜单栏
  Widget patrolMenu() {
    return Wrap(
      alignment: WrapAlignment.start,
      spacing: 20,
      children: [
        InkWell(
          onTap: () {
            controller.handelPatrolMenu("/check-room");
          },
          child: Container(
            width: MyScreenUtil.width(210),
            height: MyScreenUtil.height(105),
            margin: EdgeInsets.only(top: MyScreenUtil.height(10)),
            padding: EdgeInsets.all(MyScreenUtil.height(16)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.0),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.08),
                  // offset: Offset(0, 0),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: MyScreenUtil.width(73),
                  height: MyScreenUtil.height(73),
                  margin: EdgeInsets.only(right: MyScreenUtil.width(12)),
                  child: Image.asset("assets/images/patrolA.png"),
                ),
                Text("开始巡检",
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(20),
                    )),
              ],
            ),
          ),
        ),
        InkWell(
          onTap: () {
            controller.handelPatrolMenu("/end-task");
          },
          child: Container(
            width: MyScreenUtil.width(210),
            height: MyScreenUtil.height(105),
            margin: EdgeInsets.only(top: MyScreenUtil.height(10)),
            padding: EdgeInsets.all(MyScreenUtil.height(16)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.0),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.08),
                  // offset: Offset(0, 0),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: MyScreenUtil.width(73),
                  height: MyScreenUtil.height(73),
                  margin: EdgeInsets.only(right: MyScreenUtil.width(12)),
                  child: Image.asset("assets/images/patrolB.png"),
                ),
                Text("结束巡检",
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(20),
                    )),
              ],
            ),
          ),
        ),
        InkWell(
          onTap: () {
            controller.handelPatrolMenu("/update");
          },
          child: Container(
            width: MyScreenUtil.width(210),
            height: MyScreenUtil.height(105),
            margin: EdgeInsets.only(top: MyScreenUtil.height(10)),
            padding: EdgeInsets.all(MyScreenUtil.height(16)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.0),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.08),
                  // offset: Offset(0, 0),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: MyScreenUtil.width(73),
                  height: MyScreenUtil.height(73),
                  margin: EdgeInsets.only(right: MyScreenUtil.width(12)),
                  child: Image.asset("assets/images/patrolC.png"),
                ),
                Text("上传数据",
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(20),
                    )),
              ],
            ),
          ),
        ),
        InkWell(
          onTap: () {
            controller.handelPatrolMenu("/record");
          },
          child: Container(
            width: MyScreenUtil.width(210),
            height: MyScreenUtil.height(105),
            margin: EdgeInsets.only(top: MyScreenUtil.height(10)),
            padding: EdgeInsets.all(MyScreenUtil.height(16)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.0),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.08),
                  // offset: Offset(0, 0),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: MyScreenUtil.width(73),
                  height: MyScreenUtil.height(73),
                  margin: EdgeInsets.only(right: MyScreenUtil.width(12)),
                  child: Image.asset("assets/images/patrolD.png"),
                ),
                Text("巡检记录",
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(20),
                    )),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 维护工单
  Widget upholdMenu() {
    return Wrap(
      alignment: WrapAlignment.start,
      spacing: 20,
      children: [
        InkWell(
          onTap: () {
            Get.toNamed("/uphold-worl-order");
          },
          child: Container(
            width: MyScreenUtil.width(220),
            height: MyScreenUtil.height(105),
            margin: EdgeInsets.only(top: MyScreenUtil.height(10)),
            padding: EdgeInsets.all(MyScreenUtil.height(16)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.0),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.08),
                  // offset: Offset(0, 0),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: MyScreenUtil.width(73),
                  height: MyScreenUtil.height(73),
                  margin: EdgeInsets.only(right: MyScreenUtil.width(12)),
                  child: Image.asset("assets/images/singleA.png"),
                ),
                Text("维护工单",
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(20),
                    )),
              ],
            ),
          ),
        ),
        InkWell(
          onTap: () {
            Get.toNamed("/uphold-record");
          },
          child: Container(
            width: MyScreenUtil.width(220),
            height: MyScreenUtil.height(105),
            margin: EdgeInsets.only(top: MyScreenUtil.height(10)),
            padding: EdgeInsets.all(MyScreenUtil.height(16)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.0),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.08),
                  // offset: Offset(0, 0),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: MyScreenUtil.width(73),
                  height: MyScreenUtil.height(73),
                  margin: EdgeInsets.only(right: MyScreenUtil.width(12)),
                  child: Image.asset("assets/images/singleB.png"),
                ),
                Text("维护记录",
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(20),
                    )),
              ],
            ),
          ),
        ),
        InkWell(
          onTap: () {
            Get.toNamed("/uphold-examine");
          },
          child: Container(
            width: MyScreenUtil.width(220),
            height: MyScreenUtil.height(105),
            margin: EdgeInsets.only(top: MyScreenUtil.height(10)),
            padding: EdgeInsets.all(MyScreenUtil.height(16)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.0),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.08),
                  // offset: Offset(0, 0),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: MyScreenUtil.width(73),
                  height: MyScreenUtil.height(73),
                  margin: EdgeInsets.only(right: MyScreenUtil.width(12)),
                  child: Image.asset("assets/images/singleC.png"),
                ),
                Text("工单审核",
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(20),
                    )),
              ],
            ),
          ),
        ),
        // InkWell(
        //   onTap: (){
        //     Get.toNamed("/socket-page");
        //   },
        //   child: Container(
        //     width: MyScreenUtil.width(180),
        //     height: MyScreenUtil.height(86),
        //     margin: EdgeInsets.only(top:MyScreenUtil.height(20)),
        //     decoration: BoxDecoration(
        //       color: Color.fromRGBO(255, 255, 255, 1),
        //       borderRadius: BorderRadius.circular(10)
        //     ),
        //     child:Row(
        //       mainAxisAlignment: MainAxisAlignment.spaceAround,
        //       children: [
        //         Text(
        //           "socket服务",
        //           style: TextStyle(
        //             fontSize: MyScreenUtil.fontSize(20)
        //           ),
        //         ),
        //         Container(
        //           width: MyScreenUtil.width(80),
        //           height: MyScreenUtil.height(86),
        //           padding: EdgeInsets.only(top: 10),
        //           child: Image.asset("assets/images/singleC.png",fit:BoxFit.cover),
        //         )
        //       ],
        //     ),
        //   ),
        // ),
      ],
    );
  }
}
