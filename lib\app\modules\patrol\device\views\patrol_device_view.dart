import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';

import '../../../../controllers/global_controller.dart';
import '../controllers/patrol_device_controller.dart';
import "package:sjzx_patrol_system_mobile/app/utils/myTextField.dart";

class PatrolDeviceView extends GetView<PatrolDeviceController> {
  GlobalController globalController = Get.find();
  PatrolDeviceView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Color.fromRGBO(246, 248, 250, 1),
        body: SafeArea(
          child: Container(
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Color(0xFFFFFFFF),
                borderRadius: BorderRadius.circular(16),
              ),
              padding: EdgeInsets.only(
                  left: MyScreenUtil.width(24),
                  right: MyScreenUtil.width(24),
                  bottom: MyScreenUtil.width(24)),
              child: Column(
                children: [
                  Container(
                    // alignment: Alignment.centerLeft, // 设置垂直居中
                    height: MyScreenUtil.height(60),
                    decoration: const BoxDecoration(
                      border: Border(
                        bottom:
                            BorderSide(width: 1, color: Color(0xFFFFeeeeee)),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          onTap: () => {Get.back()},
                          child: Container(
                            child: Row(children: [
                              Container(
                                width: MyScreenUtil.width(24),
                                height: MyScreenUtil.height(24),
                                child: Image.asset(
                                  "assets/images/icon/left.png",
                                  fit: BoxFit.cover,
                                ),
                              ),
                              Obx(() => Text(controller.roomId.value)),
                            ]),
                          ),
                        ),
                        Container(
                          child: Row(
                            children: [
                              InkWell(
                                  onTap: () {
                                    controller.roomAbnormalAlter();
                                  },
                                  child: Container(
                                    alignment: Alignment.center,
                                    width: MyScreenUtil.width(84),
                                    height: MyScreenUtil.height(34),
                                    margin: EdgeInsets.only(
                                        right: MyScreenUtil.width(16)),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(4),
                                      border: Border.all(
                                          width: 1.0,
                                          color:
                                              Color.fromRGBO(245, 63, 63, 1)),
                                    ),
                                    child: Text(
                                      "异常",
                                      style: TextStyle(
                                          color: Color.fromRGBO(245, 63, 63, 1),
                                          fontSize: MyScreenUtil.fontSize(18),
                                          fontWeight: FontWeight.w600),
                                    ),
                                  )),
                              InkWell(
                                  onTap: () {
                                    //todo 找不到
                                    controller.inspectFinishBtn();
                                  },
                                  child: Container(
                                    alignment: Alignment.center,
                                    width: MyScreenUtil.width(84),
                                    height: MyScreenUtil.height(34),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(4),
                                      gradient: LinearGradient(
                                        colors: [
                                          Color(0xFF5777FF),
                                          Color(0xFF9FBDFF),
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        stops: [0.0, 1.0],
                                        transform: GradientRotation(
                                            276 * 3.14 / 180), // 将角度转换为弧度
                                      ),
                                    ),
                                    child: Text(
                                      "完成",
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: MyScreenUtil.fontSize(18),
                                          fontWeight: FontWeight.w600),
                                    ),
                                  )),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  Container(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Obx(
                          () => Container(
                            alignment: Alignment.centerLeft,
                            height: MyScreenUtil.height(50),
                            padding:EdgeInsets.all(MyScreenUtil.width(4)),
                            decoration: BoxDecoration(
                              color: Color.fromRGBO(246, 248, 250, 1),
                              borderRadius: BorderRadius.circular(MyScreenUtil.width(8))
                            ),
                            child: Row(
                              children: [
                                for (var i = 0;
                                    i < controller.specialityList.length;
                                    i++)
                                  InkWell(
                                    onTap: () {
                                      // controller.TabRoom(controller.roomData[i]);
                                      final uniqueId =
                                          DateTime.now().millisecondsSinceEpoch;
                                      // Get.offNamed("/device/${uniqueId}",
                                      //     arguments: {
                                      //       "roomName":
                                      //           "${controller.roomData[i]['name']}-${controller.roomData[i]['id']}",
                                      //       "roomType":
                                      //           "${controller.roomData[i]['type']}",
                                      //       "roomId":
                                      //           "${controller.roomData[i]['id']}",
                                      //       "name":
                                      //           "${controller.roomData[i]['name']}"
                                      //     });
                                      controller.changeSpeciality(i);
                                    },
                                    child: Container(
                                        decoration: BoxDecoration(
                                          color: controller.specialityList[i]['isActive'],
                                          borderRadius:
                                              BorderRadius.circular(5),
                                        ),
                                        padding: EdgeInsets.fromLTRB(MyScreenUtil.width(16), MyScreenUtil.width(5), MyScreenUtil.width(16), MyScreenUtil.width(5)),
                                        child: Text(
                                          controller.specialityList[i]['roomTypeName'],
                                          style: TextStyle(
                                              color: controller.specialityList[i]
                                                          ['fontColor'],),
                                        )),
                                  ),
                              ],
                            ),
                          ),
                        ),
                        Container(
                            margin:
                                EdgeInsets.only(left: MyScreenUtil.width(20)),
                            child: Row(
                              children: [
                                Container(
                                  child: Text(
                                    "打开键盘",
                                    style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize: MyScreenUtil.fontSize(18)),
                                  ),
                                ),
                                Obx(() => Switch(
                                      activeColor: Color(0xFF5777FF),
                                      inactiveThumbColor: Colors.red,
                                      value: controller.keyBoard.value,
                                      onChanged: (bool value) {
                                        controller.openKeyBoard(value);
                                      },
                                    )),
                                // Switch(
                                //   activeColor: Color(0xFF5777FF),
                                //   value: controller.keyBoard.value,
                                //   onChanged: (val) {
                                //     // controller.switchValue.value = val;
                                //     controller.openKeyBoard(val);
                                //   },
                                //   inactiveThumbColor: Colors.red,
                                // )
                              ],
                            ))
                      ],
                    ),
                  ),

                  //内容
                  Expanded(
                      child: Row(
                    children: [
                      equipmentList(),
                      Expanded(
                        flex: 1,
                        child: Container(
                          decoration: BoxDecoration(
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Color.fromRGBO(0, 0, 0, 0.1),
                                  offset: Offset(0, 0),
                                  blurRadius: 5,
                                  spreadRadius: 0,
                                ),
                              ],
                              borderRadius: BorderRadius.circular(10)),
                          margin: EdgeInsets.only(
                              left: MyScreenUtil.width(16),
                              right: MyScreenUtil.width(16)),
                          padding: EdgeInsets.only(
                              top: MyScreenUtil.width(10),
                              left: MyScreenUtil.width(24),
                              right: MyScreenUtil.width(24)),
                          child: Obx(() => inspectData()),
                        ),
                      ),
                      Obx(() => controller.keyBoard.value
                          ? Container(
                              width: MyScreenUtil.width(368),
                              decoration: BoxDecoration(
                                  color: Color.fromRGBO(246, 248, 250, 1),
                                  borderRadius: BorderRadius.circular(16)),
                              child: Column(
                                children: [
                                  InkWell(
                                    onTap: () {
                                      controller.searchCode();
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      width: MyScreenUtil.width(336),
                                      height: MyScreenUtil.height(120),
                                      margin: EdgeInsets.only(
                                          top: MyScreenUtil.height(24),
                                          bottom: MyScreenUtil.height(24)),
                                      decoration: BoxDecoration(
                                        image: DecorationImage(
                                          image: AssetImage(
                                              "assets/images/searchCode_bg.png"),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      child: Text(
                                        '                   扫描设备码',
                                        style: TextStyle(color: Colors.white),
                                      ),
                                    ),
                                  ),
                                  Wrap(
                                    spacing:
                                        MyScreenUtil.width(16), // 设置子部件之间的水平间距
                                    runSpacing: MyScreenUtil.height(
                                        16), // 设置子部件之间的垂直间距r
                                    children: <Widget>[
                                      NumberButton('1', controller.updateInput),
                                      NumberButton('2', controller.updateInput),
                                      NumberButton('3', controller.updateInput),
                                      NumberButton('4', controller.updateInput),
                                      NumberButton('5', controller.updateInput),
                                      NumberButton('6', controller.updateInput),
                                      NumberButton('7', controller.updateInput),
                                      NumberButton('8', controller.updateInput),
                                      NumberButton('9', controller.updateInput),
                                      NumberButton('0', controller.updateInput),
                                      NumberButton('.', controller.updateInput),
                                      NumberButton('-', controller.updateInput),
                                      BackButton('←', controller.BackInput),
                                      EnterButton(
                                          'Enter', controller.EnterInput),
                                    ],
                                  ),
                                  Wrap(
                                    spacing:
                                        MyScreenUtil.width(16), // 设置子部件之间的水平间距
                                    runSpacing:
                                        MyScreenUtil.width(16), // 设置子部件之间的垂直间距r
                                    children: <Widget>[
                                      Container(
                                          width: MyScreenUtil.width(160),
                                          height: MyScreenUtil.height(60),
                                          margin: EdgeInsets.only(
                                              top: MyScreenUtil.height(24)),
                                          child: ElevatedButton(
                                            style: ButtonStyle(
                                              backgroundColor:
                                                  MaterialStateProperty.all<
                                                          Color>(
                                                      Color.fromRGBO(
                                                          87, 119, 255, 0.6)),
                                            ),
                                            child: Container(
                                                width: MyScreenUtil.width(50),
                                                height: MyScreenUtil.height(16),
                                                child: Image.asset(
                                                  'assets/images/icon/back.png',
                                                  fit: BoxFit.fill,
                                                )),
                                            onPressed: () {
                                              controller.changeDevice('back');
                                            },
                                          )),
                                      Container(
                                          width: MyScreenUtil.width(160),
                                          height: MyScreenUtil.height(60),
                                          margin: EdgeInsets.only(
                                              top: MyScreenUtil.height(24)),
                                          child: ElevatedButton(
                                            style: ButtonStyle(
                                              backgroundColor:
                                                  MaterialStateProperty.all<
                                                          Color>(
                                                      Color.fromRGBO(
                                                          87, 119, 255, 0.6)),
                                            ),
                                            child: Container(
                                                width: MyScreenUtil.width(50),
                                                height: MyScreenUtil.height(16),
                                                child: Image.asset(
                                                  'assets/images/icon/next.png',
                                                  fit: BoxFit.fill,
                                                )),
                                            onPressed: () {
                                              controller.changeDevice('next');
                                            },
                                          )),
                                    ],
                                  ),
                                ],
                              ),
                            )
                          : Container()),
                    ],
                  )),
                  // Container(
                  //   child: Row(children: [
                  //     Container(
                  //       child: Column(
                  //         children: [
                  //           Container(
                  //             width: MyScreenUtil.width(242),
                  //             child: ListView(
                  //               children: [],
                  //             ),
                  //           )
                  //         ],
                  //       ),
                  //     ),
                  //     Container()
                  //   ]),
                  // )
                ],
              )),
        ));
  }

  // 设备列表
  Widget equipmentList() {
    return Container(
      width: MyScreenUtil.width(243),
      // margin: const EdgeInsets.only(right: (10)),
      padding: EdgeInsets.all(MyScreenUtil.width(10)),
      decoration: BoxDecoration(
        color: Color.fromRGBO(246, 248, 250, 1),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          Expanded(
            flex: 1,
            child: Container(
              margin: const EdgeInsets.only(top: 10),
              child: Obx(
                () => ListView(
                  children: controller.deviceList
                      .asMap()
                      .entries
                      .map((item) {
                        int index = item.key;
                        dynamic deviceListItem = item.value;
                        return deviceItemFun(index, deviceListItem);
                      })
                      .toList()
                      .cast<Widget>(),
                  // children: [
                  //   deviceItemFun()
                  // ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 设备模块
  deviceItemFun(index, deviceListItem) {
    return InkWell(
        onTap: () {
          controller.menuIndexFun(index, deviceListItem);
        },
        child: controller.menuIndex.value == index
            ? Container(
                padding: const EdgeInsets.all(10),
                margin: const EdgeInsets.only(bottom: 10),
                width: MyScreenUtil.width(200),
                height: MyScreenUtil.height(100),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    stops: [0.0, 1.0],
                    colors: [
                      Color.fromRGBO(87, 119, 255, 0.3),
                      Color(0xFF5777FF),
                    ],
                    transform: GradientRotation(250 * 90 / 180),
                  ),
                  color: MyScreenUtil.ThemColor(),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Color.fromRGBO(45, 109, 255, 0.3),
                      offset: Offset(0, 2),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: MyScreenUtil.width(10),
                          height: MyScreenUtil.height(10),
                          margin: const EdgeInsets.only(right: 10),
                          decoration: BoxDecoration(
                              color: deviceListItem['isFinish'] == 0
                                  ? const Color.fromRGBO(255, 98, 99, 1)
                                  : const Color.fromRGBO(52, 196, 71, 1),
                              borderRadius: BorderRadius.circular(10)),
                        ),
                        Text(
                          "${deviceListItem['deviceId']}",
                          style: TextStyle(
                              color: controller.menuIndex.value == index
                                  ? Colors.white
                                  : Colors.black,
                              fontSize: MyScreenUtil.fontSize(18),
                              fontWeight: FontWeight.w600),
                        )
                      ],
                    ),
                    Container(
                      padding: EdgeInsets.fromLTRB(
                          MyScreenUtil.width(16),
                          MyScreenUtil.width(5),
                          MyScreenUtil.width(16),
                          MyScreenUtil.width(5)),
                      margin: const EdgeInsets.only(top: 10),
                      decoration: BoxDecoration(
                          color: Color.fromRGBO(255, 255, 255, 0.1),
                          borderRadius: BorderRadius.circular(16)),
                      child: Text(
                        "${deviceListItem['deviceTypeName']}",
                        style: TextStyle(
                            fontSize: MyScreenUtil.fontSize(12),
                            color: Colors.white),
                      ),
                    )
                  ],
                ),
              )
            : Container(
                padding: const EdgeInsets.all(10),
                margin: const EdgeInsets.only(bottom: 10),
                width: MyScreenUtil.width(200),
                height: MyScreenUtil.height(100),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Color.fromRGBO(45, 109, 255, 0.3),
                      offset: Offset(0, 2),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: MyScreenUtil.width(10),
                          height: MyScreenUtil.height(10),
                          margin: const EdgeInsets.only(right: 10),
                          decoration: BoxDecoration(
                              color: deviceListItem['isFinish'] == 0
                                  ? const Color.fromRGBO(255, 98, 99, 1)
                                  : const Color.fromRGBO(52, 196, 71, 1),
                              borderRadius: BorderRadius.circular(10)),
                        ),
                        Text(
                          "${deviceListItem['deviceId']}",
                          style: TextStyle(
                              color: controller.menuIndex.value == index
                                  ? Colors.white
                                  : Colors.black,
                              fontSize: MyScreenUtil.fontSize(18),
                              fontWeight: FontWeight.w600),
                        )
                      ],
                    ),
                    Container(
                      // alignment: Alignment.topLeft,
                      padding: EdgeInsets.fromLTRB(
                          MyScreenUtil.width(16),
                          MyScreenUtil.width(5),
                          MyScreenUtil.width(16),
                          MyScreenUtil.width(5)),
                      margin: const EdgeInsets.only(top: 10),
                      decoration: BoxDecoration(
                          color: Color.fromRGBO(87, 119, 255, 0.1),
                          borderRadius: BorderRadius.circular(16)),
                      child: Text(
                        "${deviceListItem['deviceTypeName']}",
                        style: TextStyle(
                            fontSize: MyScreenUtil.fontSize(12),
                            color: Color(0xFF5777FF)),
                      ),
                    )
                  ],
                ),
              ));
  }

  // 巡检数据
  Widget inspectData() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              child: Row(
                children: [
                  Container(
                    width: MyScreenUtil.width(24),
                    height: MyScreenUtil.height(24),
                    margin: const EdgeInsets.only(right: 5),
                    child: Image.asset(
                      'assets/images/icon/room_title.png',
                      fit: BoxFit.fill,
                    ),
                  ),
                  // Container(
                  //   margin: const EdgeInsets.only(right: 10),
                  //   child: Text(
                  //     controller.deviceList.isNotEmpty
                  //         ? "${controller.deviceList[controller.menuIndex.value]['deviceTypeName']}"
                  //         : "",
                  //     style: TextStyle(
                  //         fontSize: MyScreenUtil.fontSize(18),
                  //         fontWeight: FontWeight.w600),
                  //   ),
                  // ),
                  Container(
                    child: Text(
                      "巡检数据",
                      style: TextStyle(
                          color: MyScreenUtil.FontColor(),
                          fontSize: MyScreenUtil.fontSize(18),
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              ),
            ),
            Container(
                margin: EdgeInsets.only(left: MyScreenUtil.width(20)),
                child: Row(
                  children: [
                    Container(
                      child: Text(
                        "设备关闭",
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: MyScreenUtil.fontSize(18)),
                      ),
                    ),
                    Switch(
                      activeColor: Color(0xFF5777FF),
                      value: controller.isOpenFun(controller.menuIndex.value),
                      onChanged: (val) {
                        // controller.switchValue.value = val;
                        controller.fillDeviceForm(
                            val, controller.menuIndex.value);
                      },
                      inactiveThumbColor: Colors.red,
                    )
                  ],
                ))
          ],
        ),
        Expanded(
            flex: 1,
            child: Container(
              margin: const EdgeInsets.only(top: 10),
              child: controller.patrolList.isNotEmpty
                  ? SingleChildScrollView(
                      child: Column(
                        children: controller.patrolList
                            .asMap()
                            .entries
                            .map((entry) {
                              int index = entry.key;
                              var item = entry.value;
                              var radioItem = controller.patrolRadioList[entry.key];
                              return Container(
                                key: ValueKey(item.id),
                                padding: const EdgeInsets.all(10),
                                margin: const EdgeInsets.only(bottom: 4),
                                decoration: BoxDecoration(
                                    // border: Border.all(
                                    //     color: Colors.black54, width: 1.0),
                                    borderRadius: BorderRadius.circular(10)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Container(
                                      width: MyScreenUtil.width(250),
                                      child: Row(
                                        children: [
                                          Container(
                                            margin: EdgeInsets.only(
                                                right: MyScreenUtil.width(10)),
                                            // width: MyScreenUtil.width(200),
                                            child: Text("${index + 1}"),
                                          ),
                                          Container(
                                            // width: MyScreenUtil.width(200),
                                            child: Text(
                                              "${item.inspectionName}",
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.w600),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (item.inspectionRangeBegin == "是") ...[
                                      Expanded(
                                        flex: 1,
                                        child: Container(
                                          width: MyScreenUtil.width(125),
                                          child: RadioListTile(
                                            key: Key(
                                                'textfield_${item.formId}$index'),
                                            title: const Text("是"),
                                            value: "是",
                                            onChanged: (value) {
                                              controller.updateDeviceFormData(
                                                  item, value);
                                              controller.findDeviceFormList(
                                                  item.formId);
                                            },
                                            groupValue: radioItem.inputValue,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Container(
                                          width: MyScreenUtil.width(125),
                                          child: RadioListTile(
                                            key: Key(
                                                'textfield_${item.formId}$index'),
                                            title: const Text("否"),
                                            value: "否",
                                            onChanged: (value) {
                                              controller.updateDeviceFormData(
                                                  item, value);
                                              controller.findDeviceFormList(
                                                  item.formId);
                                            },
                                            groupValue: radioItem.inputValue,
                                          ),
                                        ),
                                      ),
                                    ] else if (item.inspectionRangeBegin ==
                                        "有") ...[
                                      Expanded(
                                        flex: 1,
                                        child:Container(
                                          width: MyScreenUtil.width(125),
                                          child: RadioListTile(
                                            key: Key(
                                                'textfield_${item.formId}$index'),
                                            title: const Text("有"),
                                            value: "有",
                                            onChanged: (value) {
                                              controller.updateDeviceFormData(
                                                  item, value);
                                              controller.findDeviceFormList(
                                                  item.formId);
                                            },
                                            groupValue: radioItem.inputValue,
                                          ),
                                        ),
                                         
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Container(
                                          width: MyScreenUtil.width(125),
                                          child: RadioListTile(
                                            key: Key(
                                                'textfield_${item.formId}$index'),
                                            title: const Text("无"),
                                            value: "无",
                                            onChanged: (value) {
                                              controller.updateDeviceFormData(
                                                  item, value);
                                              controller.findDeviceFormList(
                                                  item.formId);
                                            },
                                            groupValue: radioItem.inputValue,
                                          ),
                                        ),
                                      ),
                                    ] else ...[
                                      Expanded(
                                          flex: 1,
                                          child: Row(
                                            children: [
                                              // Container(
                                              //   margin: const EdgeInsets.only(
                                              //       right: 10),
                                              //   child: const Text("设备数值"),
                                              // ),
                                              Container(
                                                width: MyScreenUtil.width(300),
                                                padding: EdgeInsets.only(
                                                    left:
                                                        MyScreenUtil.width(16),
                                                    right:
                                                        MyScreenUtil.width(16)),
                                                decoration: BoxDecoration(
                                                    border: Border.all(
                                                        color: Colors.black54,
                                                        width: 1.0),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10)),
                                                child: Obx(() => TextField(
                                                      controller: controller
                                                              .patrolControllerList[
                                                          index],
                                                      focusNode: controller
                                                          .focusNodeList[index],
                                                      keyboardType: controller
                                                              .keyBoard.value
                                                          ? TextInputType.none
                                                          : TextInputType
                                                              .number,
                                                      decoration: InputDecoration(
                                                          hintText:
                                                              "设备值范围: ${item.inspectionRangeBegin ?? ""}~${item.inspectionRangeEnd ?? ""}",
                                                          border: InputBorder
                                                              .none //去掉下划线
                                                          ),
                                                      onChanged: (value) {
                                                        controller.updateDeviceFormData(controller.patrolList[index],value);
                                                      },
                                                    )),
                                              ),
                                            ],
                                          ))
                                    ]
                                    // Expanded(
                                    //     flex: 1,
                                    //     child: Container(
                                    //         width: MyScreenUtil.width(150),
                                    //         child: Row(
                                    //           children: [
                                    //             Container(
                                    //               width: MyScreenUtil.width(
                                    //                   80),
                                    //               child: RadioListTile(
                                    //                   key: Key(
                                    //                       'textfield_${item.formId}$index'),
                                    //                   title:
                                    //                       const Text("是"),
                                    //                   value: "是",
                                    //                   onChanged: (value) {
                                    //                     controller
                                    //                         .updateDeviceFormData(
                                    //                             item,
                                    //                             value);
                                    //                     controller
                                    //                         .findDeviceFormList(
                                    //                             item.formId);
                                    //                   },
                                    //                   groupValue:
                                    //                       item.inputValue),
                                    //             ),
                                    //             // Container(
                                    //             //   width: MyScreenUtil.width(
                                    //             //       80),
                                    //             //   child: RadioListTile(
                                    //             //       key: Key(
                                    //             //           'textfield_${item.formId}$index'),
                                    //             //       title:
                                    //             //           const Text("否"),
                                    //             //       value: "否",
                                    //             //       onChanged: (value) {
                                    //             //         controller
                                    //             //             .updateDeviceFormData(
                                    //             //                 item,
                                    //             //                 value);
                                    //             //         controller
                                    //             //             .findDeviceFormList(
                                    //             //                 item.formId);
                                    //             //       },
                                    //             //       groupValue:
                                    //             //           item.inputValue),
                                    //             // ),
                                    //           ],
                                    //         )))
                                  ],
                                ),
                              );
                            })
                            .toList()
                            .cast<Widget>(),
                      ),
                    )
                  : Container(),
            ))
      ],
    );
  }
}

class NumberButton extends StatelessWidget {
  final String number;
  final Function(String) onPressed;

  NumberButton(this.number, this.onPressed);

  @override
  Widget build(BuildContext context) {
    return Container(
        width: MyScreenUtil.width(101),
        height: MyScreenUtil.height(50),
        child: ElevatedButton(
          style: ButtonStyle(
            backgroundColor:
                MaterialStateProperty.all<Color>(Color(0xFFF6F8FA)),
          ),
          child: Text(
            number,
            style: TextStyle(
                fontSize: MyScreenUtil.fontSize(24), color: Color(0xFF5777FF)),
          ),
          onPressed: () {
            onPressed(number);
          },
        ));
  }
}

class BackButton extends StatelessWidget {
  final String number;
  final Function(String) onPressed;

  BackButton(this.number, this.onPressed);

  @override
  Widget build(BuildContext context) {
    return Container(
        width: MyScreenUtil.width(160),
        height: MyScreenUtil.height(50),
        child: ElevatedButton(
          style: ButtonStyle(
            backgroundColor:
                MaterialStateProperty.all<Color>(Color(0xFFF6F8FA)),
          ),
          child: Container(
              width: MyScreenUtil.width(39),
              height: MyScreenUtil.height(28),
              child: Image.asset(
                'assets/images/icon/delete.png',
                fit: BoxFit.fill,
              )),
          onPressed: () {
            onPressed(number);
          },
        ));
  }
}

class EnterButton extends StatelessWidget {
  final String number;
  final Function(BuildContext) onPressed;

  EnterButton(this.number, this.onPressed);

  @override
  Widget build(BuildContext context) {
    return Container(
        width: MyScreenUtil.width(160),
        height: MyScreenUtil.height(50),
        child: ElevatedButton(
          style: ButtonStyle(
            backgroundColor:
                MaterialStateProperty.all<Color>(Color(0xFFF6F8FA)),
          ),
          child: Text(
            number,
            style: TextStyle(
                fontSize: MyScreenUtil.fontSize(24),
                color: MyScreenUtil.FontColor()),
          ),
          onPressed: () {
            onPressed(context);
          },
        ));
  }
}
