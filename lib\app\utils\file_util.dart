import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:http_parser/http_parser.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/toast.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/logger.dart';

class FileUtil {

  static const String _irTempImageDir = "/sjzx/images/";

  static Future<List<MultipartFile>> createMultipartFiles(List<File> files) async {
    final multipartFiles = <MultipartFile>[];
    for (final file in files) {
      final fileBytes = await file.readAsBytes();
      final multipartFile = MultipartFile.fromBytes(
        fileBytes,
        filename: file.path.split('/').last,
        contentType: MediaType('application', 'octet-stream'),
      );
      multipartFiles.add(multipartFile);
    }
    return multipartFiles;
  }

  static Future<String> createBase64FromUrl(String filePath) async {
    logger('createBase64FromUrl ====> $filePath');
    final Directory directory = await getTemporaryDirectory();
    final Directory imageDirectory = await Directory('${directory.path}$_irTempImageDir').create(recursive: true);
    String _targetPath = imageDirectory.path;
    File file = File('${_targetPath}_${DateTime.now().millisecondsSinceEpoch}.jpeg');
    try{
      var compressFile = await FlutterImageCompress.compressAndGetFile(filePath , file.path, quality: 20);
      var newUnit8List = await compressFile?.readAsBytes();
      var base_string = base64.encode(newUnit8List?.toList() ?? []);
      return base_string;
    }catch(e){
      logger(e);
      toast("${filePath} 图片不存在");
      return "";
    }

  }

  static Future delTempImages () async {
    final Directory directory = await getTemporaryDirectory();
    final Directory imageDirectory = await Directory('${directory.path}$_irTempImageDir').create(recursive: true);
    String targetPath = imageDirectory.path;
    return await Directory(targetPath).delete(recursive: true);
  }

  static Future deleteFileByPath(String path) async {
    return await Directory(path).delete(recursive: true);
  }

}