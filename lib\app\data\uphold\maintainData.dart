
class MaintainData{
  final String? woid;
  final String stepName;
  final String? id;
  final String? parentName;
  final String? parentId;
  final String? content;
  final String? operation;
  final String? operatorId;
  final String?  operatorName;
  final int?  operatorResult;
  final int?  operatorTime;
  final String?  supervisorId;
  final String?  supervisorName;
  final int?  supervisorResult;
  final int? supervisorTime;
  final int? fillingValue;
  final int? amount;
  final String? gapPrice;
  final int? status;
  final String? gapPicture;
  final int? fillingPictureValue;


  const MaintainData({
    required this.woid,
    required this.stepName,
    required this.id,
    this.parentName,
    this.parentId,
    required this.content,
    required this.operation,
    required this.operatorId,
    required this.operatorName,
    required this.operatorResult,
    required this.operatorTime,
    required this.supervisorId,
    required this.supervisorName,
    required this.supervisorResult,
    required this.supervisorTime,
    this.fillingValue,
    this.amount,
    this.gapPrice,
    this.status, 
    this.gapPicture,
    this.fillingPictureValue,
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      "woid":woid,
      "stepName":stepName,
      "id":id,
      "parentName":parentName,
      "parentId":parentId,
      "content":content,
      "operation":operation,
      "operatorId":operatorId,
      "operatorName":operatorName,
      "operatorResult":operatorResult,
      "operatorTime":operatorTime,
      "supervisorId":supervisorId,
      "supervisorName":supervisorName,
      "supervisorResult":supervisorResult,
      "supervisorTime":supervisorTime,
      "fillingValue":fillingValue,
      "amount":amount,
      "gapPrice":gapPrice,
      "status":status,
      "gapPicture":gapPicture,
      "fillingPictureValue":fillingPictureValue
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  // @override
  Map<String, dynamic> toJson() {
    return {
      "woid":woid,
      "stepName":stepName,
      "id":id,
      "parentName":parentName,
      "parentId":parentId,
      "content":content,
      "operation":operation,
      "operatorId":operatorId,
      "operatorName":operatorName,
      "operatorResult":operatorResult,
      "operatorTime":operatorTime,
      "supervisorId":supervisorId,
      "supervisorName":supervisorName,
      "supervisorResult":supervisorResult,
      "supervisorTime":supervisorTime,
      "fillingValue":fillingValue,
      "amount":amount,
      "gapPrice":gapPrice,
      "status":status,
      "gapPicture":gapPicture,
      "fillingPictureValue":fillingPictureValue
    };
  }
}