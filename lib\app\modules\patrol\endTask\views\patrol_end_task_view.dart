import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import '../../../../utils/screenutil.dart';
import '../controllers/patrol_end_task_controller.dart';

class PatrolEndTaskView extends GetView<PatrolEndTaskController> {
  const PatrolEndTaskView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          // 在这里执行你想要的操作
          print('Back button pressed on APage');
          return true; // 返回true表示允许返回到上一个页面
        },
        child: Scaffold(
            body: Container(
          padding: EdgeInsets.all(MyScreenUtil.width(24)),
          color: Color.fromRGBO(246, 248, 250, 1),
          child: ListView(
            children: [
              InkWell(
                onTap: () => {Get.back()},
                child: Container(
                  padding: EdgeInsets.only(
                      left: MyScreenUtil.width(24),
                      right: MyScreenUtil.width(24)),
                  // alignment: Alignment.centerLeft, // 设置垂直居中
                  height: MyScreenUtil.height(60),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: MyScreenUtil.width(24),
                        height: MyScreenUtil.height(24),
                        child: Image.asset(
                          "assets/images/icon/left.png",
                          fit: BoxFit.cover,
                        ),
                      ),
                      Text('结束巡检')
                    ],
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 20),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.all(MyScreenUtil.height(24)),
                      width: MyScreenUtil.width(1032),
                      height: MyScreenUtil.height(708),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          // 房间了类型
                          Container(
                            child: Row(
                              children: [
                                Container(
                                  width: MyScreenUtil.width(5),
                                  height: MyScreenUtil.height(20),
                                  margin: const EdgeInsets.only(right: 13),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(14.0),
                                    color: Color(0xFF5777FF),
                                  ),
                                ),
                                Text(
                                  "房间类型",
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(25, 29, 38, 1),
                                      fontSize: MyScreenUtil.fontSize(18),
                                      fontWeight: FontWeight.w600),
                                )
                              ],
                            ),
                          ),
                          roomType()
                        ],
                      ),
                    ),
                    Container(
                      width: MyScreenUtil.width(336),
                      // height: MyScreenUtil.height(708),
                      margin: EdgeInsets.only(left: MyScreenUtil.width(20)),
                      decoration: BoxDecoration(
                        // color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Column(
                        children: [
                          Container(
                            padding: EdgeInsets.all(MyScreenUtil.height(24)),
                            decoration: BoxDecoration(
                              color: Color.fromRGBO(246, 248, 250, 1),
                              borderRadius: BorderRadius.circular(14.0),
                              // color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Color.fromRGBO(0, 0, 0, 0.1),
                                  offset: Offset(0, 0),
                                  blurRadius: 20,
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                Container(
                                  child: Row(
                                    children: [
                                      Container(
                                        width: MyScreenUtil.width(5),
                                        height: MyScreenUtil.height(20),
                                        margin:
                                            const EdgeInsets.only(right: 13),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(14.0),
                                          color: Color(0xFF5777FF),
                                        ),
                                      ),
                                      Text(
                                        "巡检信息",
                                        style: TextStyle(
                                            color: const Color.fromRGBO(
                                                25, 29, 38, 1),
                                            fontSize: MyScreenUtil.fontSize(18),
                                            fontWeight: FontWeight.w600),
                                      )
                                    ],
                                  ),
                                ),
                                // 巡检信息
                                Container(
                                  margin: const EdgeInsets.only(top: 33),
                                  // height: MyScreenUtil.height(120),
                                  child: userInfo(),
                                ),
                              ],
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              controller.endPatol();
                            },
                            child: Container(
                              alignment: Alignment.center,
                              width: MyScreenUtil.width(336),
                              height: MyScreenUtil.height(120),
                              margin:
                                  EdgeInsets.only(top: MyScreenUtil.height(24)),
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(
                                      "assets/images/over.png"),
                                  fit: BoxFit.cover,
                                ),
                              ),
                              child: Text(
                                '                    结束巡检',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        )));
  }

  // 巡检信息
  Widget userInfo() {
    return Obx(() => Column(
          children: [
            // 头像
            Container(
              width: MyScreenUtil.width(80),
              height: MyScreenUtil.height(80),
              decoration: BoxDecoration(
                  // color: MyScreenUtil.ThemColor(),
                  borderRadius: BorderRadius.circular(100),
                  image: controller.userInfo.value.avatar == ""
                      ? const DecorationImage(
                          image: AssetImage("assets/images/userLogo.png"),
                          fit: BoxFit.cover)
                      : DecorationImage(
                          image: NetworkImage(controller.userInfo.value.avatar),
                          fit: BoxFit.cover)),
            ),
            // 用户信息A栏
            Container(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                      child: Text(
                    controller.userInfo.value.name,
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(18),
                      color: MyScreenUtil.FontColor(),
                    ),
                  )),
                  Container(
                    margin: const EdgeInsets.only(top: 20),
                    child: Column(
                      children: [
                        Container(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width:
                                    MyScreenUtil.width(MyScreenUtil.width(20)),
                                height: MyScreenUtil.height(
                                    MyScreenUtil.height(20)),
                                margin: EdgeInsets.only(
                                    right: MyScreenUtil.width(12)),
                                child:
                                    Image.asset("assets/images/icon/time.png"),
                              ),
                              SizedBox(
                                  child: Text(
                                "开始时间",
                                style: TextStyle(
                                    fontSize: MyScreenUtil.fontSize(
                                        MyScreenUtil.fontSize(20))),
                              )),
                            ],
                          ),
                        ),
                        Obx(
                          () => SizedBox(
                            child: Text(
                              controller.userInfo.value.patrolStartTime ?? "",
                              style: TextStyle(
                                color: Color.fromRGBO(134, 144, 156, 1),
                                fontSize: MyScreenUtil.fontSize(18),
                              ),
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: MyScreenUtil.width(16)),
                          height: MyScreenUtil.height(30),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width:
                                    MyScreenUtil.width(MyScreenUtil.width(20)),
                                height: MyScreenUtil.height(
                                    MyScreenUtil.height(20)),
                                margin: EdgeInsets.only(
                                    right: MyScreenUtil.width(12)),
                                child:
                                    Image.asset("assets/images/icon/time.png"),
                              ),
                              SizedBox(
                                  child: Text(
                                "结束时间",
                                style: TextStyle(
                                    fontSize: MyScreenUtil.fontSize(
                                        MyScreenUtil.fontSize(20))),
                              )),
                            ],
                          ),
                        ),
                        Obx(
                          () => SizedBox(
                            child: Text(
                              controller.userInfo.value.patrolEndTime??'',
                              style: TextStyle(
                                color: Color.fromRGBO(134, 144, 156, 1),
                                fontSize: MyScreenUtil.fontSize(18),
                              ),
                            ),
                          ),
                        ),
                        Container(
                            width: MyScreenUtil.width(284),
                            padding: EdgeInsets.all(MyScreenUtil.width(24)),
                            margin:
                                EdgeInsets.only(top: MyScreenUtil.height(16)),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Column(
                              children: [
                                Container(
                                  child: Row(
                                    children: [
                                      Container(
                                        width: MyScreenUtil.width(24),
                                        height: MyScreenUtil.height(24),
                                        child: Image.asset(
                                          "assets/images/icon/position_blue.png",
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      Text(
                                        controller.userInfo.value.occupation ??
                                            '暂无',
                                        style: TextStyle(
                                            color: MyScreenUtil.FontColor()),
                                      )
                                    ],
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                            top: MyScreenUtil.height(16)),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: MyScreenUtil.width(24),
                                        height: MyScreenUtil.height(24),
                                        child: Image.asset(
                                          "assets/images/icon/department_blue.png",
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      Text(
                                        controller.userInfo.value.floor ?? '暂无',
                                        style: TextStyle(
                                            color: MyScreenUtil.FontColor()),
                                      )
                                    ],
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                            top: MyScreenUtil.height(16)),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: MyScreenUtil.width(24),
                                        height: MyScreenUtil.height(24),
                                        child: Image.asset(
                                          "assets/images/icon/speciality_blue.png",
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      Text(
                                        controller
                                                .userInfo.value.technicalPost ??
                                            '暂无',
                                        style: TextStyle(
                                            color: MyScreenUtil.FontColor()),
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ))
                      ],
                    ),
                  ),
                  // Container(
                  //   margin: const EdgeInsets.only(top: 20),
                  //   width: MyScreenUtil.width(300),
                  //   child: Row(
                  //     children: [
                  //       SizedBox(
                  //           width: MyScreenUtil.width(100),
                  //           child: Text(
                  //             "巡查楼层",
                  //             style: TextStyle(
                  //                 fontSize: MyScreenUtil.fontSize(18)),
                  //           )),
                  //       SizedBox(
                  //           child: Text(
                  //         controller.userInfo.value.floor,
                  //         style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),
                  //       ))
                  //     ],
                  //   ),
                  // ),
                ],
              ),
            ),
            // 用户信息B栏
            // Container(
            //     margin: const EdgeInsets.only(left: 20),
            //     child: Column(
            //       mainAxisAlignment: MainAxisAlignment.start,
            //       children: [
            //         Container(
            //           width: MyScreenUtil.width(300),
            //           child: Row(
            //             children: [
            //               Container(
            //                   width: MyScreenUtil.width(100),
            //                   child: Text(
            //                     "职位",
            //                     style: TextStyle(
            //                         fontSize: MyScreenUtil.fontSize(18)),
            //                   )),
            //               SizedBox(
            //                   child: Text(
            //                 controller.userInfo.value.occupation ?? '暂无',
            //                 style:
            //                     TextStyle(fontSize: MyScreenUtil.fontSize(18)),
            //               ))
            //             ],
            //           ),
            //         ),
            //         Container(
            //           width: MyScreenUtil.width(300),
            //           margin: const EdgeInsets.only(top: 20),
            //           child: Row(
            //             children: [
            //               Container(
            //                   width: MyScreenUtil.width(100),
            //                   child: Text(
            //                     "专业",
            //                     style: TextStyle(
            //                         fontSize: MyScreenUtil.fontSize(18)),
            //                   )),
            //               SizedBox(
            //                   child: Text(
            //                 controller.userInfo.value.technicalPost ?? "暂无",
            //                 style:
            //                     TextStyle(fontSize: MyScreenUtil.fontSize(18)),
            //               ))
            //             ],
            //           ),
            //         ),
            //       ],
            //     )),
            // Container(
            //   child: InkWell(
            //     onTap: () {
            //       Get.delete<PatrolStartController>();
            //       // 创建新的控制器实例
            //       Get.put(PatrolStartController());
            //       controller.searchCode();
            //     },
            //     child: Column(
            //       children: [
            //         Container(
            //           width: MyScreenUtil.width(90),
            //           height: MyScreenUtil.height(90),
            //           // margin: EdgeInsets.only(bottom: MyScreenUtil.width(20)),
            //           child: Image.asset(
            //             "assets/images/searchCode.png",
            //             fit: BoxFit.cover,
            //           ),
            //         ),
            //         Container(child: Text('扫一扫房间码')),
            //       ],
            //     ),
            //   ),
            // ),
          ],
        ));
  }

  // 房间类型
  Widget roomType() {
    return Obx(() => Container(
          margin: const EdgeInsets.only(top: 20),
          width: MyScreenUtil.width(1032),
          height: MyScreenUtil.height(MyScreenUtil.height(280)),
          child: ListView(
              scrollDirection: Axis.horizontal,
              children: controller.roomTypeInfoList.map((item) {
                return Container(
                  padding: EdgeInsets.only(
                      left: MyScreenUtil.width(20),
                      right: MyScreenUtil.width(20)),
                  margin: EdgeInsets.fromLTRB(5, 5, 20, 5),
                  width: MyScreenUtil.width(300),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Color.fromRGBO(0, 0, 0, 0.1),
                          offset: Offset(0, 0),
                          blurRadius: 5,
                          spreadRadius: 0,
                        ),
                      ],
                      borderRadius:
                          BorderRadius.circular(MyScreenUtil.radius(16))),
                  child: Column(
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: MyScreenUtil.height(20)),
                        alignment: Alignment.centerLeft,
                        child: Container(
                            child: Row(
                          children: [
                            Container(
                              width: MyScreenUtil.width(24),
                              height: MyScreenUtil.height(24),
                              child: Image.asset(
                                "assets/images/icon/room_title.png",
                                fit: BoxFit.cover,
                              ),
                            ),
                            Text(
                              "${item.roomTypeName}",
                              style: TextStyle(
                                  color: MyScreenUtil.FontColor(),
                                  fontSize: MyScreenUtil.fontSize(18),
                                  fontWeight: FontWeight.w600),
                            ),
                          ],
                        )),
                      ),
                      Container(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            margin:
                                EdgeInsets.only(top: MyScreenUtil.height(20)),
                            alignment: Alignment.centerLeft,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                    child: Text(
                                  "房间数量: ${item.roomCount}",
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(25, 29, 38, 1),
                                      fontSize: MyScreenUtil.fontSize(18)),
                                )),
                                Container(
                                    margin: EdgeInsets.only(top: 16),
                                    child: Text(
                                      "设备数量: ${item.deviceCount}",
                                      style: TextStyle(
                                          color: const Color.fromRGBO(
                                              25, 29, 38, 1),
                                          fontSize: MyScreenUtil.fontSize(18)),
                                    )),
                              ],
                            ),
                          ),
                          Container(
                            width: 80,
                            height: 80,
                            child: new CircularPercentIndicator(
                              radius: 30.0,
                              lineWidth: 5.0,
                              percent: double.parse(item.progress)/100,
                              center: new Text("${item.progress}%",),
                              progressColor: Color(0xFF5777FF),
                            ),
                          )
                        ],
                      )),
                      Row(
                        children: [
                          Expanded(
                              flex: 1,
                              child: Container(
                                  height: MyScreenUtil.height(30),
                                  margin: EdgeInsets.only(
                                      top: MyScreenUtil.height(30)),
                                  child: ElevatedButton(
                                      onPressed: () {
                                        Get.toNamed("/check-room", arguments: {
                                          "roomTypeName": item.roomTypeName,
                                          "roomType": item.roomType
                                        });
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            MyScreenUtil.ThemColor(), // 背景颜色
                                        disabledBackgroundColor:
                                            const Color.fromRGBO(
                                                143, 147, 153, 1),
                                      ),
                                      child: Text(
                                        item.progress == "100"
                                            ? "巡检完成"
                                            : '进入巡检',
                                        style: const TextStyle(
                                            color: Colors.white),
                                      ))))
                        ],
                      ),
                    ],
                  ),
                );
              }).toList()),
        ));
  }
}
