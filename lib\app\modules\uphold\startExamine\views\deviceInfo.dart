import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';

import '../controllers/uphold_start_examine_controller.dart';

class DeviceInfo extends GetView<UpholdStartExamineController> {
  const DeviceInfo({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Container(
            child: Column(
              children: controller.deviceInfoData['details'].map((item){
                return rowSectionFun(item['content']??'',item['operation']??'');
              }).toList().cast<Widget>()
            ),
          ),

          Row(
            children: [
              Expanded(
                flex: 1,
                child: Container(
                  margin: EdgeInsets.only(top: MyScreenUtil.height(20)),
                  height: MyScreenUtil.height(52),
                  child: ElevatedButton(
                    onPressed:(){
                      controller.handelComponentIndex(1);
                    },
                    child: const Text("下一项"),
                  ),
                )
              )
            ],
          )
        ],
      ),
    );
  }

  rowSectionFun(title,doc){
    return Container(
      padding: EdgeInsets.only(bottom: MyScreenUtil.height(20),top:MyScreenUtil.height(20) ),
      decoration:const BoxDecoration(
        border: Border(
          bottom: BorderSide (
            color: const Color.fromRGBO(238, 238, 238, 1),
            width: 1
          )
        )
      ),
      child:  Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            margin: EdgeInsets.only(right: MyScreenUtil.width(20)),
            child: Text(title),
          ),
          Expanded(
            flex: 1,
            child: Container(
              alignment: Alignment.centerRight,
              child: Text(doc),
            )
          ),
        ],
      )
    );
  }

}
