import 'dart:convert';
import 'dart:ffi';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/controllers/global_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/model/speciality/speciality.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

import '../data/dataVersion.dart';
import "../data/room.dart";
import "../data/device.dart";
import '../data/formSQData.dart';
import "../data/deviceForm.dart";
import '../data/user.dart';
import "../data/roomTypeInfo.dart";
import "../data/history.dart";

class SQfliteController extends GetxController {
  late Database _database;

  @override
  void onInit() async {
    print('初始化存储');
    super.onInit();
    await _initDatabase();
  }

  Future<void> _initDatabase() async {
    final databasePath = await getDatabasesPath();
    final path = join(databasePath, 'test1.db');
    _database = await openDatabase(
      path,
      version: 2,
      onCreate: (db, version) async {
        /**
         * 创建 user 存放用户信息
         */
        await db.execute('''
          CREATE TABLE user (
            eid TEXT PRIMARY KEY, -- 账号and用户ID
            createTime INTEGER NULL, -- 开始时间(暂存)
            avatar TEXT, -- 头像预留
            deptName TEXT NULL, -- 部门
            floor TEXT, --楼号
            companyName TEXT, -- 公司名称
            companyId TEXT, --公司id
            gender INTEGER NULL, -- 性别 0表示男 1表示女
            livingPlace TEXT, --地址
            name TEXT, --用户名
            occupation TEXT NULL, --职位
            phone TEXT, -- 手机号
            passWorld TEXT NULL, --- 密码
            roomType TEXT, --房间类型
            roomTypeName TEXT NULL, --房间类型
            technicalPost TEXT NUll, -- 专业
            shifts TEXT, --班组
            patrolStartTime TEXT NUll, -- 巡检开始时间
            patrolEndTime TEXT NUll, -- 巡检结束时间
            patrolStartTimeStamp INTEGER, -- 巡检开始时间戳
            patrolEndTimeStamp INTEGER, -- 巡检结束时间戳
            isUpload INTEGER, -- 是否上传 0代表未上传 1代表已上传
            deviceMaintainPer INTEGER, -- 是否有维护工单权限
            isLogin INTEGER -- 是否登录
          );
        ''');

        /**
         *  创建 dataVersion 存放本地数据的版本号
         */
        await db.execute('''
          CREATE TABLE dataVersion (
            id INTEGER PRIMARY KEY, -- 主键ID
            version TEXT -- 本地数据的版本号
          );
        ''');

        /**
         * 创建 roomTypeInfo 用于存放该用户下面的房间类型
         */
        await db.execute('''
          CREATE TABLE roomTypeInfo (
            roomType TEXT, -- 房间类型
            roomTypeName TEXT , -- 房间类型名称
            roomCount INTEGER, -- 房间数量
            deviceCount INTEGER, -- 设备数量
            progress TEXT, -- 该房间下面的所有室的巡检进度
            userId TEXT NULL-- userId
          );
        ''');

        /**
         * 创建Room存放房间数据
         */
        await db.execute('''
          CREATE TABLE room (
            roomId TEXT, -- 房间id
            name TEXT , -- 房间名称
            storey INTEGER, -- 楼层
            deviceCount INTEGER, -- 房间设备数
            note TEXT, -- 备注
            type TEXT, -- 类型用于结合roomId查找当前房间下面的设备
            isTaste INTEGER NULL, -- 房间是否有异味0表示没有1表示有
            isSound INTEGER NULL, -- 房间是否有响声0表示没有1表示有
            isFinish INTEGER, -- 用于记录该房间是否巡检完成0表示未完成1表示已完成
            goTime INTEGER NULL, -- 首次进入房间时间
            sortRank INTEGER NULL, -- 排列顺序
            userId TEXT NULL-- userId
          );
        ''');
        /**
         * 创建 device 存放全部设备数据
         */
        await db.execute('''
          CREATE TABLE device (
            deviceId TEXT, -- 设备id
            formId TEXT, -- 自定义id 用于关联deviceForm表中设备的巡检表单deviceId+roomId+roomType
            deviceTypeName TEXT , -- 设备名称
            deviceType TEXT, -- 设备类型
            deviceCode TEXT, -- 设备编号
            roomId TEXT, -- 房间id
            roomType TEXT, --房间类型
            isFinish INTEGER, --  自定义字段 用于记录该设备是否巡检完成 0表示未完成 1表示已完成
            isOpen INTEGER, -- 自定义字段 用于用于记录当前设备是否开启 0表示未开启 1表示已开启
            userId TEXT NULL-- userId
          );
        ''');

        /**
         * 创建 form 存放设备的所有巡检项
         */
        await db.execute('''
          CREATE TABLE form (
            name TEXT, -- 设备名称
            deviceTypeId TEXT, -- 设备类型id
            inputType TEXT, -- 输入类型
            outputType TEXT, -- 输入类型
            inspectionName TEXT, -- 巡检项名称
            inspectionRangeBegin TEXT NULL, -- 巡检项最小范围值
            inspectionRangeEnd TEXT NULL, -- 巡检项最大范围值
            userId TEXT NULL-- userId
          );
        ''');

        /**创建 deviceForm 存放设备关联后的巡检项 */
        await db.execute('''
          CREATE TABLE deviceForm (
            id INTEGER PRIMARY KEY AUTOINCREMENT, -- 自定义id 设备的唯一标识用于页面渲染是指定一个key避免使用缓存
            name TEXT, -- 设备名称
            formId TEXT, -- -- 自定义id 用于关联device表中设备的巡检表单deviceId+roomId+roomType
            deviceTypeId TEXT, -- 设备类型id
            inputType TEXT, -- 输入类型
            outputType TEXT, -- 输入类型
            inspectionName TEXT, -- 巡检项名称
            inspectionRangeBegin TEXT NULL, -- 巡检项最小范围值
            inspectionRangeEnd TEXT NULL, -- 巡检项最大范围值
            inputValue TEXT NULL, -- 巡检项的表单值
            inputActive INTEGER NULL,-- 表单输入的状态 0正在输入 1已保存 null未保存
            userId TEXT NULL-- userId
          );
        ''');

        /**创建 history 表存放巡检上传后的记录*/
        await db.execute('''
          CREATE TABLE history (
            eid TEXT, -- 账号and用户ID
            mobile TEXT, -- 手机号
            name TEXT , -- 用户名称
            avatar TEXT, -- 头像
            patrolStartTime TEXT, -- 巡检开始时间
            patrolEndTime TEXT, -- 巡检结束时间
            patrolStartTimeStamp INTEGER, -- 巡检开始时间戳
            patrolEndTimeStamp INTEGER, -- 巡检结束时间戳
            userId TEXT NULL-- userId
          );
        ''');

        /**创建 专业类型 表存放巡检上传后的记录*/
        await db.execute('''
          CREATE TABLE speciality (
            roomType Text, -- 专业Id
            roomTypeName Text, -- 专业名称
            deviceCount INTEGER -- 设备数量
          );
        ''');

      },

      onUpgrade: (db ,  oldVersion, newVersion ) async {
        if(oldVersion < 2){
          await alertV2(db);
        }
      }
    );
  }

  /// 版本号2修改： test1.db 相关的记录表如下： 添加userId，进行关联绑定
  /// --- ，
  Future<dynamic> alertV2(Database db) async {
    await db.execute("ALTER TABLE device ADD COLUMN userId TEXT NULL;");
    await db.execute("ALTER TABLE deviceFrom ADD COLUMN userId TEXT NULL;");
    await db.execute("ALTER TABLE history ADD COLUMN userId TEXT NULL;");
    await db.execute("ALTER TABLE room ADD COLUMN userId TEXT NULL;");
    await db.execute("ALTER TABLE roomTypeInfo ADD COLUMN userId TEXT NULL;");
    await db.execute("ALTER TABLE user ADD COLUMN isLogin INTEGER;"); // 添加是否登录字段， 1已登录 0 未登录
  }

  /*用户数据 */
  Future<void> insertUsers(List<UserInfo>users) async {
    print(jsonEncode(users));
    final db = await _database;
    final batch = db.batch();
    for (final user in users) {
      batch.insert(
        'user',
        user.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }
  Future<List<UserInfo>> findUsers(sql) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query('user',where:sql);
    return List.generate(maps.length, (i) {
      return UserInfo(
        eid:maps[i]["eid"],
        createTime:maps[i]["createTime"],
        deptName:maps[i]["deptName"],
        floor:maps[i]["floor"],
        gender:maps[i]["gender"],
        livingPlace:maps[i]["livingPlace"],
        name:maps[i]["name"],
        occupation:maps[i]["occupation"],
        phone:maps[i]["phone"],
        passWorld:maps[i]["passWorld"],
        roomType:maps[i]["roomType"],
        roomTypeName:maps[i]["roomTypeName"],
        technicalPost:maps[i]["technicalPost"],
        shifts:maps[i]["shifts"],
        avatar:maps[i]["avatar"],
        patrolStartTime: maps[i]["patrolStartTime"],
        patrolEndTime: maps[i]["patrolEndTime"],
        patrolStartTimeStamp: maps[i]["patrolStartTimeStamp"],
        patrolEndTimeStamp: maps[i]["patrolEndTimeStamp"],
        isUpload: maps[i]["isUpload"],
        companyName:maps[i]["companyName"],
        companyId:maps[i]["companyId"],
        deviceMaintainPer:maps[i]["deviceMaintainPer"] ??0
      );
    });
  }

  /*版本号*/
  Future<void> insertDataVersion(List<DataVersion> dataVersions) async {
    final db = await _database;
    final batch = db.batch();
    for (final dataVersion in dataVersions) {
      batch.insert(
        'dataVersion',
        dataVersion.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }
  Future<List<DataVersion>> findDataVersion(sql) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query('dataVersion',where: sql);
    return List.generate(maps.length, (i) {
      return DataVersion(
        version:maps[i]["version"],
        id:maps[i]["id"],
      );
    });
  }
  

  /**房间类型数据 */
  // 插入数据
  Future<void> insertRoomTypeInfo(List<RoomTypeInfo> roomTypeInfos , {String? userId}) async {
    final db = await _database;
    final batch = db.batch();
    for (final roomTypeInfo in roomTypeInfos) {
      roomTypeInfo.userId = userId;
      batch.insert(
        'roomTypeInfo',
        roomTypeInfo.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }
  Future<List<RoomTypeInfo>> findRoomTypeInfo(sql) async {
    final db = await _database;
    late List<Map<String, dynamic>> maps;

    if(Get.isRegistered<GlobalController>()){
      GlobalController globalController = Get.find();
      var userId = globalController.userInfo?.value?.data?.eid;
      var whereUserId = "userId='${userId}'";
      sql = whereUserId;
    }
    maps = await db.query('roomTypeInfo',where:sql);

    // if(sql==''){
    //   maps = await db.query('roomTypeInfo');
    // }else{
    // }
    return List.generate(maps.length, (i) {
      return RoomTypeInfo(
        roomType: maps[i]["roomType"],
        roomTypeName: maps[i]["roomTypeName"],
        roomCount: maps[i]["roomCount"],
        deviceCount: maps[i]["deviceCount"],
        progress: maps[i]["progress"]
      );
    });
  }



  /*房间数据*/
  // 插入数据
  /*Future<void> insertRoom(room) async {
    final db = await _database;
    await db.insert(
      'room',
      room.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }*/

  /// 查询当前已登录
  Future<UserInfo?> queryCurrentLoginedUser() async {
      final db = await _database;
      List userList = await db.query('user' , where: "where isLogin=1");
      if(userList.isEmpty) return null;
      return userList[0];
  }

  // 批量插入房间数据
  Future<void> insertRooms(List<Room> rooms, {String? userId}) async {
    final db = await _database;
    final batch = db.batch();
    for (final room in rooms) {
      room.userId = userId;
      batch.insert(
        'room',
        room.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }


  // 批量插入专业数据
  Future<void> insertspeciality(List<Speciality> specialitys) async {
    final db = await _database;
    final batch = db.batch();
    for (final speciality in specialitys) {
      batch.insert(
        'speciality',
        speciality.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }

  
  // 查询房间列表
  Future<List<Room>> findRoomList(sql) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps;

    var whereUserIdSQL = "";
    if(Get.isRegistered<GlobalController>()){
      GlobalController globalController = Get.find();
      var userId = globalController.queryCurrentUserId();
      whereUserIdSQL = "userId='${userId}'";
    }
    
    if(sql == ''){
      maps  = await db.query('room' , orderBy:'sortRank ASC');
    }else{
      sql = "$sql AND $whereUserIdSQL";
      maps = await db.query('room',orderBy:'sortRank ASC',);
    }
    return List.generate(maps.length, (i) {
      return Room(
        roomId: maps[i]["roomId"],
        name: maps[i]["name"],
        storey: maps[i]["storey"],
        deviceCount: maps[i]["deviceCount"],
        note: maps[i]["note"],
        type: maps[i]["type"],
        isFinish: maps[i]["isFinish"],
        isTaste: maps[i]["isTaste"],
        isSound: maps[i]["isSound"],
        goTime:maps[i]["goTime"],
      );
    });
  }
  // 查询房间数据
  Future<List<Room>> findRoomData(sql) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps;

    var whereUserIdSQL = "";
    if(Get.isRegistered<GlobalController>()){
      GlobalController globalController = Get.find();
      var userId = globalController.queryCurrentUserId();
      whereUserIdSQL = "userId='${userId}'";
    }

    if(sql == ''){
      maps  = await db.query('room' , where: whereUserIdSQL);
    }else{
      sql = "$sql AND $whereUserIdSQL";
      maps = await db.query('room',where:sql);
    }
    return List.generate(maps.length, (i) {
      return Room(
        roomId: maps[i]["roomId"],
        name: maps[i]["name"],
        storey: maps[i]["storey"],
        deviceCount: maps[i]["deviceCount"],
        note: maps[i]["note"],
        type: maps[i]["type"],
        isFinish: maps[i]["isFinish"],
        isTaste: maps[i]["isTaste"],
        isSound: maps[i]["isSound"],
        goTime:maps[i]["goTime"],
      );
    });
  }

  /*设备数据操作 */
  // 插入设备数据
  Future<void> insertDevices(List<Device> devices) async {
    final db = await _database;
    final batch = db.batch();
    for (final device in devices) {
      batch.insert(
        'device',
        device.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }
  // 查询设备数据
  Future<List<Device>> findDevices(String sql) async {
    final db = await _database;
    late List<Map<String, dynamic>> maps;

    var whereUserIdSQL = "";
    if(Get.isRegistered<GlobalController>()){
      GlobalController globalController = Get.find();
      var userId = globalController.queryCurrentUserId();
      whereUserIdSQL = "userId='${userId}'";
    }

    if(sql == ""){
      maps = await db.query('device' , where: whereUserIdSQL);
    }else{
      sql = "$sql AND $whereUserIdSQL";
      maps = await db.query('device',where:sql);
    }
    return List.generate(maps.length, (i) {
      return Device(
        deviceId: maps[i]["deviceId"],
        formId:maps[i]["formId"],
        deviceTypeName: maps[i]["deviceTypeName"],
        deviceType: maps[i]["deviceType"],
        deviceCode: maps[i]["deviceCode"],
        roomId: maps[i]["roomId"],
        roomType: maps[i]["roomType"],
        isFinish: maps[i]["isFinish"],
        isOpen: maps[i]["isOpen"],
        userId:maps[i]['userId']
      );
    });
  }

  /*巡检项数据操作 */
  Future<void> insertForms(List<FormSQData> forms) async {
    final db = await _database;
    final batch = db.batch();

    String? userId = "";
    if(Get.isRegistered<GlobalController>()){
      GlobalController globalController = Get.find();
      userId = globalController.queryCurrentUserId();
    }

    for (final form in forms) {
      form.userId = userId;
      batch.insert(
        'form',
        form.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }
  // 查询巡检项数据
  Future<List<FormSQData>> findForms(String sql) async {
    final db = await _database;
    late List<Map<String, dynamic>> maps;

    var whereUserIdSQL = "";
    if(Get.isRegistered<GlobalController>()){
      GlobalController globalController = Get.find();
      var userId = globalController.queryCurrentUserId();
      whereUserIdSQL = "userId='${userId}'";
    }

    if(sql == ""){
      maps = await db.query('form' , where: whereUserIdSQL);
    }else{
      sql = "$sql AND $whereUserIdSQL";
      maps = await db.query('form',where:sql);
    }
    return List.generate(maps.length, (i) {
      return FormSQData(
        name: maps[i]["name"],
        deviceTypeId:maps[i]["deviceTypeId"],
        inputType: maps[i]["inputType"],
        outputType: maps[i]["outputType"],
        inspectionName: maps[i]["inspectionName"],
        inspectionRangeBegin: maps[i]["inspectionRangeBegin"],
        inspectionRangeEnd: maps[i]["inspectionRangeEnd"]
      );
    });
  }

  //查询所有专业列表
  Future<List<Speciality>> findSpecialityList() async {
    final db = await _database;
    final List<Map<String, dynamic>> maps;

    var whereUserIdSQL = "";
    maps  = await db.query('speciality' , distinct:true);
    return List.generate(maps.length, (i) {
      return Speciality(
        roomType: maps[i]["roomType"],
        roomTypeName: maps[i]["roomTypeName"],
        deviceCount: maps[i]["deviceCount"],
      );
    });
  }


  /*设备关联巡检项数据操作 */
  Future<void> insertDeviceForm(List<DeviceForm> deviceForms) async {
    final db = await _database;
    final batch = db.batch();

    String? userId = "";
    if(Get.isRegistered<GlobalController>()){
      GlobalController globalController = Get.find();
      userId = globalController.queryCurrentUserId();
    }

    for (final deviceForm in deviceForms) {
      deviceForm.userId = userId ?? '';
      batch.insert(
        'deviceForm',
        deviceForm.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }
  // 查找关联巡检项数据
  Future<List<DeviceForm>> findDeviceForm(String sql) async {
    final db = await _database;
    late List<Map<String, dynamic>> maps;

    var whereUserIdSQL = "";
    if(Get.isRegistered<GlobalController>()){
      GlobalController globalController = Get.find();
      var userId = globalController.queryCurrentUserId();
      whereUserIdSQL = "userId='${userId}'";
    }

    if(sql == ""){
      maps = await db.query('deviceForm' , where: whereUserIdSQL);
    }else{
      sql = "$sql AND $whereUserIdSQL";
      maps = await db.query('deviceForm',where:sql);
    }
    return List.generate(maps.length, (i) {
      return DeviceForm(
        id: maps[i]["id"],
        name: maps[i]["name"],
        formId:maps[i]["formId"],
        deviceTypeId: maps[i]["deviceTypeId"],
        inputType: maps[i]["inputType"],
        outputType: maps[i]["outputType"],
        inspectionName: maps[i]["inspectionName"],
        inspectionRangeBegin: maps[i]["inspectionRangeBegin"],
        inspectionRangeEnd: maps[i]["inspectionRangeEnd"],
        inputValue: maps[i]["inputValue"],
        inputActive: maps[i]["inputActive"],
      );
    });
  }

  /**巡检历史操作 */
  Future<void> insertHistory(List<History> historyData) async {
    final db = await _database;
    final batch = db.batch();
    for (final history in historyData) {
      batch.insert(
        'history',
        history.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }

  Future<List<History>> findHistory(String sql) async {
    final db = await _database;
    late List<Map<String, dynamic>> maps;
    if(sql == ""){
      maps = await db.query('history');
    }else{
      maps = await db.query('history',where:sql);
    }
    return List.generate(maps.length, (i) {
      return History(
        eid: maps[i]["eid"],
        mobile: maps[i]["mobile"],
        name:maps[i]["name"],
        avatar: maps[i]["avatar"],
        patrolStartTime: maps[i]["patrolStartTime"],
        patrolEndTime: maps[i]["patrolEndTime"],
        patrolStartTimeStamp: maps[i]["patrolStartTimeStamp"],
        patrolEndTimeStamp: maps[i]["patrolEndTimeStamp"],
      );
    });
  }
  

  
  // 更新数据
  Future<void> updateTable(String tableName,Map<String, dynamic> params, String sql) async {
    print('fun-> $updateTable... tableName: $tableName');
    final db = await _database;

    var whereUserIdSQL = "";
    if(Get.isRegistered<GlobalController>()){
      GlobalController globalController = Get.find();
      var userId = globalController.queryCurrentUserId();
      whereUserIdSQL = "userId='${userId}'";
    }
    if(sql==""){
      sql = whereUserIdSQL;
      await db.update(
        tableName,
        params,
        where: sql
      );
    }else{
      sql = "$sql AND $whereUserIdSQL";
      await db.update(
        tableName,
        params,
        where: sql,
      );
    }
  }

  // 更新数据
  Future<void> updateUserTable(String tableName,Map<String, dynamic> params, String sql) async {
    final db = await _database;
    if(sql==""){
      await db.update(
          tableName,
          params,
      );
    }else{
      await db.update(
        tableName,
        params,
        where: sql,
      );
    }
  }

  // 清空数据
  Future<void> clearTable(String tableName) async {
    final db = await _database;
    await db.rawDelete('DELETE FROM $tableName');
  }

  // 清空数据
  Future<void> clearTableByUserId(String tableName , String? userId) async {
    if(userId == null) return;
    final db = await _database;
    await db.rawDelete("DELETE FROM $tableName WHERE userId='$userId'");
  }

  // 查询表是否存在
  Future<bool> isExist(String tableName)async{
    final db = await _database;
    var result = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'");
    return result.isNotEmpty;
  }

  // 查询改查询数据是否为空
  Future<bool> isExistData(String tableName , String? userId) async {
    if(userId == null) return false;
    final db = await _database;
    var l = await db.rawQuery("SELECT * FROM $tableName WHERE userId='$userId'");
    return l.isNotEmpty;
  }

  Future<bool> isHaveUnCompleteTask(String? userId) async {
    if(userId == null) return false;
    final db = await _database;
    var l = await db.rawQuery("SELECT * FROM user WHERE eid='$userId' AND patrolStartTimeStamp!=0");
    return l.isNotEmpty;
  }
  
  Future checkWithoutDataTableByUserId(String tableName , String? userId) async {
    if(userId == null) return false;
    final db = await _database;
    db.rawQuery("UPDATE $tableName SET userId='$userId' WHERE userId='' OR userId=NULL");
  }

}
