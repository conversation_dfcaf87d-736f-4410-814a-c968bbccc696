import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/routes/app_pages.dart';
import 'package:sjzx_patrol_system_mobile/app/routes/route_helper.dart';

import '../../../../../main.dart';
import '../../../../db/ir/ir_chest.dart';
import '../../../../utils/permission/permission_util.dart';
import '../../../../utils/screenutil.dart';
import "../../../../controllers/global_controller.dart";
import 'package:percent_indicator/percent_indicator.dart';

import '../controllers/ir_patrol_start_controller.dart';


/// 热红外，
/// 【开始巡检】 和 【结束巡检】复用
class IrPatrolStartView extends GetView<IrPatrolStartController> {

  const IrPatrolStartView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return true; // 返回true表示允许返回到上一个页面
        },
        child: SafeArea(
            child: Scaffold(
                body: Container(
          padding: EdgeInsets.all(MyScreenUtil.width(24)),
          color: Color.fromRGBO(246, 248, 250, 1),
          child: Column(
            children: [
              InkWell(
                onTap: () => {Get.back()},
                child: Container(
                  padding: EdgeInsets.only(
                      left: MyScreenUtil.width(24),
                      right: MyScreenUtil.width(24)),
                  // alignment: Alignment.centerLeft, // 设置垂直居中
                  height: MyScreenUtil.height(60),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: MyScreenUtil.width(24),
                        height: MyScreenUtil.height(24),
                        child: Image.asset(
                          "assets/images/icon/left.png",
                          fit: BoxFit.cover,
                        ),
                      ),
                      Text('开始巡检')
                    ],
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 20),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.all(MyScreenUtil.height(24)),
                      width: MyScreenUtil.width(1032),
                      height: MyScreenUtil.height(708),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          // 房间了类型
                          Container(
                            child: Row(
                              children: [
                                Container(
                                  width: MyScreenUtil.width(5),
                                  height: MyScreenUtil.height(20),
                                  margin: const EdgeInsets.only(right: 13),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(14.0),
                                    color: Color(0xFF5777FF),
                                  ),
                                ),
                                Text(
                                  "设备类型",
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(25, 29, 38, 1),
                                      fontSize: MyScreenUtil.fontSize(18),
                                      fontWeight: FontWeight.w600),
                                )
                              ],
                            ),
                          ),
                          roomType(context)
                        ],
                      ),
                    ),
                    Container(
                      width: MyScreenUtil.width(336),
                      // height: MyScreenUtil.height(708),
                      margin: EdgeInsets.only(left: MyScreenUtil.width(20)),
                      decoration: BoxDecoration(
                        // color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Column(
                        children: [
                          Container(
                            padding: EdgeInsets.all(MyScreenUtil.height(24)),
                            decoration: BoxDecoration(
                              color: Color.fromRGBO(246, 248, 250, 1),
                              borderRadius: BorderRadius.circular(14.0),
                              // color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Color.fromRGBO(0, 0, 0, 0.1),
                                  offset: Offset(0, 0),
                                  blurRadius: 20,
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                Container(
                                  child: Row(
                                    children: [
                                      Container(
                                        width: MyScreenUtil.width(5),
                                        height: MyScreenUtil.height(20),
                                        margin:
                                            const EdgeInsets.only(right: 13),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(14.0),
                                          color: Color(0xFF5777FF),
                                        ),
                                      ),
                                      Text(
                                        "巡检信息",
                                        style: TextStyle(
                                            color: const Color.fromRGBO(
                                                25, 29, 38, 1),
                                            fontSize: MyScreenUtil.fontSize(18),
                                            fontWeight: FontWeight.w600),
                                      )
                                    ],
                                  ),
                                ),
                                // 巡检信息
                                Container(
                                  margin: const EdgeInsets.only(top: 33),
                                  // height: MyScreenUtil.height(120),
                                  child: userInfo(),
                                ),

                                (!controller.isStartModel.value) ? InkWell(
                                  onTap: () => controller.endIrPatrol(),
                                  child: Container(
                                    alignment: Alignment.center,
                                    width: MyScreenUtil.width(336),
                                    height: MyScreenUtil.height(120),
                                    margin:
                                    EdgeInsets.only(top: MyScreenUtil.height(24)),
                                    decoration: BoxDecoration(
                                      image: DecorationImage(
                                        image: AssetImage(
                                            "assets/images/over.png"),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    child: Text('结束巡检',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ),
                                ) : Container(),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ))));
  }

  // 巡检信息
  Widget userInfo() {
    return Obx(() => Column(
          children: [
            // 头像
            Container(
              width: MyScreenUtil.width(80),
              height: MyScreenUtil.height(80),
              decoration: BoxDecoration(
                  // color: MyScreenUtil.ThemColor(),
                  borderRadius: BorderRadius.circular(100),
                  image: controller.userInfo.value.avatar == ""
                      ? const DecorationImage(
                          image: AssetImage("assets/images/userLogo.png"),
                          fit: BoxFit.cover)
                      : DecorationImage(
                          image: NetworkImage(controller.userInfo.value.avatar),
                          fit: BoxFit.cover)),
            ),
            // 用户信息A栏
            Container(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                      child: Text(
                    controller.userInfo.value.name,
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(18),
                      color: MyScreenUtil.FontColor(),
                    ),
                  )),
                  Container(
                    margin: const EdgeInsets.only(top: 20),
                    child: Column(
                      children: [
                        Container(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width:
                                    MyScreenUtil.width(MyScreenUtil.width(20)),
                                height: MyScreenUtil.height(
                                    MyScreenUtil.height(20)),
                                margin: EdgeInsets.only(
                                    right: MyScreenUtil.width(12)),
                                child:
                                    Image.asset("assets/images/icon/time.png"),
                              ),
                              SizedBox(
                                  child: Text(
                                "开始时间",
                                style: TextStyle(
                                    fontSize: MyScreenUtil.fontSize(
                                        MyScreenUtil.fontSize(20))),
                              )),
                            ],
                          ),
                        ),
                        Obx(
                          () => SizedBox(
                            child: Text(
                              controller.irStartTime.value,
                              style: TextStyle(
                                color: Color.fromRGBO(134, 144, 156, 1),
                                fontSize: MyScreenUtil.fontSize(18),
                              ),
                            ),
                          ),
                        ),

                        Container(
                          margin: EdgeInsets.only(top: MyScreenUtil.width(16)),
                          height: MyScreenUtil.height(30),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width:
                                MyScreenUtil.width(MyScreenUtil.width(20)),
                                height: MyScreenUtil.height(
                                    MyScreenUtil.height(20)),
                                margin: EdgeInsets.only(
                                    right: MyScreenUtil.width(12)),
                                child:
                                Image.asset("assets/images/icon/time.png"),
                              ),
                              SizedBox(
                                  child: Text(
                                    "结束时间",
                                    style: TextStyle(
                                        fontSize: MyScreenUtil.fontSize(
                                            MyScreenUtil.fontSize(20))),
                                  )),
                            ],
                          ),
                        ),
                        Obx(
                              () => SizedBox(
                            child: Text(
                              controller.irEndTime.value,
                              style: TextStyle(
                                color: Color.fromRGBO(134, 144, 156, 1),
                                fontSize: MyScreenUtil.fontSize(18),
                              ),
                            ),
                          ),
                        ),

                        Container(
                            width: MyScreenUtil.width(284),
                            padding: EdgeInsets.all(MyScreenUtil.width(24)),
                            margin:
                                EdgeInsets.only(top: MyScreenUtil.height(16)),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Column(
                              children: [
                                Container(
                                  child: Row(
                                    children: [
                                      Container(
                                        width: MyScreenUtil.width(24),
                                        height: MyScreenUtil.height(24),
                                        child: Image.asset(
                                          "assets/images/icon/position_blue.png",
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      Text(
                                        controller.userInfo.value.occupation ??
                                            '暂无',
                                        style: TextStyle(
                                            color: MyScreenUtil.FontColor()),
                                      )
                                    ],
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                      top: MyScreenUtil.height(16)),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: MyScreenUtil.width(24),
                                        height: MyScreenUtil.height(24),
                                        child: Image.asset(
                                          "assets/images/icon/department_blue.png",
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      Text(
                                        controller.userInfo.value.deptName ?? '暂无',
                                        style: TextStyle(
                                            color: MyScreenUtil.FontColor()),
                                      )
                                    ],
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                      top: MyScreenUtil.height(16)),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: MyScreenUtil.width(24),
                                        height: MyScreenUtil.height(24),
                                        child: Image.asset(
                                          "assets/images/icon/speciality_blue.png",
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      Text(
                                        controller.userInfo.value.technicalPost ?? '暂无',
                                        style: TextStyle(
                                            color: MyScreenUtil.FontColor()),
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ))
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }

  // 房间类型
  Widget roomType(BuildContext context) {
    return Obx(() => Container(
          margin: const EdgeInsets.only(top: 20),
          width: MyScreenUtil.width(1032),
          height: MyScreenUtil.height(MyScreenUtil.height(280)),
          child: ListView(
              scrollDirection: Axis.horizontal,
              children: controller.roomTypeInfoList.map((item) {

                IrChest irChest = item;

                return Container(
                  padding: EdgeInsets.only(
                      left: MyScreenUtil.width(20),
                      right: MyScreenUtil.width(20)),
                  margin: EdgeInsets.fromLTRB(5, 5, 20, 5),
                  width: MyScreenUtil.width(300),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Color.fromRGBO(0, 0, 0, 0.1),
                          offset: Offset(0, 0),
                          blurRadius: 5,
                          spreadRadius: 0,
                        ),
                      ],
                      borderRadius:
                          BorderRadius.circular(MyScreenUtil.radius(16))),
                  child: Column(
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: MyScreenUtil.height(20)),
                        alignment: Alignment.centerLeft,
                        child: Container(
                            child: Row(
                          children: [
                            Container(
                              width: MyScreenUtil.width(24),
                              height: MyScreenUtil.height(24),
                              child: Image.asset(
                                "assets/images/icon/room_title.png",
                                fit: BoxFit.cover,
                              ),
                            ),
                            Expanded(child: Text(
                              "${irChest.chestName}",
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style: TextStyle(
                                  color: MyScreenUtil.FontColor(),
                                  fontSize: MyScreenUtil.fontSize(18),
                                  fontWeight: FontWeight.w600),
                            )
                              ,),

                          ],
                        )),
                      ),
                      Container(
                          child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            margin:
                                EdgeInsets.only(top: MyScreenUtil.height(20)),
                            alignment: Alignment.centerLeft,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                    child: Text(
                                  "设备数量: ${irChest.deviceCount}",
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(25, 29, 38, 1),
                                      fontSize: MyScreenUtil.fontSize(18)),
                                )),
                                Container(),
                              ],
                            ),
                          ),
                          Container(
                            width: 80,
                            height: 80,
                            child: CircularPercentIndicator(
                              radius: 30.0,
                              lineWidth: 5.0,
                              percent: double.parse(item.progress) / 100,
                              center: Text(
                                "${item.progress}%",
                              ),
                              progressColor: Color(0xFF5777FF),
                            ),
                          )
                        ],
                      )),
                      Row(
                        children: [
                          Expanded(
                              flex: 1,
                              child: Container(
                                  height: MyScreenUtil.height(30),
                                  margin: EdgeInsets.only(
                                      top: MyScreenUtil.height(30)),
                                  child: ElevatedButton(
                                      onPressed: ()async {

                                        var hasPermission = await PermissionUtil.checkCameraPermission(context , tip: scanPermissionTip );
                                        if (!hasPermission) return;

                                        // roiPlugin.startRoiPage();
                                        var argument = {
                                          "chestId" : irChest.chestId
                                        };

                                        route(Routes.IR_DEVICE_CHECK , params: argument);

                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            MyScreenUtil.ThemColor(), // 背景颜色
                                        disabledBackgroundColor:
                                            const Color.fromRGBO(
                                                143, 147, 153, 1),
                                      ),
                                      child: Text(
                                        item.progress == "100"
                                            ? "巡检完成"
                                            : '进入巡检',
                                        style: const TextStyle(
                                            color: Colors.white),
                                      ))))
                        ],
                      ),
                    ],
                  ),
                );
              }).toList()),
        ));
  }
}
