
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_device.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir_task/ir_task.dart';
import 'package:sjzx_patrol_system_mobile/app/retrofit/entity/ir_upload_req.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/string_util.dart';
import '../../../retrofit/entity/ir_permission/ir_task_resp.dart';


List<IrChest> convert2IrChest(String userId, List<IrTaskRespItem> sources){
  List<IrChest> dbList = [];

  sources.forEach((task) {
    task.infraredInspectionDeviceTypePadVOS?.forEach((chest) {
      if(chest != null){
        dbList.add(IrChest(
            StringUtil.getUUid()
            , chest.deviceTypeId ?? ''
            , task.id
            , chest.name
            , "0"
            , false
            , userId
            , chest.inspectionDevices?.length ?? 0
            , 0
        ));
      }
    });
  });
  return dbList;
}

List<IrChestDevice> convert2IrChestDevices(String userId, List<IrTaskRespItem> sources){
  List<IrChestDevice> dbList = [];

  sources.forEach((task) {
     task.infraredInspectionDeviceTypePadVOS?.forEach((chest) {
       if(chest != null){
         chest.inspectionDevices?.forEach((device) {
           if(device != null){
             dbList.add(IrChestDevice(
                 StringUtil.getUUid(),
                 device.id ?? '',
                 device.name,
                 task.id,
                 device.infraredDeviceTypeId ?? '',
                 chest.name,
                 device.code,
                 device.num ?? '',
                 device.installationSite,
                 device.project,
                 device.upperLimitValue,
                 false,
                 "",
                 "",
                 userId,
                 0 ,// 初始化为0
                 ""
             ));
           }
         });
       }
     });

  });
  return dbList;
}


IrUploadDeviceReq convertIrRequestUploadBody(IrChestDevice device) {
  return IrUploadDeviceReq()
    ..code = device.code ?? ''
    ..detectionTime = device.detectionTime //TODO
    ..deviceId = device.irDeviceId
    ..infraredDeviceTypeId = device.infraredDeviceTypeId
    ..infraredDeviceTypeName = device.infraredDeviceTypeName
    ..installationSite = device.installationSite
    ..maxTemperature = device.checkTemperature
    ..mark = device.comments ?? ''
    ..name = device.deviceName ?? ''
    ..num = device.num ?? ''
    ..picture =  device.path
    ..project = device.project ?? '';
}

// 红外巡检任务
IrTask convert2UiTask(String userId ,IrTaskRespItem body) {
  return IrTask(body.id ?? ''
      , body.inspector
      , body.inspectorId
      , body.deptId
      , body.deptName
      , body.type
      , body.status
      , userId
      , 0  // 开始时间
      , 0  // 结束时间
      , body.deviceCount ?? 0
      , 0  // 已检测设备数
      , false
  );

}