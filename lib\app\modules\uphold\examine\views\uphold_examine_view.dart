import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../utils/screenutil.dart';
import '../controllers/uphold_examine_controller.dart';

class UpholdExamineView extends GetView<UpholdExamineController> {
  const UpholdExamineView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(()=>Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(MyScreenUtil.height(60)),
        child: AppBar(
          title:Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children:[
              Text(controller.titleName.value)
            ],
          ),
          centerTitle: true,
        ),
      ),
      
      
      body: Container(
        // padding: const EdgeInsets.all(10),
        child: controller.examineWorkList.length!=0?ListView(
          children: controller.examineWorkList.map((item){
            return workList(item);
          }).toList().cast<Widget>(),
        ):Container(
          padding: EdgeInsets.only(top: MyScreenUtil.height(20),left: MyScreenUtil.height(20)),
          child: const Text("当前没有需要审核的工单"),
        ),
      )
    ));
  }

  // 工单列表
  workList(workListItem){
    return Container(
      margin: EdgeInsets.all(MyScreenUtil.height(10)),
      decoration:BoxDecoration(
        border: Border.all(
          color: MyScreenUtil.ThemColor(),
          width: 1
        ),
        borderRadius: BorderRadius.circular(MyScreenUtil.radius(10))
      ),
      child: Column(
        children: [
          // 标题
          Container(
            decoration:BoxDecoration(
              color: Color.fromRGBO(249, 230, 232, 1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(MyScreenUtil.radius(10)),
                topRight: Radius.circular(MyScreenUtil.radius(10))
              )
            ), 
            padding:const EdgeInsets.all(10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children:[
                Text("审核工单",style:TextStyle(
                  color:const Color.fromRGBO(43, 51, 63, 1),
                  fontSize: MyScreenUtil.fontSize(18)
                )),
                Text("待审核",style:TextStyle(
                  color:const Color.fromRGBO(195, 12, 34, 1),
                  fontSize:MyScreenUtil.fontSize(18)
                ))
                
              ],
            ),
          ),
          // 内容
          Container(
            padding: EdgeInsets.only(top: MyScreenUtil.height(22),bottom:MyScreenUtil.height(22) ),
            child:Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("维护部门: ${workListItem['deptName']}"),
                      ),
                      Container(
                        width:MyScreenUtil.width(380),
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("设备位置: ${workListItem['installationSite']}",overflow: TextOverflow.ellipsis,),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("执行人: ${controller.parseOperator(workListItem['operator'])}"),
                      ),
                      Container(
                        child:Text("厂家: ${workListItem['factory']??''}"),
                      )
                    ],
                  ),
                ),
                Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("设备名称: ${workListItem['devicePlanName']}"),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("设备编号: ${workListItem['deviceNum']}"),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("监督员: ${workListItem['supervision']['name']}"),
                      ),
                    ],
                  ),
                ),
                Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("维护类型: ${workListItem['typeName']}"),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:workListItem['week'] != null ? Text(
                            "维护时间: ${workListItem['typeName']}第${workListItem['week']}周",
                            style:
                                TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                          ):Text(
                            "维护时间: ${workListItem['typeName']}",
                            style:
                                TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                          ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("专业工程师: ${workListItem['pe']['name']}"),
                      )
                    ],
                  ),
                ),
                Container(
                  child:Column(
                    children: [
                      ElevatedButton(
                        onPressed: (){
                          Get.toNamed(
                            "/start-examine",
                            arguments:{
                              "workData":workListItem
                            }
                          );
                        },
                        child: const Text("开始审核"),
                        style: ButtonStyle(
                          backgroundColor:MaterialStateProperty.resolveWith((states){
                            return MyScreenUtil.ThemColor();
                          })
                        ),
                      )
                    ],
                  ),
                )
              ],
            ) ,
          )
        ],
      ),
    );
  }

}
