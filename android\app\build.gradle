def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '2'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0.1'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

def keystorePropertiesFile = rootProject.file("keystore.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

android {

    // compileSdkVersion flutter.compileSdkVersion
    namespace = "com.example.sjzx_patrol_system_phone"
    compileSdkVersion 35
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

//    buildFeatures {
//        compose true
//        viewBinding = true
//    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.3'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'

        main {
            jniLibs.srcDir 'libs'
        }
    }

    signingConfigs {
        release  {
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
        }
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.example.sjzx_patrol_system_phone"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        
        // minSdkVersion flutter.minSdkVersion
        minSdkVersion 23
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    buildTypes {
        debug {
            minifyEnabled false
            signingConfig signingConfigs.release
            ndk {
                abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
            }
        }

        release {
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            ndk {
                abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
            }

            /*applicationVariants.all { variant ->
                variant.outputs.forEach { file ->
                    def appName = '综合管理平台'
                    def time = new Date().format("yyyyMMdd-HHmm", TimeZone.getDefault())
                    def buildType = variant.buildType.name
                    // 输入结果如：MyAppName-0.5.1-release.apk
                    def rootDir = project.rootDir
                    def outputFileName = "${appName}-${time}-${defaultConfig.versionName}-${buildType}.apk"
                    println("  L Terminal Run : cd "+ rootDir + "/apk/${buildType}/" + "  file: ${outputFileName}")

                    copy {
                        from file.outputFile
                        into "${rootDir}/apk_dir/${buildType}/"
                        rename {
                            String srcFileName ->
                                "${outputFileName}"
                        }
                    }
                }
            }*/
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"

//    implementation files("libs/HCUSBSDK.jar")
//    implementation files("libs/jna.jar")
//    implementation files("libs/PlayerSDK_hcnetsdk.jar")

    implementation fileTree(dir: "libs", include: ["gyuv-1.0.11.aar"])

    // 自引入库
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

    implementation 'androidx.appcompat:appcompat:1.4.0'
    implementation 'com.google.android.material:material:1.3.0'

    def lifecycle_version = "2.3.0"
    // ViewModel
    implementation "androidx.lifecycle:lifecycle-viewmodel:$lifecycle_version"
    // LiveData
    implementation "androidx.lifecycle:lifecycle-livedata:$lifecycle_version"
    // alternately - if using Java8, use the following instead of lifecycle-compiler
    implementation "androidx.lifecycle:lifecycle-common-java8:$lifecycle_version"

    def fragment_version = '1.3.4'
    implementation "androidx.fragment:fragment-ktx:$fragment_version"
    implementation "androidx.fragment:fragment:$fragment_version"

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.3.0"
//    implementation "com.blankj:utilcode:1.25.5"

    implementation "androidx.recyclerview:recyclerview:1.2.0"

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    implementation 'androidx.core:core-ktx:1.7.0'
    // constraintlayout view版本 和 compose版本
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("androidx.constraintlayout:constraintlayout-compose:1.0.1")
}


