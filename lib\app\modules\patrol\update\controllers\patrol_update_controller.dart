import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:collection/collection.dart';
import 'package:get/get.dart';
import '../../../../api/room.dart';
import '../../../../controllers/global_controller.dart';
import '../../../../controllers/sqflite_controller.dart';
import '../../../../data/user.dart';
import '../../../../data/history.dart';

class PatrolUpdateController extends GetxController {
  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();

  // 初始化房间的接口实例
  RoomApi roomApi = RoomApi();

  /*响应式数据 */
  // 标题
  RxString titleName = "上传数据".obs;
  // 上传列表(在sqflite中的数据为用户信息表)
  RxMap userInfo = {}.obs;



  @override
  void onInit()async{
    super.onInit();
    await findUserInfo();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  // 查找用户信息
  findUserInfo()async{
    // 查找用户信息判断isUpload字段如果为0则显示上传数据
    var userInfoData = await sqfliteController.findUsers("eid='${globalController.userInfo.value!.data.eid}'");
    final List<Map<String, dynamic>> userInfolist = userInfoData.map((item) => item.toJson()).toList();

    if(userInfoData.isNotEmpty){
      var userItem = userInfoData[0];
      if(userItem.patrolStartTimeStamp == 0 && userItem.patrolEndTimeStamp == 0){
        userInfo.value = {};
      }else{
        userInfo["eid"] = userItem.eid;
        userInfo["name"] = userItem.name;
        userInfo["avatar"] = userItem.avatar;
        userInfo["patrolStartTime"] = userItem.patrolStartTime;
        userInfo["patrolEndTime"] = userItem.patrolEndTime;
        userInfo['occupation'] = userItem.occupation;
        userInfo['technicalPost'] = userItem.technicalPost;
      }
    }

    // if(userInfoData[0].isUpload == 0){
    //   for(var userItem in userInfoData){
    //     userInfo["eid"] = userItem.eid;
    //     userInfo["name"] = userItem.name;
    //     userInfo["avatar"] = userItem.avatar;
    //     userInfo["patrolStartTime"] = userItem.patrolStartTime;
    //     userInfo["patrolEndTime"] = userItem.patrolEndTime;
    //     userInfo['occupation'] = userItem.occupation;
    //     userInfo['technicalPost'] = userItem.technicalPost;
    //   }
    // }else{
    //   userInfo.value = {};
    // }
    // update();
  } 

  // 数据上传
  uploadFun(){
    if(userInfo["patrolEndTime"]==null){
      Fluttertoast.showToast(
        msg: "当前巡检任务未结束,无法上传数据",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0
      );
    }else{
      initUploadData();
    }
  }

  initUploadData()async{
    loadingFun();
    var index = 0;
    var inspections = {};
    // 初始化用户信息
    var userInfo = await sqfliteController.findUsers("eid='${globalController.userInfo.value!.data.eid}'");

    DateTime d = DateTime.now();

    inspections['eid'] = userInfo[0].eid;
    inspections['employeeName'] = userInfo[0].name;
    inspections['deptName'] = userInfo[0].floor??"";
    inspections['profession'] = userInfo[0].technicalPost;
    inspections['roomType'] = userInfo[0].roomType;
    inspections['startTime'] = userInfo[0].patrolStartTimeStamp;
    inspections['endTime'] = userInfo[0].patrolEndTimeStamp;
    inspections['occupation'] = userInfo[0].occupation;
    inspections['company'] = userInfo[0].companyName;
    inspections['devices'] = [];
    inspections['unusualSmell'] = [];
    inspections['unusualSound'] = [];
    inspections['roomList'] = [];

    // 根据房间类型表查找室内房间
    var roomTypeInfo = await sqfliteController.findRoomTypeInfo("");
    var process = 0;
    for(var roomTypeInfoItem in roomTypeInfo){
      int number = int.parse(roomTypeInfoItem.progress);
      process = process+number;
      // 根据室内房间查找设备
      var  roomList = await sqfliteController.findRoomData("type='${roomTypeInfoItem.roomType}'");
      for(var roomItem in roomList){
        /**房间异味 */
        if(roomItem.isTaste==1){
          var tasteData = tasteFun(roomItem);
          inspections['unusualSmell'].add(tasteData);
        }
        /**房间异响 */
        if(roomItem.isSound!=0&&roomItem.isSound!=null){
          var soundData = soundFun(roomItem);
          inspections['unusualSound'].add(soundData);
        }

        /**记录进入房间时间 */
        if(roomItem.goTime!=null){
          var startTimeData = roomStartFun(roomItem);
          inspections['roomList'].add(startTimeData);
        }
        /**巡检项表单查询及处理 */
        // 根据设备构建接口的devices基本数据
        var deviceList = await sqfliteController.findDevices("roomId='${roomItem.roomId}' AND roomType='${roomItem.type}'");
        for(var deviceItem in deviceList){
          // 根据设备列表查询下面的巡检项
          var deviceFormList = await sqfliteController.findDeviceForm("formId='${deviceItem.formId}'");
          final List<Map<String, dynamic>> xxx = deviceFormList.map((item) => item.toJson()).toList();
          // 按照单位进行分组
          var groups = groupBy(deviceFormList, (obj) => obj.outputType);
          List<Map<String, dynamic>> result = [];

          groups.forEach((key, value) {
            result.add({
              'outputType': key,
              'data': value.map((obj) => {
                "label": obj.inspectionName,
                "value": obj.inputValue,
              }).toList(),
            });
          });
          // 查找 inspections['devices'] 中 deviceId 为当前id的对象将数据添加到pictures字段中
          var initPictures = [];
          for(var resultItem in result){
            var initPicturesObj = {};
            initPicturesObj['parsedData'] = {};
            for(var resltDataItem in resultItem['data']){
              initPicturesObj['parsedData'][resltDataItem["label"]] = resltDataItem['value']??'';
            }
            initPictures.add(initPicturesObj);
          }
          // 赋值最终的设备信息
          inspections['devices'].add({
            "deviceId":deviceItem.deviceId,
            "did":deviceItem.deviceId,
            "roomId":deviceItem.roomId,
            "roomType":deviceItem.roomType,
            "pictures":initPictures,
            "deviceStatus":deviceItem.isOpen,//0设备关闭,1设备开启
          });
        }
      }
    }
    inspections['process'] = (process/roomTypeInfo.length).toInt();
    print(inspections);
    // 调用上传接口
    var uploadDataParams = {
      "inspections":[inspections]
    };
    var response = await roomApi.uploadData(jsonEncode(uploadDataParams));
    if(response.data['code'] == 1){
      Get.back();
      Fluttertoast.showToast(
        msg: "数据上传成功",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0
      );
      // 上传成功后调用户状态清空方法
      resetUser();
      resetTable();
    }else{
      Get.back();
      Fluttertoast.showToast(
        msg: "数据上传上传失败",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0
      );
    }
  }

  // 房间异味
  tasteFun(roomItem){
    var tasteItemObj= {
      "description":'是',
      "roomId": roomItem.roomId,
      "roomType": roomItem.type
    };
    return tasteItemObj;
  }
  
  // 房间异响
  soundFun(roomItem){
    var soundItemObj= {
      "description": '是',
      "roomId": roomItem.roomId,
      "roomType": roomItem.type
    };
    return soundItemObj;
  }

  roomStartFun(roomItem){
    var soundItemObj= {
      "startTime": roomItem.goTime,
      "roomId": roomItem.roomId,
      "roomType": roomItem.type
    };
    return soundItemObj;
  }

  // 重置用户
  resetUser()async{
    /**上传成功后清空 user ,isUpload字段为 0*/
    sqfliteController.updateTable(
      "user", 
      {
        "isUpload":0
      }, 
      "eid='${globalController.userInfo.value!.data.eid}'"
    );
    /*向巡检历史表中添加一条历史记录*/
    var uid = globalController.userInfo.value!.data.eid;
    var userInfoData = await sqfliteController.findUsers("eid='${uid}'");
    List<History> historyList = [];
    historyList.add(History(
        eid:userInfoData[0].eid,
        mobile:userInfoData[0].phone,
        name:userInfoData[0].name,
        avatar:userInfoData[0].avatar,
        patrolStartTime:userInfoData[0].patrolStartTime,
        patrolEndTime:userInfoData[0].patrolEndTime,
        patrolStartTimeStamp:userInfoData[0].patrolStartTimeStamp,
        patrolEndTimeStamp:userInfoData[0].patrolEndTimeStamp,
    ));
    sqfliteController.insertHistory(historyList);
    userInfo.value = {};
    update();
    // await findUserInfo();
  }

  // 重置room \roomTypeInfo \device \ deviceForm表的状态和数值
  resetTable(){
    // 重置室内的完成状态
    sqfliteController.updateTable(
      "room",
      {
        "isFinish":0,
        "goTime":null,
      },
      ""
    );
    // 重置巡检房间的完成进度
    sqfliteController.updateTable(
      "roomTypeInfo",
      {"progress":0},
      ""
    );
    // 重置设备的完成状态
    sqfliteController.updateTable(
      "device",
      {
        "isFinish":0,
        "isOpen":1
      },
      ""
    );
    // 重置设备巡检表单的值和保存状态
    sqfliteController.updateTable(
      "deviceForm",
      {
        "inputActive":null,
        "inputValue":null
      },
      ""
    );
    // 清空用户巡检的开始和结束时间
    sqfliteController.updateUserTable(
      "user", 
      {
        'patrolStartTime':null,
        'patrolEndTime':null,
        'patrolStartTimeStamp':0,
        'patrolEndTimeStamp':0,

      }, 
      "eid='${globalController.userInfo.value!.data.eid}'"
    );
  }







  /*体验优化loading */
  /**体验优化 */
  loadingFun(){
    Get.dialog(
      barrierDismissible:false,
      const SpinKitFadingCircle(
        color: Colors.white,
        size: 50.0,
      ),
    );
  }

}
