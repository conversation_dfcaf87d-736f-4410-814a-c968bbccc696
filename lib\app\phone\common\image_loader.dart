

import 'package:extended_image_library/extended_image_library.dart';
import 'package:flutter/material.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';

import '../../utils/string_util.dart';


class ImageLoader extends StatelessWidget {
  
  final String? url;

  final double? width;
  final double? height;
  final bool? isCircle;

  final String? placeHolder;

  const ImageLoader({super.key , this.url , this.width ,this.height , this.isCircle , this.placeHolder});
  
  @override
  Widget build(BuildContext context) {
    return _buildAvatar();
  }
  
  _buildAvatar() {
    if(StringUtil.isEmpty(url)) {
      return Container(
        width: width ?? 72,
        height: height ?? 72,
        child: Image.asset(placeHolder ?? AssetsRes.LOGO , fit: BoxFit.cover,),
      );
    }

    ImageProvider provider = url!.startsWith("http") == true ? ExtendedNetworkImageProvider(
        url!
    ) :ExtendedFileImageProvider(File(url!)) as ImageProvider;

    return Container(
      width: width ?? 72,
      height: height ?? 72,
      decoration: BoxDecoration(
          image: DecorationImage(
              fit: BoxFit.cover,
              image: provider),
          border: Border.all(
              width: 1, color: const Color(0xFFD3D3D3)),
          borderRadius: BorderRadius.circular(isCircle == true ? 36 : 0)),
    );
  }
  
}