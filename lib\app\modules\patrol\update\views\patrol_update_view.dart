import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';

import '../controllers/patrol_update_controller.dart';

class PatrolUpdateView extends GetView<PatrolUpdateController> {
  const PatrolUpdateView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(
      //   title: Obx(()=>Row(
      //     mainAxisAlignment: MainAxisAlignment.start,
      //     children: [
      //       Text('${controller.titleName.value}')
      //     ],
      //   )),
      //   centerTitle: true,
      // ),
      body: Safe<PERSON>rea(
          child: Container(
        margin: const EdgeInsets.all(20),
        child: Column(
          children: [
            InkWell(
              onTap: () => {Get.back()},
              child: Container(
                padding: EdgeInsets.only(
                    left: MyScreenUtil.width(24),
                    right: MyScreenUtil.width(24)),
                // alignment: Alignment.centerLeft, // 设置垂直居中
                height: MyScreenUtil.height(60),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  children: [
                    Container(
                      width: MyScreenUtil.width(24),
                      height: MyScreenUtil.height(24),
                      child: Image.asset(
                        "assets/images/icon/left.png",
                        fit: BoxFit.cover,
                      ),
                    ),
                    Text('上传数据')
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 1,
              child: Container(
                margin: EdgeInsets.only(
                    bottom: MyScreenUtil.height(24),
                    top: MyScreenUtil.height(24)),
                padding: EdgeInsets.all(MyScreenUtil.height(24)),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16)),
                child: Column(
                  children: [
                    Container(
                        margin: EdgeInsets.only(
                          bottom: MyScreenUtil.height(24),
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: MyScreenUtil.width(5),
                              height: MyScreenUtil.height(20),
                              margin: const EdgeInsets.only(right: 13),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(14.0),
                                color: Color(0xFF5777FF),
                              ),
                            ),
                            Text(
                              "待上传数据列表",
                              style: TextStyle(
                                  fontSize: MyScreenUtil.fontSize(18),
                                  fontWeight: FontWeight.w600),
                            )
                          ],
                        )),
                    Expanded(flex: 1, child: DataList()),
                    Container(
                      width: double.infinity,
                      height: MyScreenUtil.height(60),
                      decoration: BoxDecoration(
                          color: MyScreenUtil.FontColor(),
                          borderRadius:
                              BorderRadius.circular(MyScreenUtil.radius(16))),
                      child: ElevatedButton(
                          onPressed: () {
                            controller.uploadFun();
                            // Get.offNamed("/home");
                          },
                          style: ButtonStyle(backgroundColor:
                              MaterialStateProperty.resolveWith((states) {
                            return MyScreenUtil.FontColor();
                          })),
                          child: const Text("数据上传")),
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      )),
    );
  }

  // 待上传数据列表
  Widget DataList() {
    return Obx(() => GridView.count(
          crossAxisCount: 1,
          // childAspectRatio: MyScreenUtil.getScreenWidth()/ 300,
          childAspectRatio: MyScreenUtil.getScreenWidth() / 150,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
          children: controller.userInfo.isEmpty
              ? [
                  Container(
                    child: const Text("当前没有要上传的数据"),
                  )
                ]
              : [
                  // 选中
                  Container(
                    padding: EdgeInsets.only(
                        top: MyScreenUtil.height(10),
                        bottom: MyScreenUtil.height(10)),
                    decoration: BoxDecoration(
                        border: Border.all(
                            width: 1,
                            color: const Color.fromRGBO(143, 147, 153, 1)),
                        borderRadius: BorderRadius.circular(10)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Container(
                          width: MyScreenUtil.width(120),
                          height: MyScreenUtil.width(120),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(
                                  MyScreenUtil.radius(120)),
                              // color:MyScreenUtil.ThemColor(),
                              image: controller.userInfo['avatar'] == ''
                                  ? const DecorationImage(
                                      image: AssetImage(
                                          "assets/images/userLogo.png"),
                                      fit: BoxFit.fill)
                                  : DecorationImage(
                                      image: NetworkImage(
                                          "${controller.userInfo['avatar']}"),
                                      fit: BoxFit.fill)),
                        ),
                        Container(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(
                                        right: MyScreenUtil.width(20)),
                                    width: MyScreenUtil.width(100),
                                    child: Text(
                                      "巡检员",
                                      textAlign: TextAlign.right,
                                      style: TextStyle(
                                          fontSize: MyScreenUtil.fontSize(18)),
                                    ),
                                  ),
                                  Container(
                                    width: MyScreenUtil.width(200),
                                    child: Text(
                                      "${controller.userInfo['name']}",
                                      style: TextStyle(
                                          fontSize: MyScreenUtil.fontSize(18)),
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(
                                        right: MyScreenUtil.width(20)),
                                    width: MyScreenUtil.width(100),
                                    child: Text(
                                      "职位",
                                      textAlign: TextAlign.right,
                                      style: TextStyle(
                                          fontSize: MyScreenUtil.fontSize(18)),
                                    ),
                                  ),
                                  Container(
                                    width: MyScreenUtil.width(200),
                                    child: Text(
                                      "${controller.userInfo['occupation'] ?? '暂无'}",
                                      style: TextStyle(
                                          fontSize: MyScreenUtil.fontSize(18)),
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(
                                        right: MyScreenUtil.width(20)),
                                    width: MyScreenUtil.width(100),
                                    child: Text(
                                      "专业",
                                      textAlign: TextAlign.right,
                                      style: TextStyle(
                                          fontSize: MyScreenUtil.fontSize(18)),
                                    ),
                                  ),
                                  Container(
                                    width: MyScreenUtil.width(200),
                                    child: Text(
                                      "${controller.userInfo['technicalPost'] ?? '暂无'}",
                                      style: TextStyle(
                                          fontSize: MyScreenUtil.fontSize(18)),
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(
                                        right: MyScreenUtil.width(16)),
                                    width: MyScreenUtil.width(140),
                                    child: Text(
                                      "开始巡检时间",
                                      textAlign: TextAlign.right,
                                      style: TextStyle(
                                          fontSize: MyScreenUtil.fontSize(18)),
                                    ),
                                  ),
                                  Container(
                                    width: MyScreenUtil.width(200),
                                    child: Text(
                                      "${controller.userInfo['patrolStartTime'] ?? ''}",
                                      style: TextStyle(
                                          fontSize: MyScreenUtil.fontSize(18)),
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(
                                        right: MyScreenUtil.width(16)),
                                    width: MyScreenUtil.width(140),
                                    child: Text(
                                      "结束巡检时间",
                                      textAlign: TextAlign.right,
                                      style: TextStyle(
                                          fontSize: MyScreenUtil.fontSize(18)),
                                    ),
                                  ),
                                  Container(
                                    width: MyScreenUtil.width(200),
                                    child: Text(
                                      "${controller.userInfo['patrolEndTime'] ?? ''}",
                                      style: TextStyle(
                                          fontSize: MyScreenUtil.fontSize(18)),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Container(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text("未上传",
                                  style: TextStyle(
                                      color: MyScreenUtil.ThemColor(),
                                      fontSize: MyScreenUtil.fontSize(20),
                                      fontWeight: FontWeight.w500))
                              // Icon(
                              //   Icons.check_box,
                              //   color:MyScreenUtil.ThemColor(),
                              //   size:MyScreenUtil.fontSize(40)
                              // )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ],
        ));
  }
}
