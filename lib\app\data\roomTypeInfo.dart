

class RoomTypeInfo {
  final String roomType;
  final String roomTypeName;
  final int roomCount;
  final int deviceCount;
  final String progress;
  String? userId;


  RoomTypeInfo({
    required this.roomType,
    required this.roomTypeName,
    required this.roomCount,
    required this.deviceCount,
    required this.progress,
    this.userId
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      'roomType': roomType,
      'roomTypeName': roomTypeName,
      'roomCount': roomCount,
      'deviceCount': deviceCount,
      'progress': progress,
      'userId':userId,
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  Map<String, dynamic> toJson() {
    return {
      'roomType': roomType,
      'roomTypeName': roomTypeName,
      'roomCount': roomCount,
      'deviceCount': deviceCount,
      'progress': progress
    };
  }

}