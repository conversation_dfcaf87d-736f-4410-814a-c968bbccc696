import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';

import '../controllers/uphold_start_uphold_controller.dart';

// 回退计划
class Recall extends GetView<UpholdStartUpholdController> {
  const Recall({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() =>  Container(
      height: MyScreenUtil.height(600),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            child: Column(
              children: [
                Container(
                  padding:const EdgeInsets.all(10),
                  decoration:BoxDecoration(
                    color: const Color.fromRGBO(249, 230, 232,1),
                    borderRadius: BorderRadius.circular(MyScreenUtil.radius(10))
                  ),
                  child:Text(
                    "${controller.recallList.value[0].content}",
                    style: TextStyle(
                      color: Color.fromRGBO(43, 51, 63,1)
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.centerLeft,
                  padding: EdgeInsets.all(MyScreenUtil.height(10)),
                  margin: EdgeInsets.only(top:MyScreenUtil.height(10)),
                  decoration:BoxDecoration(
                    color: const Color.fromRGBO(249, 230, 232,1),
                    borderRadius: BorderRadius.circular(MyScreenUtil.radius(10))
                  ),
                  child:Text(
                    "专业工程师: ${controller.workItemData['pe']??''} (${controller.workItemData['pePhone']??''})",
                    style: TextStyle(
                      color: Color.fromRGBO(43, 51, 63,1)
                    ),
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Container(
                  height: MyScreenUtil.height(52),
                  child: ElevatedButton(
                    onPressed: (){
                      controller.recallExecute(controller.recallList[0]);
                      controller.handelComponentIndex(5,"workOrder");
                    }, 
                    child: const Text("我已查阅")
                  ),
                )
              )
            ],
          )
        ],
      )
    ));
  }


}