import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sjzx_patrol_system_mobile/app/retrofit/entity/ir_upload_req.dart';

import 'entity/base_resp.dart';
import 'entity/ir_permission/IrDeviceListItem.dart';
import 'entity/ir_permission/ir_task_resp.dart';
import 'entity/login_req_body.dart';
import 'entity/login_response.dart';
import 'entity/phone_version_detail_resp.dart';
import 'entity/upload_img_req.dart';
part 'ir_datasource.g.dart';

@RestApi()
abstract class IrDataSource {

  factory IrDataSource(Dio dio, {String baseUrl}) = _IrDataSource;

  @POST('/user/pda/login/v1')
  Future<BaseResp<LoginRespData>> login(@Body() LoginReqBody body);

  @GET('/user/pda/infrared/check')
  Future<BaseResp<bool>> irPermission(@Query("userId") String? userId,);

  // 红外设备列表 弃用....
  @deprecated
  @GET('/infrared/inspection/pad/list')
  Future<BaseResp<List<IrDeviceListItem>>> _getIrDeviceList(@Query("userId") String? userId);


  // pad端 获取检测任务列表 2024-06-03
  @GET('/infrared/inspection/pad/list/v1/{userId}')
  Future<BaseResp<List<IrTaskRespItem>>> getIrTaskList(@Path("userId") String? userId);

  // 提交
  @POST('/infrared/inspection/pad/v1')
  Future<BaseResp<dynamic>> commitIrDevices(@Body() IrUploadReq body);

  // 升级（phone使用）
  @GET('/version/mobile/v1')
  Future<BaseResp<VersionResp?>> getVersion();  // 升级（phone使用）

  //上传红外图片
  @FormUrlEncoded()
  @POST('/infrared/inspection/upload/v1')
  Future<BaseResp<String>> uploadImg(@Field("file") String file );



}