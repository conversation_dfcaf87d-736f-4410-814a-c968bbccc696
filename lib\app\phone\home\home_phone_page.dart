
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/home/<USER>';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';

import '../../modules/ir_detection/ir_detection_task_enter.dart';
import '../../retrofit/entity/phone_version_detail_resp.dart';
import '../../routes/app_pages.dart';
import '../../routes/route_helper.dart';
import '../../utils/screenutil.dart';
import '../../utils/storage.dart';
import '../common/small_widget.dart';

class HomePhonePage extends StatefulWidget {

  const HomePhonePage({super.key});

  @override
  State<StatefulWidget> createState() => PhoneHomeState();
}

class PhoneHomeState extends State<HomePhonePage> {

  PhoneHomeController controller = Get.find();

  @override
  void initState() {
    super.initState();
    ever(controller.showSetting, (v) => _showSettingDialog());

    ever(controller.showUpgrade, (v) => _showUpgradeDialog(v));
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PhoneHomeController>(builder: (_){
      return Scaffold(
        backgroundColor: Color(0xfff2f3f5),
        resizeToAvoidBottomInset: true,
        body: Container(
          constraints: const BoxConstraints(
            maxWidth: double.infinity
          ),
          child: Stack(
            children: [
              Image.asset(AssetsRes.PHONE_LOGIN_BG,fit: BoxFit.cover,
              width: double.infinity,
              ),
              Column(
                children: [
                  75.gap,
                  Row(
                    children: [
                      Expanded(child: Container()),
                      Expanded(child: Container(
                        alignment: Alignment.center,
                        child: const MainTitle(title: '首页',),
                      )),
                      Expanded(child: Container(
                        alignment: Alignment.centerRight,
                        margin: EdgeInsets.only(right: 20),
                        child: InkWell(
                          child: Image.asset(AssetsRes.PHONE_SETTING_ICON, width: 18,),
                          onTap: (){
                            controller.logout();
                          },
                        ),
                      )),
                    ],
                  ),

                  51.gap,

                  userInfoCard(_),

                  _.hasIrPermission.value ?
                  _buildItemCard(controller) : Container(
                    padding: const EdgeInsets.all(20),
                    child: const Text('您没有红外巡检权限'),
                  )
                ],
              )
            ],
          ),
        ),
      );
    });
  }

  _buildItemCard(PhoneHomeController _) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 5),
      child: Row(
        children: [
          Expanded(child: _buildFunctionCard(AssetsRes.PHONE_CHECK_BG , "检测任务", (){
            _.preCheckIrData(() {
              route(Routes.IR_TASK, params: {'isRecord': false});
            });
          })),
          10.gap,
          Expanded(
            child: _buildFunctionCard(AssetsRes.PHONE_RECORD_BG, '检测记录', (){
              _.preCheckIrData(() {
                route(Routes.IR_TASK, params: {'isRecord': true});
              });
            }),
          ),
        ],
      ),
    );
  }

  _buildFunctionCard(String icon , String label, VoidCallback? action) {
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: () => action?.call(),
      child: Container(
        width: double.infinity,
        height: 100,
        decoration: BoxDecoration(
          color: Colors.transparent,
          image: DecorationImage(
            image: AssetImage(icon),
            fit: BoxFit.fitWidth, // 完全填充
          ),
        ),
        child: Container(
          alignment: Alignment.centerLeft,
          margin: const EdgeInsets.only(left: 12),
          child: Text(label, style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 15 ,color: Colors.black),),
        ),
      ),
    );
  }

  // 用户信息
  Widget userInfoCard(PhoneHomeController controller) {
    return Obx(() => Container(
      margin: const EdgeInsets.symmetric(horizontal: 15),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.topRight,
            colors: [
              Color(0xFF6884FF),
              Color(0xffC5D0FF),
            ]),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          16.gap,
          Row(
            children: [
              6.gap,
              // 头像
              Container(
                width: 85,
                height: 85,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100),
                    image: controller.userInfo.value.avatar == ''
                        ? const DecorationImage(
                        image: AssetImage(AssetsRes.USERLOGO),
                        fit: BoxFit.fill) : DecorationImage(
                        image: NetworkImage(controller.userInfo.value.avatar),
                        fit: BoxFit.fill)),
              ),
              Expanded(child: Container(
                padding: const EdgeInsets.only(top: 14),
                alignment: Alignment.centerLeft,
                height: 80,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Container(
                      alignment: Alignment.topLeft,
                      child: Row(
                        children: [
                          Text(controller.userInfo.value.name,
                              style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600)),
                          4.gap,
                          Flexible(
                              child:Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: const Color(0xffFFA24C),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Text(controller.userInfo.value.getSpiltOccupation() ?? '暂无'
                                  // child: Text('腐恶费饿哦访客哦佛额吉佛额吉佛额飞机哦额飞机饿哦就发额飞机哦额金佛'
                                  , maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(fontSize: 12,color: Colors.white),),
                              )
                          ),
                          4.gap,
                        ],
                      ),
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      margin: const EdgeInsets.only(top: 6),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const SizedBox(
                            child: Icon(Icons.phone, color: Colors.white ,size: 13,),
                          ),
                          2.gap,
                          Text(
                            controller.userInfo.value.phone,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 13,
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),),
              Container(
                child: TextButton.icon(
                  onPressed: () {
                    controller.resetIrDeviceData();
                  },
                  icon: Image.asset(AssetsRes.PHONE_UPDATE_DATA_ICON, width: 14,),
                  label: const Text(
                    "更新数据",
                    style: TextStyle(
                        fontSize: 13,
                        color: Colors.white),
                  ),
                ),
              ),
              6.gap
            ],
          ),

          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                19.gap,
                Row(
                  children: [
                    24.gap,
                    Expanded(child: _buildLabel('部门', "${controller.userInfo.value.deptName ?? '暂无'}"),),
                    5.gap,
                    Expanded(child: _buildLabel('专业', "${controller.userInfo.value.technicalPost ?? '暂无'}"),),
                    24.gap
                  ],
                ),
                10.gap,
                Row(
                  children: [
                    24.gap,
                    Expanded(child:_buildLabel('岗位', controller.userInfo.value.occupation ?? '暂无'),),
                  ],
                ),
                19.gap,
              ],
            ),
          )
        ],
      ),
    ));

  }

  _showSettingDialog() async {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Container(
          width: (440),
          height: (208),
          padding:EdgeInsets.all((16)),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    child: Row(
                      children: [
                        Container(
                          width: (5),
                          height: (20),
                          margin: const EdgeInsets.only(right: 13),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(14.0),
                            color: Color(0xFF5777FF),
                          ),
                        ),
                        const Text(
                          "设置",
                          style: TextStyle(
                            color: Color(0xFF2F303A),
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  InkWell(
                    onTap: () => Get.back(),
                    child: Container(
                      padding: EdgeInsets.all(5),
                      child: Icon(
                        Icons.close,
                        size: 20,
                        color: MyScreenUtil.ThemColor(),
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  InkWell(
                    onTap: () {
                      controller.checkAppUpgrade(showToast: true);
                    },
                    child: Container(
                      margin: const EdgeInsets.only(top: (30)),
                      padding: const EdgeInsets.only(
                        left: 20,
                        right: 20,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 1,
                          color: MyScreenUtil.FontColor(),
                        ),
                        borderRadius:
                        BorderRadius.circular((8)),
                      ),
                      child: Text(
                        '重新下载',
                        style: TextStyle(
                          color: MyScreenUtil.FontColor(),
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      controller.checkAppUpgrade(showToast: true);
                      Get.back();
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                        left: 20,
                        right: 20,
                      ),
                      margin: const EdgeInsets.only(top: 30),
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 1,
                          color: MyScreenUtil.FontColor(),
                        ),
                        borderRadius:
                        BorderRadius.circular((8)),
                      ),
                      child: Text(
                        '检查更新',
                        style: TextStyle(
                          color: MyScreenUtil.FontColor(),
                          fontSize: (16),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              InkWell(
                onTap: () {
                  Get.back();
                  Storage.removeData("userInfo");
                  offRoute(Routes.PHONE_LOGIN);
                },
                child: Container(
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(top: (50)),
                  height: (40),
                  decoration: BoxDecoration(
                    color: Color(0xFFFF5757),
                    borderRadius: BorderRadius.circular((8)),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: (24),
                        height: (24),
                        child: Image.asset("assets/images/icon/Power.png"),
                      ),
                      const Text(
                        "退出登陆",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: (18),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _showUpgradeDialog(VersionResp remoteVersion) {
    Get.dialog(
        barrierDismissible: false, // 禁止点击空白处退出
        AlertDialog(
          contentPadding: EdgeInsets.zero,
          content: Container(
            color: Colors.transparent,
            padding: EdgeInsets.zero,
            width: MyScreenUtil.width(400),
            height: MyScreenUtil.height(400),
            child: Stack(
              children: [
                Positioned(
                    left: 0,
                    right: 0,
                    child: Transform.translate(
                      offset: Offset(0, -40),
                      child: Container(
                        child: Image.asset(AssetsRes.UPDATE),
                      ),
                    )),
                Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: const Text("新版本升级",
                              style: TextStyle(fontSize: 17),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: Text(
                              "检测到新版本 ${remoteVersion.number}",
                              style: TextStyle(
                                  color: const Color.fromRGBO(134, 144, 156, 1),
                                  fontSize: 13),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(bottom: 16),
                            child: Text(
                              "${remoteVersion.description}",
                              style: TextStyle(
                                  color: Color.fromRGBO(78, 89, 105, 1),
                                  fontSize: 17),
                            ),
                          ),
                          Container(
                              padding: EdgeInsets.only(left: 16, right: 16),
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 1,
                                    child: ElevatedButton(
                                      onPressed: () {
                                        controller.downApp(remoteVersion);
                                        Get.back();
                                      },
                                      style: ButtonStyle(
                                        elevation: MaterialStateProperty.all(0),
                                      ),
                                      child: Text("立即升级", style: TextStyle(fontSize: 13),),
                                    ),
                                  )
                                ],
                              )),
                          Container(
                              padding: EdgeInsets.only(left: 16, right: 16),
                              margin: EdgeInsets.only(bottom: 16),
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 1,
                                    child: ElevatedButton(
                                      style: ButtonStyle(
                                        elevation: MaterialStateProperty.all(0),
                                        backgroundColor:
                                        MaterialStateProperty.all<Color>(
                                            const Color.fromRGBO(
                                                242, 243, 245, 1)),
                                      ),
                                      onPressed: () {
                                        Get.back();
                                      },
                                      child: const Text(
                                        "以后再说",
                                        style: TextStyle(
                                            fontSize: 13,
                                            color:
                                            Color.fromRGBO(21, 22, 25, 1)),
                                      ),
                                    ),
                                  )
                                ],
                              ))
                        ],
                      ),
                    ))
              ],
            ),
          ), // 主内容区
        ));
  }

   _buildLabel(String label, String value) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(label, style: const TextStyle(fontSize: 13 ,color: Colors.black),),
          const Text(':', style: TextStyle(fontSize: 13 ,color: Colors.black),),
          7.gap,
          Expanded(
              child:Text(value,
                style: const TextStyle(
                    fontSize: 13 ,
                    color: Colors.black,
                    overflow: TextOverflow.ellipsis),)
          ),
        ],
      );
   }
  
}


