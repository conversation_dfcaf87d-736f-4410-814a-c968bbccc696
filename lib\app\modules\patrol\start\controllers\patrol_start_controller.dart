import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../controllers/global_controller.dart';
import '../../../../controllers/sqflite_controller.dart';
import "../../../../data/user.dart";
import "../../../../data/roomTypeInfo.dart";
import '../../../../utils/screenutil.dart';
import 'package:flutter_scankit/flutter_scankit.dart';

class PatrolStartController extends GetxController {
  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();

  // 用户基本信息
  Rx<UserInfo> userInfo = Rx<UserInfo>(const UserInfo(
    eid: "",
    avatar: "",
    name: "",
    phone: "",
    passWorld: "",
    deptName: "",
    technicalPost: "",
    livingPlace: "",
    gender: 0,
    occupation: "",
    roomTypeName: "",
    createTime: 0,
    floor: "",
    shifts: "",
    roomType: "",
    isUpload: 0,
    patrolStartTimeStamp: 0,
    patrolEndTimeStamp: 0,
    companyId: "",
    companyName: "",
    deviceMaintainPer: 0,
  ));

  // 房间类型列表
  RxList roomTypeInfoList = [].obs;

  final count = 0.obs;

  @override
  void onInit() {
    super.onInit();
    insertPatrolStartTime();
    findRoomData();
    ever(globalController.patrolStart, (_) {
      findRoomData();
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  toastFun(text) {
    Fluttertoast.showToast(
        msg: text,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  // 根据storage中的用户id 查询user表中的用户信息
  findUserInfo() async {
    var userInfoData = await sqfliteController
        .findUsers("eid='${globalController.userInfo.value!.data.eid}'");
    final List<Map<String, dynamic>> userInfolist =
        userInfoData.map((userInfoItem) => userInfoItem.toJson()).toList();
    userInfo.value = userInfoData[0];
    if (userInfoData[0].patrolEndTime != null) {
      ifFinish();
    }
    update();
  }

  // 插入巡检时间
  insertPatrolStartTime() async {
    var userInfoData = await sqfliteController
        .findUsers("eid='${globalController.userInfo.value!.data.eid}'");
    DateTime date = DateTime.now();
    var month = date.month < 10 ? "0${date.month}" : "${date.month}";
    var day = date.day < 10 ? "0${date.day}" : "${date.day}";
    var hour = date.hour < 10 ? "0${date.hour}" : "${date.hour}";
    var minute = date.minute < 10 ? "0${date.minute}" : "${date.minute}";
    var second = date.second < 10 ? "0${date.second}" : "${date.second}";
    print("${date.year}-${month}-${day} ${hour}:${minute}:${second}");
    if (userInfoData[0].patrolStartTime == null) {
      sqfliteController.updateUserTable(
          "user",
          {
            "patrolStartTime":
                "${date.year}-${month}-${day} ${hour}:${minute}:${second}",
            "patrolStartTimeStamp": date.millisecondsSinceEpoch
          },
          "eid='${globalController.userInfo.value!.data.eid}'");
    }
    findUserInfo();
  }

  // 查询房间类型数据
  findRoomTypeInfo() async {
    var roomTypeInfoData = await sqfliteController.findRoomTypeInfo("");
    final List<Map<String, dynamic>> roomTypeInfoDataList =
        roomTypeInfoData.map((roomInfoItem) => roomInfoItem.toJson()).toList();
    roomTypeInfoList.value = roomTypeInfoData;
    update();
  }

  // 查询房间类型下面内室的巡检进度
  findRoomData() async {
    var roomTypeInfoData = await sqfliteController.findRoomTypeInfo("");
    for (var roomTypeInfoItem in roomTypeInfoData) {
      var roomData = await sqfliteController
          .findRoomData("type='${roomTypeInfoItem.roomType}'");
      final List<Map<String, dynamic>> roomDataList =
          roomData.map((item) => item.toJson()).toList();
      int accomplishCount = roomData.where((item) => item.isFinish == 1).length;
      var progress =
          (accomplishCount / roomData.length * 100).toStringAsFixed(0);
      sqfliteController.updateTable("roomTypeInfo", {"progress": progress},
          "roomType='${roomTypeInfoItem.roomType}'");
    }
    await findRoomTypeInfo();
  }

  // 判断是否已经巡检结束
  ifFinish() async {
    Get.dialog(
        barrierDismissible: false,
        AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: const [Text("提示")],
          ),
          content: Row(
            children: const [Text("请将此次巡检数据上传后在重新巡检")],
          ),
          actions: [
            ElevatedButton(
                onPressed: () {
                  Get.back();
                  Get.back();
                },
                child: const Text("返回首页"),
                style: ButtonStyle(backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return MyScreenUtil.ThemColor();
                }))),
          ],
        ));
  }

  goDetail(id) async {
    var roomData = await sqfliteController.findRoomData("roomId='${id}'");
    var roomTypeInfoData = await sqfliteController.findRoomTypeInfo("");
    // if()
    var roomList = [];
    roomData.forEach((item) {
      roomTypeInfoData.forEach((info) {
        if (info.roomType == item.type) {
          roomList.add(item);
        }
      });
    });
    if (roomList.length >= 1) {
      Get.toNamed("/device", arguments: {
        "roomName": "${roomData[0].name}-${roomData[0].roomId}",
        "roomType": roomData[0].type,
        "roomId": roomData[0].roomId,
        "name": roomData[0].name,
      });
    } else {
      toastFun('查无此房间');
    }
  }

  searchCode() {
    var scanKit;
    /**扫一扫相关配置 */
    // 相机和外部存储权限(获取安卓权限)

    scanKit = ScanKit();
    scanKit.addResultListen((val) async {
      goDetail(val);
    });

    final permissionGroup1 = const [
      Permission.camera,
      Permission.photos,
      Permission.storage,
    ];

    Timer(Duration(milliseconds: 300), () async {
      // 在延时0.3秒后执行的任务

      var pstatus = await permissionGroup1.request();
      var hasGrant = true;
      pstatus.forEach((key, value){
        if(value.isDenied){
          hasGrant = false;
        }
      });
      if (!hasGrant) {
        permissionGroup1.request();

      } else {
        try {
          await scanKit.startScan();
        } on PlatformException {
          print("扫一扫报错");
        }
      }

    });
  }
}
