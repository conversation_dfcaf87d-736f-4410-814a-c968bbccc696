import 'dart:math';

import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/patrol_record_controller.dart';

import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';

class PatrolRecordView extends GetView<PatrolRecordController> {
  const PatrolRecordView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        backgroundColor: Color.fromRGBO(246, 248, 250, 1),
        body: SafeArea(
          child: Container(
            width: MyScreenUtil.width(1392),
            margin: EdgeInsets.all(MyScreenUtil.width(24)),
            padding: EdgeInsets.only(
                left: MyScreenUtil.width(24),
                bottom: MyScreenUtil.width(24),
                right: MyScreenUtil.width(24)),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              // border: Border(bottom: )
            ),
            child: Column(
              children: [
                InkWell(
                  onTap: () => {Get.back()},
                  child: Container(
                    height: MyScreenUtil.height(60),
                    decoration: const BoxDecoration(
                      border: Border(
                        bottom:
                            BorderSide(width: 1, color: Color(0xFFFFeeeeee)),
                      ),
                      // color:Colors.red
                    ),
                    child: Row(children: [
                      Container(
                        width: MyScreenUtil.width(24),
                        height: MyScreenUtil.height(24),
                        child: Image.asset(
                          "assets/images/icon/left.png",
                          fit: BoxFit.cover,
                        ),
                      ),
                      Obx(() => Text(controller.titleName.value)),
                    ]),
                  ),
                ),
                Expanded(
                    flex: 1,
                    child: Container(
                      padding:EdgeInsets.only(top:MyScreenUtil.height(16),bottom:MyScreenUtil.height(16)),
                      child: DataList(),
                    )),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 数据列表
  Widget DataList() {
    return GridView.count(
      padding: const EdgeInsets.only(left: 20, right: 20),
      crossAxisCount: 2,
      childAspectRatio: MyScreenUtil.getScreenWidth() / 300,
      crossAxisSpacing: 10,
      mainAxisSpacing: 10,
      children: controller.historyList.isEmpty
          ? [const Text("暂无巡检记录")]
          : controller.historyList
              .map((item) {
                return historyItemFun(item);
              })
              .toList()
              .cast<Widget>(),
      // children: [
      //   historyItemFun()
      // ],
    );
  }

  // 巡检上传的数据项模板
  historyItemFun(item) {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(
              width: 1, color: const Color.fromRGBO(143, 147, 153, 1)),
          borderRadius: BorderRadius.circular(10)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Container(
            width: MyScreenUtil.width(140),
            height: MyScreenUtil.width(140),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(MyScreenUtil.radius(140)),
                // color:MyScreenUtil.ThemColor(),
                image: item.avatar != ""
                    ? DecorationImage(
                        image: NetworkImage("${item.avatar}"),
                        fit: BoxFit.cover)
                    : const DecorationImage(
                        image: AssetImage("assets/images/userLogo.png"),
                        fit: BoxFit.cover)),
          ),
          Container(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Row(
                  children: [
                    Container(
                      width: MyScreenUtil.width(150),
                      child: Text(
                        "巡检员",
                        style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),
                      ),
                    ),
                    Container(
                      width: MyScreenUtil.width(150),
                      child: Text(
                        "${item.name}",
                        style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Container(
                      width: MyScreenUtil.width(150),
                      child: Text(
                        "手机号",
                        style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),
                      ),
                    ),
                    Container(
                      width: MyScreenUtil.width(150),
                      child: Text(
                        "${item.mobile}",
                        style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Container(
                      width: MyScreenUtil.width(150),
                      child: Text(
                        "开始巡检时间",
                        style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),
                      ),
                    ),
                    Container(
                      width: MyScreenUtil.width(200),
                      child: Text("${item.patrolStartTime}",
                          style:
                              TextStyle(fontSize: MyScreenUtil.fontSize(18))),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Container(
                      width: MyScreenUtil.width(150),
                      child: Text("结束巡检时间",
                          style:
                              TextStyle(fontSize: MyScreenUtil.fontSize(18))),
                    ),
                    Container(
                      width: MyScreenUtil.width(200),
                      child: Text("${item.patrolEndTime}",
                          style:
                              TextStyle(fontSize: MyScreenUtil.fontSize(18))),
                    ),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
