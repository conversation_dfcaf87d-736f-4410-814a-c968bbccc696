

import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';

import '../../../res/assets_res.dart';

class ChestCard extends StatefulWidget {

  late IrChest irChest;

  final VoidCallback? click;

  ChestCard({super.key , required this.irChest , this.click});

  @override
  State<ChestCard> createState() => _ChestCardState();
}

class _ChestCardState extends State<ChestCard> with SingleTickerProviderStateMixin{

  late AnimationController? _animationController;

  late Animation<int> _animation;
  var begin = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(vsync: this, duration: Duration(milliseconds: 1500));
    startAnimation();
  }

  startAnimation() async{
    await Future.delayed(Duration(milliseconds: 300));
    _animationController?.forward();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => widget.click?.call(),
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 13 ,vertical: 10),
        decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: const [
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, 0.1),
                offset: Offset(0, 0),
                blurRadius: 5,
                spreadRadius: 0,
              ),
            ],
            borderRadius:
            BorderRadius.circular(12)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  SizedBox(
                    width: 22,
                    height: 22,
                    child: Image.asset(
                      AssetsRes.ROOM_TITLE,
                      fit: BoxFit.cover,
                    ),
                  ),
                  2.gap,
                  Expanded(child: Text(
                    "${widget.irChest.chestName}",
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    style: const TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w600),
                  )),
                ],
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 10),
              alignment: Alignment.centerLeft,
              child: Text(
                "设备数量:  ${widget.irChest.deviceCount}",
                style: const TextStyle(
                    color: Colors.black,
                    fontSize: 14),
              ),
            ),

            12.gap,

            Row(
              children: [
                Expanded(
                    child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                  child: AnimatedBuilder(animation: _animationController!, builder: (ctx , child){
                    _animation = IntTween(begin: begin, end: widget.irChest.getIntProgress())
                        .animate(CurvedAnimation(parent: _animationController!, curve: Curves.easeInOut));
                    return LinearProgressIndicator(
                      minHeight: 8,
                      backgroundColor: Colors.grey[200],
                      valueColor: const AlwaysStoppedAnimation(Colors.blue),
                      value: (_animation.value )/100,
                    );
                  },),
                )),
                8.gap,

                AnimatedBuilder(animation: _animationController!, builder: (ctx ,child){
                  return Text("${_animation.value}%" , style: const TextStyle(fontSize: 13, color: Colors.black));
                  // return Text("${_animationController!.value}%" , style: const TextStyle(fontSize: 13, color: Colors.black));
                })


              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  dispose() {
    super.dispose();
    _animationController?.dispose();
  }
}