
import 'package:dio/dio.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/toast.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/loading_helper.dart';


class ErrorCodeInterceptor extends Interceptor {

  ErrorCodeInterceptor();

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    try {
      var errorCode = err.response?.statusCode ?? 200;
      if(errorCode != 200){
        LoadingManager.disMiss();
        var errorResponse = err.response;
        var errorMsg = '';
        if(errorResponse != null){
          errorMsg = errorResponse.data['error'];
        }
        toast('错误码：${errorCode}，错误信息： ${errorMsg}');
      }
    }catch(e) {
      print(e);
    }
    super.onError(err, handler);
  }




}

