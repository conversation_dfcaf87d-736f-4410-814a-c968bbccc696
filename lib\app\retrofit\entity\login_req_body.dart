
import '../../model/login/loginModel.dart';
import 'package:json_annotation/json_annotation.dart';

class LoginReqBody {
  String? user;
  String? password;

  LoginReqBody({
    required this.user,
    required this.password});

  LoginReqBody.fromJson(Map<String, dynamic> json){
    user = json['user'];
    password = json['password'];
  }

  Map<String, dynamic> toJson() => {'user':user,'password':password};
}








