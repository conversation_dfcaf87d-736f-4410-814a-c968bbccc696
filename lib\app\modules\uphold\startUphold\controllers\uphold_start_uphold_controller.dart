import 'dart:convert';
import 'dart:ffi';
// import 'dart:html';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'package:flutter_scankit/flutter_scankit.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sjzx_patrol_system_mobile/app/controllers/socket_server_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/controllers/upholdSqflite_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';
import "../../../../utils/mySigature.dart";
import "../../../../controllers/global_controller.dart";
import "../../../../controllers/socket_client_controller.dart";
import 'package:image_picker/image_picker.dart';

import "../views/deviceInfo.dart";
import '../views/premise.dart';
import '../views/ensure.dart';
import '../views/toolPage.dart';
import '../views/recall.dart';
import '../views/process.dart';

class UpholdStartUpholdController extends GetxController {
  //TODO: Implement UpholdStartUpholdController
  GlobalController globalController = Get.find();
  SocketClientController socketClientController = Get.find(); // 客户端
  SocketServerController socketServerController = Get.find(); //  服务端
  UpholdSQLController upholdSQLController = Get.find();

  // 记录 ListView 的位置
  ScrollController scrollController = ScrollController();
  RxDouble offsetNum = 0.0.obs;

  // 动态标题
  RxString titleName = "".obs;
  //左侧内容区的映射
  RxMap componentArr = {
    0: DeviceInfo(), // 设备信息
    1: Premise(), // 前提条件
    2: Ensure(), // 安全保障
    3: ToolPage(), // 工具及备份条件
    4: Recall(), // 回退计划
    5: Process() // 操作流程
  }.obs;
  RxMap componentNameArr = {
    0: "设备信息", // 设备信息
    1: "先提条件", // 前提条件
    2: "安全保障", // 安全保障
    3: "工具及备件要求", // 工具及备份条件
    4: "回退计划", // 回退计划
    5: "操作流程" // 操作流程
  }.obs;
  RxInt componentIndex = 0.obs;
  String? base64Image = "";
  // 拍照
  final ImagePicker picker = ImagePicker();
  // RxList<XFile> pickedFileData = <XFile>[].obs;

  // File? _imageFile;

  /**扫一扫相关配置 */
  // 相机和外部存储权限(获取安卓权限)
  final permissions = const [
    Permission.storage,
    Permission.camera,
  ];
  // 相机和外部存储权限(获取iod权限)
  final permissionGroup = const [
    Permission.camera,
    Permission.photos,
  ];

  final permissionGroups = const [
    Permission.camera,
    Permission.photos,
    Permission.storage,
  ];

  late bool isCustom;
  late ScanKit scanKit;
  RxString code = "".obs;

  /**维护工单项步骤验证 */
  RxInt maintenanceIndex = 0.obs;
  /**数据响应式 */
  RxList deviceInfoList = [].obs; // 设备信息列表
  RxList premiseDataList = [].obs; //前提条件
  RxList guaranteeList = [].obs; // 安全保障
  RxList toolList = [].obs; // 工具及备件要求
  RxList recallList = [].obs; // 回退计划
  RxList processList = [].obs; // 操作流程

  RxMap workItemData = {}.obs;
  RxString workId = "".obs; // 工单id
  RxList userInfo = [].obs; // 当前用户信息
  RxInt userRole = 0.obs; // 当前用户角色
  RxString userNames = "".obs; // 执行人名单
  RxList userNamesList = [].obs; //执行&监督人名单详细数据
  RxMap lignatureViewList = {}.obs; // 签字回显

  @override
  void onInit() async {
    super.onInit();
    // 工单全部数据
    workItemData.value = Get.arguments['workItem'];
    // print("打印当前工单设备 ${workItemData.value}");
    // 工单id
    workId.value = Get.arguments['id'];
    // 页面标题
    titleName.value =
        "${Get.arguments['deptName']}-${Get.arguments['deviceGroupName']}-${Get.arguments['typeName']}:${Get.arguments['installationSite']}";
    // 获取当前工单的执行步骤
    await getStep();
    // 获取用户身份
    await handelRole();
    // 获取执行人名单
    await getEnforcerList();
    // 签字回显
    signatureEcho(componentNameArr[componentIndex.value]);

    // 获取设备信息
    getDeviceInfo(Get.arguments['id']); // 工单id
    // 获取前提条件
    getPremiseData(Get.arguments['id']);
    // 获取安全保障
    getGuaranteeData(Get.arguments['id']);
    // 获取工具及备件要求
    getToolData(Get.arguments['id']);
    // 回退计划
    getRecallData(Get.arguments['id']);
    // 维护工单
    getProcessData(Get.arguments['id']);

    // 初始化扫一扫,并监听扫描的结果
    scanKit = ScanKit();
    scanKit.onResult.listen((val) {
      scanResult(val);
    });

    // 监听控制变量，如果发生变量查找设备表更新更新相关的视图
    ever(globalController.deviceIndex, (value) {
      // 设备信息
      // print("监听到设备信息通信后台数据库发生更新 $value");
    });

    ever(globalController.premiseIndex, (value) {
      // 先提条件
      getPremiseData(workId.value);
    });

    ever(globalController.guaranteeIndex, (value) {
      // 安全保障
      getGuaranteeData(workId.value);
    });

    ever(globalController.toolIndex, (value) {
      // 安全保障
      getToolData(workId.value);
    });

    ever(globalController.processIndex, (value) {
      // 操作流程
      getProcessData(workId.value);
    });

    ever(globalController.signIndex, (value) {
      // 签字回显
      // print("监听签字变化 ${value}");
      signatureEcho(componentNameArr[componentIndex.value]);
    });

    /**记录 ListView 的位置 */
    scrollController.addListener(() {
      offsetNum.value = scrollController.offset;
    });
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    scanKit.dispose();
    super.onClose();
  }

  /*获取该工单的步骤 */
  getStep() async {
    var workOrderData =
        await upholdSQLController.findWorkOrder("id='${workId.value}'");
    // print("步骤 ${workOrderData[0].finishStep}");
    componentIndex.value = workOrderData[0].finishStep;
    update();
  }

  /**设备信息业务代码 */
  // 获取设备信息相关数据
  getDeviceInfo(id) async {
    deviceInfoList.value = [];
    var deviceInfoResult = await upholdSQLController
        .findMaintainItem("woid='${id}' AND stepName='设备信息'");
    final List<Map<String, dynamic>> deviceInfoJson =
        deviceInfoResult.map((item) => item.toJson()).toList();
    deviceInfoList.value = deviceInfoResult;
    // print(jsonEncode(deviceInfoList));
    // deviceInfoResult.forEach((item){
    //   deviceInfoList.add(deviceInfo_biserial(item.content??'',item.operation??''));
    // });
    update();
  }

  //设备信息项目完成
  deviceInfo_finish() {
    // if(globalController.socketType == 0){
    //   toastFun();
    //   return;
    // }
    DateTime date = DateTime.now();
    var deviceFlag = false;
    // if(userRole.value == 0){
    //   // 执行人操作，修改本地库
    //   upholdSQLController.updateTable(
    //     "maintainItem",
    //     {
    //       "operatorId":userInfo.value[0].uid,
    //       "operatorName":userInfo.value[0].name,
    //       "operatorResult":1,
    //       "operatorTime":date.millisecondsSinceEpoch
    //     },
    //     "woid='${workId.value}' AND stepName='设备信息'"
    //   );
    //   var deviceSend = {
    //     "tabelName":"maintainItem", // 本地数据库表名
    //     "id":workId.value, // 工单id
    //     "stepName":"设备信息", // 用于区分项目
    //     "userRole":userRole.value,
    //     "data":{
    //       "operatorId":userInfo.value[0].uid, // 登录的用户id
    //       "operatorName":userInfo.value[0].name, // 登录的用户姓名
    //       "operatorResult":1, // 执行状态
    //       "operatorTime":date.millisecondsSinceEpoch // 执行时间
    //     }
    //   };
    //   // 向监督人发送数据
    //   handelSocket(deviceSend);
    // }else{
    // 监督人操作，修改本地数据
    upholdSQLController.updateTable(
        "maintainItem",
        {
          "supervisorId": userInfo.value[0].uid,
          "supervisorName": userInfo.value[0].name,
          "supervisorResult": 1,
          "supervisorTime": date.millisecondsSinceEpoch
        },
        "woid='${workId.value}' AND stepName='设备信息'");
    var deviceSend = {
      "tabelName": "maintainItem",
      "id": workId.value,
      "stepName": "设备信息",
      "userRole": userRole.value,
      "data": {
        "supervisorId": userInfo.value[0].uid,
        "supervisorName": userInfo.value[0].name,
        "supervisorResult": 1,
        "supervisorTime": date.millisecondsSinceEpoch
      }
    };
    // 向执行人发送数据
    // handelSocket(deviceSend);
    // }
    handelComponentIndex(1, "workOrder");
  }

  /**先提条件业务代码 */
  getPremiseData(id) async {
    // print('id:==============${id}');
    premiseDataList.value = [];
    var premiseDataResult = await upholdSQLController
        .findMaintainItem("woid='${id}' AND stepName='先提条件'");
    final List<Map<String, dynamic>> premiseDataJson =
        premiseDataResult.map((item) => item.toJson()).toList();
    premiseDataList.value = premiseDataResult;
    scrollController.jumpTo(offsetNum.value);
    update();
  }

  // 先提条件状态计算
  premiseComputeStatus(index) {
    var premiseDataLength = premiseDataList.length;
    var premiseData = premiseDataList;
    // if(premiseData[index].operatorResult!=0 && premiseData[index].supervisorResult!=0){
    //   return "已完成";
    // }else{
    //   return  "正在维护";
    // }

    // 执行人 userRole.value == 0
    if (userRole.value == 0) {
      if (premiseData[index].status != 0) {
        return "已完成";
      } else if (maintenanceIndex.value == index) {
        return "正在维护";
      } else if (premiseData[index].status == 0) {
        return "等待维护";
      } else {
        return "等待维护";
      }
    }
    //监督人 userRole.value == 1
    if (userRole.value == 1) {
      if (premiseData[index].status != 0) {
        return "已完成";
      } else if (premiseData[index].status != 0) {
        return "正在维护";
      } else {
        return "等待维护";
      }
    }
  }

  // 先提条件执行
  premiseExecute(premiseItem) async {
    // if(globalController.socketType == 0){
    //   toastFun();
    //   return;
    // }
    DateTime date = DateTime.now();
    /**查找 gapPrice 字段从库中查出来发送给另外的终端*/
    var premiseMaintainItem = await upholdSQLController.findMaintainItem(
        "woid='${premiseItem.woid}' AND stepName='先提条件' AND content='${premiseItem.content}'");
    print(jsonEncode(premiseMaintainItem));
    var intercept1 = true;//判断输入框是否输入
    if(premiseMaintainItem[0].fillingValue == 1){
      var gapPriceList = premiseMaintainItem[0]?.gapPrice;
      var inputList = jsonDecode(gapPriceList ?? '') ?? '';
      for(var item in inputList){
        if(item.isEmpty){
          intercept1 = false;
          // print('999999');
        }
      }
    }
    if(premiseMaintainItem[0].fillingValue == 1 && !intercept1){
      Fluttertoast.showToast(
        msg: "请检查所有输入项是否填写",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
      return;
    }
    if(premiseMaintainItem[0].fillingPictureValue == 1 && (premiseMaintainItem[0].gapPicture?.isEmpty ?? false)){
      Fluttertoast.showToast(
        msg: "请上传图片后再进行操作",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
      return;
    }
    // 执行人操作
    // if(userRole.value == 0){
    //   // 修改本地库
    //   upholdSQLController.updateTable(
    //     "maintainItem",
    //     {
    //       "operatorId":userInfo.value[0].uid,
    //       "operatorName":userInfo.value[0].name,
    //       "operatorResult":1,
    //       "operatorTime":date.millisecondsSinceEpoch
    //     },
    //     "woid='${workId.value}' AND stepName='先提条件' AND content='${premiseItem.content}'"
    //   );

    //   // 向监督人发送数据
    var deviceSend = {
      "tabelName": "maintainItem", // 本地数据库表名
      "id": workId.value, // 工单id
      "stepName": "先提条件", // 用于区分项目
      "userRole": userRole.value, // 角色标识
      "content": '${premiseItem.content}', // 用于maintainItem的条件查询
      "data": {
        "operatorId": userInfo.value[0].uid, // 登录的用户id
        "operatorName": userInfo.value[0].name, // 登录的用户姓名
        "operatorResult": 1, // 执行状态
        "operatorTime": date.millisecondsSinceEpoch, // 执行时间
        "gapPrice": premiseMaintainItem[0].gapPrice, // 输入框的值
      }
    };
    //   // 通知更新工单状态改变
    //   maintenanceIndex.value++;
    //   // 向监督人发送数据
    //   handelSocket(deviceSend);
    // }
    // 监督人操作
    if (userRole.value == 1) {
      // 监督人操作，修改本地数据
      upholdSQLController.updateTable(
          "maintainItem",
          {
            "supervisorId": userInfo.value[0].uid,
            "supervisorName": userInfo.value[0].name,
            "operatorResult": 1,
            "supervisorResult": 1,
            "supervisorTime": date.millisecondsSinceEpoch,
            "status": 1
          },
          "woid='${workId.value}' AND stepName='先提条件' AND content='${premiseItem.content}'");
      var deviceSend = {
        "tabelName": "maintainItem",
        "id": workId.value,
        "stepName": "先提条件",
        "userRole": userRole.value,
        "content": '${premiseItem.content}',
        "data": {
          "supervisorId": userInfo.value[0].uid,
          "supervisorName": userInfo.value[0].name,
          "supervisorResult": 1,
          "supervisorTime": date.millisecondsSinceEpoch,
          "gapPrice": premiseMaintainItem[0].gapPrice,
        }
      };
      // 通知更新工单状态改变
      maintenanceIndex.value++;
      // 向执行人发送数据
      // handelSocket(deviceSend);
    }
    // 提价完成后查看自己的数据
    getPremiseData(workId.value);
  }

  // 先提条件输入框
  premiseInputValue(premiseItem, value, index) async {
    // 执行人操作
    /**将该条数据找出后，解析 gapPrice 字符变为 list 类型， 根据 index 修改对应位置的值，最后已json字符串的形式存到库中 */
    var premiseMaintainItem = await upholdSQLController.findMaintainItem(
        "woid='${premiseItem.woid}' AND stepName='先提条件' AND content='${premiseItem.content}'");
    final List<Map<String, dynamic>> premiseMaintainItemJson =
        premiseMaintainItem.map((item) => item.toJson()).toList();
    var premiseGapPrice =
        json.decode(premiseMaintainItem[0].gapPrice as String);
    premiseGapPrice[index] = value;
    // 写入到数据库
    upholdSQLController.updateTable(
        "maintainItem",
        {
          "gapPrice": json.encode(premiseGapPrice),
        },
        "woid='${premiseItem.woid}' AND stepName='先提条件' AND content='${premiseItem.content}'");
  }

  /**安全保障 */
  getGuaranteeData(id) async {
    guaranteeList.value = [];
    var guaranteeDataResult = await upholdSQLController
        .findMaintainItem("woid='${id}' AND stepName='安全保障'");
    final List<Map<String, dynamic>> guaranteeDataJson =
        guaranteeDataResult.map((item) => item.toJson()).toList();
    guaranteeList.value = guaranteeDataResult;
    scrollController.jumpTo(offsetNum.value);
    update();
  }

  // 安全保障状态判断
  guaranteeComputeStatus(index) {
    var guaranteeDataLength = guaranteeList.length;
    var guaranteeData = guaranteeList;
    // 执行人操作
    if (userRole.value == 0) {
      if (guaranteeData[index].operatorResult != 0 &&
          guaranteeData[index].supervisorResult != 0) {
        return "已完成";
      } else if (maintenanceIndex.value == index) {
        return "正在维护";
      } else if (guaranteeData[index].operatorResult == 0 &&
          guaranteeData[index].supervisorResult == 0) {
        return "等待维护";
      } else {
        return "等待维护";
      }
    }
    //监督人 userRole.value == 1
    if (userRole.value == 1) {
      if (guaranteeData[index].status != 0) {
        return "已完成";
      } else if (guaranteeData[index].status != 0) {
        return "正在维护";
      } else {
        return "等待维护";
      }
    }
  }

  // 安全保障执行
  guaranteeExecute(guaranteeItem) async {
    // if(globalController.socketType == 0){
    //   toastFun();
    //   return;
    // }
    DateTime date = DateTime.now();
    /**查找 gapPrice 字段从库中查出来发送给另外的终端*/
    var guaranteeMaintainItem = await upholdSQLController.findMaintainItem(
        "woid='${guaranteeItem.woid}' AND stepName='安全保障' AND content='${guaranteeItem.content}'");

    var intercept1 = true;//判断输入框是否输入
    if(guaranteeMaintainItem[0].fillingValue == 1){
      var gapPriceList = guaranteeMaintainItem[0]?.gapPrice;
      var inputList = jsonDecode(gapPriceList ?? '') ?? '';
      for(var item in inputList){
        if(item.isEmpty){
          intercept1 = false;
        }
      }
    }
    if(guaranteeMaintainItem[0].fillingValue == 1 && !intercept1){
      Fluttertoast.showToast(
        msg: "请检查所有输入项是否填写",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
      return;
    }
    if(guaranteeMaintainItem[0].fillingPictureValue == 1 && (guaranteeMaintainItem[0].gapPicture?.isEmpty ?? false)){
      Fluttertoast.showToast(
        msg: "请上传图片后再进行操作",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
      return;
    }
    // 执行人操作
    // if (userRole.value == 0) {
    //   // 修改本地库
    //   upholdSQLController.updateTable(
    //       "maintainItem",
    //       {
    //         "operatorId": userInfo.value[0].uid,
    //         "operatorName": userInfo.value[0].name,
    //         "operatorResult": 1,
    //         "operatorTime": date.millisecondsSinceEpoch
    //       },
    //       "woid='${workId.value}' AND stepName='安全保障' AND content='${guaranteeItem.content}'");
    //   // 向监督人发送数据
    //   var deviceSend = {
    //     "tabelName": "maintainItem", // 本地数据库表名
    //     "id": workId.value, // 工单id
    //     "stepName": "安全保障", // 用于区分项目
    //     "userRole": userRole.value,
    //     "content": "${guaranteeItem.content}",
    //     "data": {
    //       "operatorId": userInfo.value[0].uid, // 登录的用户id
    //       "operatorName": userInfo.value[0].name, // 登录的用户姓名
    //       "operatorResult": 1, // 执行状态
    //       "operatorTime": date.millisecondsSinceEpoch, // 执行时间
    //       "gapPrice": guaranteeMaintainItem[0].gapPrice, // 安全保障输入值
    //     }
    //   };
    // 通知更新工单状态改变
    // maintenanceIndex.value++;
    // // 向监督人发送数据
    // handelSocket(deviceSend);
    // }
    // 监督人操作
    if (userRole.value == 1) {
      // 监督人操作，修改本地数据
      upholdSQLController.updateTable(
          "maintainItem",
          {
            "supervisorId": userInfo.value[0].uid,
            "supervisorName": userInfo.value[0].name,
            "operatorResult": 1,
            "supervisorResult": 1,
            "supervisorTime": date.millisecondsSinceEpoch,
            "status": 1,
          },
          "woid='${workId.value}' AND stepName='安全保障' AND content='${guaranteeItem.content}'");
      // 通知更新工单状态改变
      maintenanceIndex.value++;
      // 向执行人发送数据
      // handelSocket(deviceSend);
    }
    // 提价完成后查看自己的数据
    getGuaranteeData(workId.value);
  }

  // 安全保障输入模块
  guaranteeInputValue(guaranteeItem, value, index) async {
    // 执行人操作
    /**将该条数据找出后，解析 gapPrice 字符变为 list 类型， 根据 index 修改对应位置的值，最后已json字符串的形式存到库中 */
    var guaranteeMaintainItem = await upholdSQLController.findMaintainItem(
        "woid='${guaranteeItem.woid}' AND stepName='安全保障' AND content='${guaranteeItem.content}'");
    final List<Map<String, dynamic>> guaranteeMaintainItemJson =
        guaranteeMaintainItem.map((item) => item.toJson()).toList();
    var guaranteeGapPrice =
        json.decode(guaranteeMaintainItem[0].gapPrice as String);
    guaranteeGapPrice[index] = value;
    // 写入到数据库
    upholdSQLController.updateTable(
        "maintainItem",
        {
          "gapPrice": json.encode(guaranteeGapPrice),
        },
        "woid='${guaranteeItem.woid}' AND stepName='安全保障' AND content='${guaranteeItem.content}'");
  }

  /**工具及备件要求 */
  getToolData(id) async {
    toolList.value = [];
    var toolDataResult = await upholdSQLController
        .findMaintainItem("woid='${id}' AND stepName='工具及备件要求'");
    final List<Map<String, dynamic>> toolDataJson =
        toolDataResult.map((item) => item.toJson()).toList();
    toolList.value = toolDataResult;
    scrollController.jumpTo(offsetNum.value);
    update();
  }

  // 工具及备件要求状态判断
  toolComputeStatus(index) {
    var toolDataLength = toolList.length;
    var toolData = toolList;
    // 监督人
    if (userRole.value == 0) {
      if (toolData[index].operatorResult != 0 &&
          toolData[index].supervisorResult != 0) {
        return "已完成";
      } else if (maintenanceIndex.value == index) {
        return "正在维护";
      } else if (toolData[index].operatorResult == 0 &&
          toolData[index].supervisorResult == 0) {
        return "等待维护";
      } else {
        return "等待维护";
      }
    }
    if (userRole.value == 1) {
      if (toolData[index].status != 0) {
        return "已完成";
      } else if (toolData[index].status != 0) {
        return "正在维护";
      } else {
        return "等待维护";
      }
    }
  }

  // 工具及备件要求执行
  toolExecute(toolItem) async {
    // if(globalController.socketType == 0){
    //   toastFun();
    //   return;
    // }
    DateTime date = DateTime.now();
    var toolMaintainItem = await upholdSQLController.findMaintainItem(
        "woid='${toolItem.woid}' AND stepName='工具及备件要求' AND content='${toolItem.content}'");
    var intercept1 = true;//判断输入框是否输入
    if(toolMaintainItem[0].fillingValue == 1){
      var gapPriceList = toolMaintainItem[0]?.gapPrice;
      var inputList = jsonDecode(gapPriceList ?? '') ?? '';
      for(var item in inputList){
        if(item.isEmpty){
          intercept1 = false;
        }
      }
    }
    if(toolMaintainItem[0].fillingValue == 1 && !intercept1){
      Fluttertoast.showToast(
        msg: "请检查所有输入项是否填写",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
      return;
    }
    if(toolMaintainItem[0].fillingPictureValue == 1 && (toolMaintainItem[0].gapPicture?.isEmpty ?? false)){
      Fluttertoast.showToast(
        msg: "请上传图片后再进行操作",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
      return;
    }

    // 执行人操作
    if (userRole.value == 0) {
      // 修改本地库
      upholdSQLController.updateTable(
          "maintainItem",
          {
            "operatorId": userInfo.value[0].uid,
            "operatorName": userInfo.value[0].name,
            "operatorResult": 1,
            "operatorTime": date.millisecondsSinceEpoch
          },
          "woid='${workId.value}' AND stepName='工具及备件要求' AND content='${toolItem.content}' ");
      // 向监督人发送数据
      var deviceSend = {
        "tabelName": "maintainItem", // 本地数据库表名
        "id": workId.value, // 工单id
        "stepName": "工具及备件要求", // 用于区分项目
        "userRole": userRole.value,
        "content": "${toolItem.content}",
        "data": {
          "operatorId": userInfo.value[0].uid, // 登录的用户id
          "operatorName": userInfo.value[0].name, // 登录的用户姓名
          "operatorResult": 1, // 执行状态
          "operatorTime": date.millisecondsSinceEpoch, // 执行时间
          "gapPrice": toolMaintainItem[0].gapPrice, // 工具及备件要求输入值
        }
      };
      // // 通知更新工单状态改变
      // maintenanceIndex.value++;
      // // 向监督人发送数据
      // handelSocket(deviceSend);
    }
    // 监督人操作
    if (userRole.value == 1) {
      // 监督人操作，修改本地数据
      upholdSQLController.updateTable(
          "maintainItem",
          {
            "supervisorId": userInfo.value[0].uid,
            "supervisorName": userInfo.value[0].name,
            "operatorResult": 1,
            "supervisorResult": 1,
            "supervisorTime": date.millisecondsSinceEpoch,
            "status": 1,
          },
          "woid='${workId.value}' AND stepName='工具及备件要求' AND content='${toolItem.content}'");
      var deviceSend = {
        "tabelName": "maintainItem",
        "id": workId.value,
        "stepName": "工具及备件要求",
        "userRole": userRole.value,
        "content": "${toolItem.content}",
        "data": {
          "supervisorId": userInfo.value[0].uid,
          "supervisorName": userInfo.value[0].name,
          "supervisorResult": 1,
          "supervisorTime": date.millisecondsSinceEpoch,
          "gapPrice": toolMaintainItem[0].gapPrice, // 工具及备件要求输入值
        }
      };
      // // 通知更新工单状态改变
      maintenanceIndex.value++;
      // // 向执行人发送数据
      // handelSocket(deviceSend);
    }
    // 提价完成后查看自己的数据
    getToolData(workId.value);
  }

  //工具及备件输入模块
  toolInputValue(toolItem, value, index) async {
    // 执行人操作
    /**将该条数据找出后，解析 gapPrice 字符变为 list 类型， 根据 index 修改对应位置的值，最后已json字符串的形式存到库中 */
    var toolMaintainItem = await upholdSQLController.findMaintainItem(
        "woid='${toolItem.woid}' AND stepName='工具及备件要求' AND content='${toolItem.content}'");
    final List<Map<String, dynamic>> toolMaintainItemJson =
        toolMaintainItem.map((item) => item.toJson()).toList();
    var toolGapPrice = json.decode(toolMaintainItem[0].gapPrice as String);
    toolGapPrice[index] = value;
    // 写入到数据库
    upholdSQLController.updateTable(
        "maintainItem",
        {
          "gapPrice": json.encode(toolGapPrice),
        },
        "woid='${toolItem.woid}' AND stepName='工具及备件要求' AND content='${toolItem.content}'");
  }

  /**回退计划 */
  getRecallData(id) async {
    recallList.value = [];
    var recallDataResult = await upholdSQLController
        .findMaintainItem("woid='${id}' AND stepName='回退计划'");
    final List<Map<String, dynamic>> recallDataJson =
        recallDataResult.map((item) => item.toJson()).toList();
    recallList.value = recallDataResult;
    update();
  }

  recallExecute(recallItem) {
    // if(globalController.socketType == 0){
    //   toastFun();
    //   return;
    // }
    DateTime date = DateTime.now();
    // 执行人操作
    if (userRole.value == 0) {
      // 修改本地库
      upholdSQLController.updateTable(
          "maintainItem",
          {
            "operatorId": userInfo.value[0].uid,
            "operatorName": userInfo.value[0].name,
            "operatorTime": date.millisecondsSinceEpoch
          },
          "woid='${workId.value}' AND stepName='回退计划'");
      // 向监督人发送数据
      var deviceSend = {
        "tabelName": "maintainItem", // 本地数据库表名
        "id": workId.value, // 工单id
        "stepName": "回退计划", // 用于区分项目
        "userRole": userRole.value,
        "data": {
          "operatorId": userInfo.value[0].uid, // 登录的用户id
          "operatorName": userInfo.value[0].name, // 登录的用户姓名
          "operatorResult": 1, // 执行状态
          "operatorTime": date.millisecondsSinceEpoch // 执行时间
        }
      };
      // 向监督人发送数据
      handelSocket(deviceSend);
    }
    // 监督人操作
    if (userRole.value == 1) {
      // 监督人操作，修改本地数据
      upholdSQLController.updateTable(
          "maintainItem",
          {
            "supervisorId": userInfo.value[0].uid,
            "supervisorName": userInfo.value[0].name,
            "operatorResult": 1,
            "supervisorResult": 1,
            "supervisorTime": date.millisecondsSinceEpoch
          },
          "woid='${workId.value}' AND stepName='回退计划'");
      var deviceSend = {
        "tabelName": "maintainItem",
        "id": workId.value,
        "stepName": "回退计划",
        "userRole": userRole.value,
        "data": {
          "supervisorId": userInfo.value[0].uid,
          "supervisorName": userInfo.value[0].name,
          "supervisorResult": 1,
          "supervisorTime": date.millisecondsSinceEpoch
        }
      };
      // 向执行人发送数据
      handelSocket(deviceSend);
    }
  }

  /**操作流程 */
  getProcessData(id) async {
    processList.value = [];
    var processDataResult = await upholdSQLController
        .findMaintainItem("woid='${id}' AND stepName='操作流程'");
    final List<Map<String, dynamic>> processDataJson =
        processDataResult.map((item) => item.toJson()).toList();
    // 格式化数据按照 parentName 分组
    final groupByParentName =
        groupBy(processDataResult, (obj) => obj.parentName);
    final result = groupByParentName.entries.map((entry) {
      final parentName = entry.key;
      final child = entry.value.map((item) {
        return {
          'woid': item.woid,
          'stepName': item.stepName,
          'id': item.id,
          'parentName': item.parentName,
          'content': item.content,
          'operation': item.operation,
          'operatorId': item.operatorId,
          'operatorName': item.operatorName,
          'operatorResult': item.operatorResult,
          'operatorTime': item.operatorTime,
          'supervisorId': item.supervisorId,
          'supervisorName': item.supervisorName,
          'supervisorResult': item.supervisorResult,
          'supervisorTime': item.supervisorTime,
          'fillingValue': item.fillingValue,
          'amount': item.amount,
          'gapPrice': item.gapPrice,
          'status': item.status,
          'gapPicture': item.gapPicture,
          'fillingPictureValue': item.fillingPictureValue,
        };
      }).toList();
      return {
        'parentName': parentName,
        'child': child,
      };
    }).toList();
    processList.value = result;
    scrollController.jumpTo(offsetNum.value);
    update();
  }

  getProcessStatus(index, itemData) {
    // 执行人
    if (userRole.value == 0) {
      if (itemData['status'] != 0) {
        return "已完成";
      } else {
        return "正在维护";
      }
    }
    // 监督人
    if (userRole.value == 1) {
      if (itemData['status'] != 0) {
        return "已完成";
      } else if (itemData['status'] != 0) {
        return "正在维护";
      } else {
        return "等待维护";
      }
    }
  }

  /**操作流程执行 */
  processExecute(processItem) async {
    // if(globalController.socketType == 0){
    //   toastFun();
    //   return;
    // }
    DateTime date = DateTime.now();
    var processMaintainItem = await upholdSQLController.findMaintainItem(
        "woid='${workId.value}' AND stepName='操作流程' AND content='${processItem['content']}'");
    var intercept1 = true;//判断输入框是否输入
    if(processMaintainItem[0].fillingValue == 1){
      var gapPriceList = processMaintainItem[0]?.gapPrice;
      var inputList = jsonDecode(gapPriceList ?? '') ?? '';
      for(var item in inputList){
        if(item.isEmpty){
          intercept1 = false;
        }
      }
    }
    if(processMaintainItem[0].fillingValue == 1 && !intercept1){
      Fluttertoast.showToast(
        msg: "请检查所有输入项是否填写",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
      return;
    }
    if(processMaintainItem[0].fillingPictureValue == 1 && (processMaintainItem[0].gapPicture?.isEmpty ?? false)){
      Fluttertoast.showToast(
        msg: "请上传图片后再进行操作",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
      return;
    }
    // 执行人操作
    if (userRole.value == 0) {
      // 修改本地库
      upholdSQLController.updateTable(
          "maintainItem",
          {
            "operatorId": userInfo.value[0].uid,
            "operatorName": userInfo.value[0].name,
            "operatorResult": 1,
            "operatorTime": date.millisecondsSinceEpoch
          },
          "woid='${workId.value}' AND stepName='操作流程' AND parentName='${processItem['parentName']}' AND content='${processItem['content']}'");
      // 向监督人发送数据
      var deviceSend = {
        "tabelName": "maintainItem", // 本地数据库表名
        "id": workId.value, // 工单id
        "stepName": "操作流程", // 用于区分项目
        "content": processItem['content'], // 任务内容
        "parentName": processItem['parentName'], // 任务子类
        "userRole": userRole.value,
        "data": {
          "operatorId": userInfo.value[0].uid, // 登录的用户id
          "operatorName": userInfo.value[0].name, // 登录的用户姓名
          "operatorResult": 1, // 执行状态
          "operatorTime": date.millisecondsSinceEpoch, // 执行时间
          "gapPrice": processMaintainItem[0].gapPrice, // 操作流程输入值
        }
      };
      // 向监督人发送数据
      handelSocket(deviceSend);
    }
    // 监督人操作
    if (userRole.value == 1) {
      // 监督人操作，修改本地数据
      upholdSQLController.updateTable(
          "maintainItem",
          {
            "supervisorId": userInfo.value[0].uid,
            "supervisorName": userInfo.value[0].name,
            "operatorResult": 1,
            "supervisorResult": 1,
            "supervisorTime": date.millisecondsSinceEpoch,
            "status": 1,
          },
          "woid='${workId.value}' AND stepName='操作流程' AND parentName='${processItem['parentName']}' AND content='${processItem['content']}'");
      // var deviceSend = {
      //   "tabelName": "maintainItem",
      //   "id": workId.value,
      //   "stepName": "操作流程",
      //   "userRole": userRole.value,
      //   "content": processItem['content'], // 任务内容
      //   "parentName": processItem['parentName'], // 任务子类
      //   "data": {
      //     "supervisorId": userInfo.value[0].uid,
      //     "supervisorName": userInfo.value[0].name,
      //     "supervisorResult": 1,
      //     "supervisorTime": date.millisecondsSinceEpoch,
      //     "gapPrice": processMaintainItem[0].gapPrice,
      //   }
      // };
      // 向执行人发送数据
      // handelSocket(deviceSend);
    }
    getProcessData(workId.value);
  }

  //操作流程输入模块
  processInputValue(childItem, value, index) async {
    // 执行人操作
    /**将该条数据找出后，解析 gapPrice 字符变为 list 类型， 根据 index 修改对应位置的值，最后已json字符串的形式存到库中 */
    var processMaintainItem = await upholdSQLController.findMaintainItem(
        "woid='${childItem['woid']}' AND stepName='操作流程' AND content='${childItem['content']}'");
    final List<Map<String, dynamic>> processMaintainItemJson =
        processMaintainItem.map((item) => item.toJson()).toList();
    var processGapPrice =
        json.decode(processMaintainItem[0].gapPrice as String);
    processGapPrice[index] = value;
    // 写入到数据库
    upholdSQLController.updateTable(
        "maintainItem",
        {
          "gapPrice": json.encode(processGapPrice),
        },
        "woid='${childItem['woid']}' AND stepName='操作流程' AND content='${childItem['content']}'");
  }

  /**socket消息收发*/
  handelSocket(data) {
    var jsonData = json.encode(data);
    var utf8Bytes = utf8.encode(jsonData);
    if (socketServerController.serverSocket != null) {
      // 服务端可以传对象格式给客户端
      socketServerController.send(utf8Bytes);
      return true;
    } else if (socketClientController.clientSocket != null) {
      // 客户端需要传JSON给服务端
      socketClientController.sendData(utf8Bytes);
      return true;
    }
  }

  /**toast弹窗*/
  toastFun() {
    // Fluttertoast.showToast(
    //   msg: "当前连接已断开",
    //   toastLength: Toast.LENGTH_SHORT,
    //   gravity: ToastGravity.CENTER,
    //   timeInSecForIosWeb: 1,
    //   backgroundColor: Colors.black,
    //   textColor: Colors.white,
    //   fontSize: 16.0
    // );
  }
  /**判断用户是执行人还是监督人 */
  handelRole() async {
    var uid = globalController.userInfo.value!.data.eid;
    var roleResult =
        await upholdSQLController.findUser("woid='${workId}' AND uid='${uid}'");
    final List<Map<String, dynamic>> roleJson =
        roleResult.map((form) => form.toJson()).toList();
    userInfo.value = roleResult;
    userRole.value = roleResult[0].role;
    update();
  }

  /**获取执行人名单 */
  getEnforcerList() async {
    var enforceData = await upholdSQLController
        .findUser("woid='${workId.value}' AND role=0"); //数据库查找人员,查找执行人0
    var enforceArr = [];
    enforceData.forEach((item) {
      enforceArr.add(item.name);
    });
    userNames.value = enforceArr.join("、");
    // 查找执行和监督人
    var workerData = await upholdSQLController
        .findUser("woid='${workId.value}' AND role IN(0,1)");
    // print("workerData ${workerData}");
    userNamesList.value = workerData;
    update();
  }

  /**时间格式化 */
  formatTime(timeStamp, {format = "yyyy-MM-dd HH:mm:ss"}) {
    if (timeStamp != null && timeStamp != 0 && timeStamp != "") {
      var timeStampInt = timeStamp is String ? int.parse(timeStamp) : timeStamp;
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timeStampInt);
      String planTime = DateFormat(format).format(dateTime);
      return planTime;
    } else {
      return "正在执行";
    }
  }

  /*签字回显数据函数封装  */
  signatureEcho(stepName) async {
    lignatureViewList.value = {};
    // 查询 stepTake 表获取签名的json
    var signatureData = await upholdSQLController
        .findStepTake("stepName='${stepName}' AND woid='${workId.value}'");
    var signatureAll = {};
    // 将执行人格式统一到map中
    var operateSignatureResult = signatureData[0].operateSignature;
    // print(operateSignatureResult);
    if (operateSignatureResult != "" && operateSignatureResult != null) {
      var operateList = json.decode(operateSignatureResult);
      operateList.forEach((item) {
        var baseContent = jsonDecode(item['baseImg']);
        signatureAll["${item['user']['uid']}"] = {
          "baseContent": baseContent,
          "date": signatureData[0].operateTime
        };
      });
    }
    // 将监督人格式统一到map中
    var supervisorSignatureResult = signatureData[0].supervisorSignature;
    if (supervisorSignatureResult != "" && supervisorSignatureResult != null) {
      var supervisorBaseContent = jsonDecode(supervisorSignatureResult);
      userNamesList.forEach((item) {
        if (item.role == 1) {
          signatureAll["${item.uid}"] = {
            "baseContent": supervisorBaseContent,
            "date": signatureData[0].supervisorTime
          };
        }
      });
    }
    lignatureViewList.value = signatureAll;
    update();
  }

  /*电子签名格式解析*/
  signatureAnalysis(index) {
    // 需要将存储的字符串传承二进制流
    var dataStr = lignatureViewList.value[index]['baseContent'];
    // print(dataStr);
    final Uint8List decodedData = base64Decode(dataStr);
    // print(jsonEncode(decodedData));
    return decodedData;
  }

  /**维护完成 */
  finishFun()async {
    // var processDataResult = await upholdSQLController
    //     .findMaintainItem("woid='${workId.value}' AND stepName='操作流程'");
    // print("123,${jsonEncode(lignatureViewList.value)}");

    var intercept = true;
    for (var classItem in processList){
      for (var item in classItem['child']){
        if(item?['status'] == 0){
          intercept = false;
        }
      }
    }

    for (var item in userNamesList){
      if(lignatureViewList['${item.uid}']==null){
        intercept = false;
      }
    }
    //
    if(!intercept){
      Fluttertoast.showToast(
        msg: "请执行完成并签名后完成维护",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
      return;
    }
    DateTime timeStamp = DateTime.now();
    upholdSQLController.updateTable(
        "workOrder",
        {"isFinish": 1, "finishDate": timeStamp.millisecondsSinceEpoch},
        "id='${workId.value}'");
    globalController.setWorlOrderIndex(timeStamp.millisecondsSinceEpoch);
    Get.back();
  }

  // 下一步切换步骤模块
  handelComponentIndex(int key, String tableName) {
    // if(globalController.socketType == 0){
    //   toastFun();
    //   return;
    // }
    
    //判断先体条件步骤校验
    var intercept = true;
    if(key == 2){
      for (var item in premiseDataList){
        if(item.status == 0){
          intercept = false;
        }
      }
    }
    //判断安全保障步骤校验
    if(key == 3){
      for (var item in guaranteeList){
        if(item.status == 0){
          intercept = false;
        }
      }
    }

    //判断工具及备件要求步骤校验
    if(key == 4){
      for (var item in toolList){
        if(item.status == 0){
          intercept = false;
        }
      }
    }


    //
    if(!intercept){
      Fluttertoast.showToast(
        msg: "请完成所有操作后再进行下一步",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
      return;
    }
    // 更新 workOrder 表中的步骤数
    upholdSQLController.updateTable(
        tableName, {"finishStep": key}, "id='${workId.value}'");
    maintenanceIndex.value = 0; // 用于各菜单的工单进行状态
    componentIndex.value = key; // 用于左侧菜单映射的下标,查找表的 stepTake 签字字段
    lignatureViewList.value = {}; // 清空签字回显对喜爱那个
    scrollController.jumpTo(0); // 重置 listView 的高度
    update();
  }

  // 开始扫描
  Future<void> startScan() async {
    try {
      await scanKit.startScan();
    } on PlatformException {}
  }

  // 获取扫描结果
  scanResult(value) {
    code.value = value;
    update();
  }

  // 电子签名弹窗
  signAlter(userItem, stepName) {
    Get.dialog(
        barrierDismissible: false, // 禁止点击空白处退出
        AlertDialog(
          title: const Text("电子签名"), // 标题
          content: Container(
            width: MyScreenUtil.width(600),
            height: MyScreenUtil.height(600),
            child: MySigature(
              userItem: userItem,
              stepName: stepName,
              onClone: signAlterClone,
            ),
          ), // 签名区
        ));
  }

  // 关闭弹窗
  signAlterClone() {
    Get.back();
  }

  uploadPhoto(item, index, name) async {
    XFile? pickedFile = await picker.pickImage(
        source: ImageSource.camera, maxWidth: 800, maxHeight: 800);
    if (pickedFile != null) {
      final imageBytes = await pickedFile.readAsBytes();
      base64Image = base64Encode(imageBytes);
      if (name == '操作流程') {
        upholdSQLController.updateTable(
            "maintainItem",
            {
              "gapPicture": base64Image,
              "fillingPictureValue": 1,
            },
            "woid='${item['woid']}' AND stepName='${name}' AND content='${item['content']}'");
      } else {
        upholdSQLController.updateTable(
            "maintainItem",
            {
              "gapPicture": base64Image,
              "fillingPictureValue": 1,
            },
            "woid='${item.woid}' AND stepName='${name}' AND content='${item.content}'");
      }

      if (name == "先提条件") {
        getPremiseData(workId.value);
      } else if (name == "安全保障") {
        getGuaranteeData(workId.value);
      } else if (name == "工具及备件要求") {
        getToolData(workId.value);
      } else if (name == "操作流程") {
        getProcessData(workId.value);
      }
      // update();
    }
  }
}
