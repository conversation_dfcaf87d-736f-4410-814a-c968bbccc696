class RoomModule {
  RoomModule({
    required this.code,
    required this.msg,
    required this.data,
  });
  late final int code;
  late final String msg;
  late final List<Data> data;
  
  RoomModule.fromJson(Map<String, dynamic> json){
    code = json['code'];
    msg = json['msg'];
    data = List.from(json['data']).map((e)=>Data.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['code'] = code;
    _data['msg'] = msg;
    _data['data'] = data.map((e)=>e.toJson()).toList();
    return _data;
  }
}

class Data {
  Data({
    required this.type,
    required this.rooms,
  });
  late final String type;
  late final List<Rooms> rooms;
  
  Data.fromJson(Map<String, dynamic> json){
    type = json['type'];

    rooms = List.from(json['rooms'] ?? []).map((e)=>Rooms.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['type'] = type;
    _data['rooms'] = rooms.map((e)=>e.toJson()).toList();
    return _data;
  }
}

class Rooms {
  Rooms({
    required this.roomId,
    required this.name,
    required this.deviceCount,
    required this.note,
    required this.type,
  });
  late final String roomId;
  late final String name;
  late final int deviceCount;
  late final String note;
  late final String type;
  
  Rooms.fromJson(Map<String, dynamic> json){
    roomId = json['roomId'];
    name = json['name'];
    deviceCount = json['deviceCount'];
    note = json['note'];
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['roomId'] = roomId;
    _data['name'] = name;
    _data['deviceCount'] = deviceCount;
    _data['note'] = note;
    _data['type'] = type;
    return _data;
  }
}
