import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:sjzx_patrol_system_mobile/app/api/env_config.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/logger_interceptor.dart';

import 'interceptor/error_code_interceptor.dart';


class AppDio with DioMixin implements Dio {
  AppDio._([BaseOptions? options]) {
    options = BaseOptions(
      baseUrl: '${Host.userApi}',
      contentType: 'application/json',
      connectTimeout: const Duration(seconds: 120),
      sendTimeout: const Duration(seconds: 120),
      receiveTimeout: const Duration(seconds: 120),
    );

    this.options = options;
    // interceptors.add(InterceptorsWrapper(onRequest: (options, handler) async {
    //   options.headers.addAll(await userAgentClientHintsHeader());
    //   handler.next(options);
    // }));
    interceptors.add(ErrorCodeInterceptor());

    if (kDebugMode) {
      interceptors.add(LinqDioLogger(enableLog: true));
    }

    httpClientAdapter = HttpClientAdapter();
  }

  static Dio getInstance() => AppDio._();
}

