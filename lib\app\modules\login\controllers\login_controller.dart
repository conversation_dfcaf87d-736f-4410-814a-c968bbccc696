import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:sjzx_patrol_system_mobile/app/db/db_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/ir_detection/manager/ir_manager.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/string_util.dart';
import '../../../../main.dart';
import '../../../api/env_config.dart';
import '../../../api/room.dart';
import '../../../api/user.dart';
import "../../../api/upholdApi.dart";
import '../../../controllers/global_controller.dart';
import '../../../controllers/upholdSqflite_controller.dart';
import '../../../data/dataVersion.dart';
import '../../../data/uphold/maintainData.dart';
import '../../../data/uphold/stepTakeData.dart';
import '../../../data/uphold/user.dart';
import "../../../model/login/loginModel.dart";
import '../../../retrofit/app_dio.dart';
import '../../../retrofit/entity/login_req_body.dart';
import '../../../retrofit/ir_datasource.dart';
import "../../../utils/storage.dart";
import "../../../controllers/sqflite_controller.dart";
import "../../../data/room.dart";
import "../../../data/device.dart";
import '../../../data/formSQData.dart';
import "../../../data/deviceForm.dart";
import "../../../data/user.dart";
import "../../../data/roomTypeInfo.dart";
import "../../../data/uphold/workOrder.dart";
import "../../../model/room/roomModel.dart";
import "../../../model/device/device.dart";






class LoginController extends GetxController {
  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();
  UpholdSQLController upholdSQLController = Get.find();
  // 接口实例
  UserApi userApi = UserApi();
  // 初始化房间的接口实例
  RoomApi roomApi = RoomApi();
  // 维护工单实例
  UpholdApi upholdApi = UpholdApi();
  

  // 防止因为没有观察变量而报错
  RxString pageName = "欢迎登录数据中心管理平台".obs;
  // 监听再按一次退出APP
  var lastPopTime = DateTime.now().obs;
  // 用户名&密码
  RxString userCode = "".obs;
  RxString userPsWord = "".obs;

  TextEditingController textEditingController = TextEditingController();
  TextEditingController textPasswordController = TextEditingController();

  final count = 0.obs;

  @override
  void onInit() {
    super.onInit();
    if(kDebugMode){
      textEditingController.text = "18032639232";
      textPasswordController.text = "666666";
      userCode.value =  "18032639232";
      userPsWord.value =  "666666";
    }
  }

  @override
  void onReady() async {
    super.onReady();
    var value = await Storage.getConfig(SHAREPREFERENCE_LAST_LOGINED_MOBILE);
    // print('之前有没有记录 手机号----> $value');
    if(value != null && value != ""){
      textEditingController.text = value;
      userCode.value = value;
      update();
    }
  }

  @override
  void onClose() {
    textEditingController?.dispose();
    textPasswordController.dispose();
    super.onClose();
  }
  
  // 用户名数据响应式
  setUserCode(value){
    userCode.value = value;
    update();
  }
  // 密码数据响应式
  setUserPassword(value){
    userPsWord.value = value;
    update();
  }
  // 登录
  login() async {
    if(userCode.value == "" || userPsWord.value == ""){
      Fluttertoast.showToast(
        msg: "请输入账号和密码",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 2,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0
      );
      return;
    }
    loadingFun();
    var userForm = {
      "password":userPsWord.value,
      "user":userCode.value
    };
    var response =await userApi.login(userForm);

    LoginModel res;
    if(response == null){Get.back();}
    if(response.data['code'] == 0){
      Fluttertoast.showToast(
        msg:response.data['msg'],
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 2,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0
      );
      Get.back();
    }else{
      // 将密码存到user表中,用于后面的数据更新
      response.data["data"]["passWorld"] = userPsWord.value;
      res = LoginModel.fromJson(response.data);
      globalController.setUserInfo(res);
      Storage.setData("userInfo", res);
      Storage.setConfig(SHAREPREFERENCE_LAST_LOGINED_MOBILE, res.data.phone);

      var userInfo = await Storage.getData('userInfo');

      var hasTask = await isHaveUnCompleteTask();
      if(!hasTask){
        await insertUserData(res.data);
      }

      await insertRoomTypeInfo(res.data.roomTypeInfoList , userId: res.data.eid);
      // 获取版本号,如果版本号为空则插入当前版本号并更新数据
      var version = await sqfliteController.findDataVersion("id=0");
      var versionInt =  await getVersion();
      if(version.isEmpty){
        List<DataVersion> dataVersionsList = [];
        dataVersionsList.add(
          DataVersion(
            id:0,
            version:'${versionInt}',
          )
        );
        sqfliteController.insertDataVersion(dataVersionsList);
      }

      /// 是否插入新数据
      await updateRoomData();
      await updateDeviceData(res.data.eid);
      await updateDeviceParamsData();
      await relevanceDeviceANDRoom(res.data.eid);

      /**工单维护接口 */
      await getUpholdData(res);

      await fetchIrPermission(res.data.eid);

      Get.back();
      Get.offNamed("/home");
    }
    
    // globalController.setIsLogin(true);
    // Get.offNamed("/home");
  }

  // 确认退出APP
  setLastPopTime(){
    lastPopTime.value = DateTime.now();
    update();
  }

  // 根据user的开始结束时间判断是否有 未完成的巡检任务
  Future isHaveUnCompleteTask() async {
    var r = await sqfliteController.isHaveUnCompleteTask(globalController.queryCurrentUserId());
    return r;
  }


  /**插入用户信息到数据库 */
  insertUserData(userInfo)async{
    List<UserInfo> userList = [];
    userList.add(UserInfo(
      eid:userInfo.eid??"",
      name:userInfo.name,
      phone:userInfo.phone,
      passWorld:userPsWord.value,
      deptName:userInfo.deptName,
      technicalPost:userInfo.deptPost,
      gender:userInfo.gender,
      occupation:userInfo.occupation,
      roomTypeName:userInfo.roomTypeName??"",
      createTime:userInfo.createTime??0,
      floor:userInfo.floor ?? "",
      shifts:userInfo.shifts??"",
      roomType:userInfo.roomType??"", 
      livingPlace:userInfo.livingPlace??"",
      avatar:"",
      isUpload:0,
      patrolStartTimeStamp:0,
      patrolEndTimeStamp:0,
      companyName:userInfo.companyName??"",
      companyId:userInfo.companyId??"",
      deviceMaintainPer:userInfo.deviceMaintainPer,
    ));
    sqfliteController.insertUsers(userList);
  }

  // 登录后： 判断该用户是否已经存在了数据 插入房间类型数据到数据库
  insertRoomTypeInfo(List? roomTypeInfo , {String? userId})async{
    bool exist = await sqfliteController.isExistData('roomTypeInfo', userId ?? '');
    if(exist){
      return;
    }
    // var result  = await sqfliteController.isExist('roomTypeInfo');
    // if(result){
    //   await sqfliteController.clearTable("roomTypeInfo");
    // }
    List<RoomTypeInfo> roomTypeInfoList = [];
    roomTypeInfo?.forEach((item){
      roomTypeInfoList.add(RoomTypeInfo(
        roomType:item.roomType,
        roomTypeName:item.roomTypeName,
        roomCount:item.roomCount,
        deviceCount:item.deviceCount,
        progress:"0",
      ));
    });
    sqfliteController.insertRoomTypeInfo(roomTypeInfoList , userId: userId );
  }


  /**拉取数据 */
  // 获取更新设备的版本号
  getVersion()async{
    var response = await roomApi.getVersionApi(); 
    return response.data["data"];
  }
  // 获取更新房间数据
  updateRoomData()async{
    var result  = await sqfliteController.isExistData('room' , globalController.queryCurrentUserId() ?? '');
    if(result){
      return;
      // await sqfliteController.clearTable("room");
    }
    var response = await roomApi.getRoomApi();
    RoomModule res = RoomModule.fromJson(response.data);
    // 将数据存入room表中
    List<Room> initData = []; 
    res.data?.forEach((item) {
      item.rooms.forEach((roomsItem) {
        initData.add(Room(
            roomId: roomsItem.roomId??'', 
            name: roomsItem.name??'', 
            storey: 1, 
            deviceCount: roomsItem.deviceCount??0, 
            note: roomsItem.note??'', 
            type: roomsItem.type??'',
            isFinish: 0,
            isTaste: null,
            isSound: null,
            goTime:null,
        ));
      });
    });

    var userId = globalController.userInfo.value!.data.eid;
    sqfliteController.insertRooms(initData , userId: userId);
  }
  
  // 获取更新的设备信息
  updateDeviceData(String? userId)async{
    var result  = await sqfliteController.isExistData('device' , globalController.queryCurrentUserId());
    if(result) return;
    var response = await roomApi.getDeviceApi();
    DeviceModule res = DeviceModule.fromJson(response.data);
    // 将数据更新到 device 表中
    List<Device> DeviceInitData = [];
    res.data.forEach((item) {
      item.devices.forEach((devicesItem) {
        DeviceInitData.add(Device(
          deviceId: devicesItem.deviceId, 
          formId: "${devicesItem.deviceId}${devicesItem.roomId}${devicesItem.roomType}", 
          deviceTypeName: devicesItem.deviceTypeName, 
          deviceType: devicesItem.deviceType, 
          deviceCode:devicesItem. deviceCode, 
          roomId: devicesItem.roomId, 
          roomType: devicesItem.roomType, 
          isFinish: 0,
          isOpen: 1,
          userId:userId
        ));
      });
    });
    sqfliteController.insertDevices(DeviceInitData , );
  }

  // 获取更新设备的参数信息
  updateDeviceParamsData() async{
    var result  = await sqfliteController.isExistData('form' , globalController.queryCurrentUserId());
    if(result) return;
    var response = await roomApi.getDeviceParams();
    List<FormSQData> formInitData = [];
    response.data["data"].forEach((key,value){
      value.forEach((item){
        item['inspectionData'].forEach((inspectionItem){
          formInitData.add(FormSQData(
            name:item['name'],
            deviceTypeId:item["deviceTypeId"],
            inputType:"",
            outputType:"",
            inspectionName:inspectionItem["name"],
            inspectionRangeBegin:inspectionItem["rangeBegin"] is String?inspectionItem["rangeBegin"]:inspectionItem["rangeBegin"].toString(),
            inspectionRangeEnd: inspectionItem["rangeEnd"] is String?inspectionItem["rangeEnd"]:inspectionItem["rangeEnd"].toString(),
          ));
        });
      });
    });
    sqfliteController.insertForms(formInitData);
  }

  // 关联设备和巡检项
  relevanceDeviceANDRoom(String? userId)async{
    var result  = await sqfliteController.isExistData('deviceForm' , globalController.queryCurrentUserId());
    if(result) return;
    var deviceData = await sqfliteController.findDevices("");
    final List<Map<String, dynamic>> deviceDataList = deviceData.map((device) => device.toJson()).toList();
    List<DeviceForm> DeviceFormList = [];
    // 遍历每一个设备的数据根据 deviceType 查找响应的巡检项
    for (var item in deviceData){
      var formDataList =  await sqfliteController.findForms("deviceTypeId = '${item.deviceType}'");
      final List<Map<String, dynamic>> formData = formDataList.map((form) => form.toJson()).toList();
      // 得到巡检项后循环巡检项放到DeviceFormList的数组中
      for (var formItem in formDataList) {
        DeviceFormList.add(DeviceForm(
          name:formItem.name,
          formId:"${item.deviceId}${item.roomId}${item.roomType}",
          deviceTypeId:formItem.deviceTypeId,
          inputType:formItem.inputType,
          outputType:formItem.outputType,
          inspectionName:formItem.inspectionName,
          inspectionRangeBegin:formItem.inspectionRangeBegin,
          inspectionRangeEnd:formItem.inspectionRangeEnd,
          inputValue:null,
          inputActive:null,
          userId:userId
        ));
      };
    };
    sqfliteController.insertDeviceForm(DeviceFormList);
  }


  /** 重置数据*/
  // 重置房间数据
  resetRoomData()async{
    // 判断当前表是否为空,不为空时重置表中的 isTaste/ isSound / isFinish 为null / 0; 不在像接口拉去数据
    var roomData =  await sqfliteController.findRoomData('');
    if(roomData.isNotEmpty){
      sqfliteController.updateTable(
        "room",
        {
          "isTaste":null,
          "isSound":null,
          "isFinish":0
        },
        ''
      );
    }
  }
  // 重置设备数据
  resetDeviceData()async{
    // 判断当前 device 表是否为空,不为空时重置表中的 isFinish 为 0; 不在向接口拉去数据
    var devicesData = await sqfliteController.findDevices("");
    if(devicesData.isNotEmpty){
      sqfliteController.updateTable(
        "device",
        {
          "isFinish":0
        },
        ''
      );
    }
  }
  // 重置设备的参数信息
  resetDeviceANDRoom() async{
    // 如果表 deviceForm 存在，清空 inputValue 和 inputActive 字段为null, 不向接口拉去数据
    var deviceRoomData = await sqfliteController.findDeviceForm("");
    if(deviceRoomData.isNotEmpty){
      sqfliteController.updateTable(
        "deviceForm",
        {
          "inputValue":null,
          "inputActive":null
        },
        ''
      );
    }
  }

  /**工单维护接口 */
  // 获取维护工单数据
  getUpholdData(userInfo)async{
    var result  = await upholdSQLController.isExistData('workOrder' , globalController.queryCurrentUserId() ?? '');
    // if(result){
    //   return;
    //   // await sqfliteController.clearTable("room");
    // }
    var response = await upholdApi.getAllData(userInfo.data.eid,0);
    // print('全部工单： ${response}');

    await insertWorkOrder(response.data,userInfo.data.eid);
  }
  // workOrder & user 表中插入工单数据和用户数据
  insertWorkOrder(workOrderData,userId)async{
    var result  = await upholdSQLController.isExist('user');
    var workOrderResult  = await upholdSQLController.isExist('workOrder');
    var stepTakeResult  = await upholdSQLController.isExist('stepTake');
    var maintainItemResult  = await upholdSQLController.isExist('maintainItem');
    // if(result){
    //   await upholdSQLController.clearTable("user");
    // }
    // if(workOrderResult){
    //   await upholdSQLController.clearTable("workOrder");
    // }
    var data = workOrderData["data"];
    // workOrder的数据格式
    List<WorkOrder> workOrderList = [];
    // user 的数据格式
    List<User> userList = [];
    List<StepTakeData> stepTakeList = [];
    List<MaintainData> maintainList = [];
    // data.forEach((item) async {
    print(jsonEncode(data));
    for(var item in data){
      var sqlItem = await upholdSQLController.isExistDataItem("workOrder",item["id"],userId);
      
      if (!sqlItem){
        print(666);
        print("1111111111111111111111,${item["id"]},${userId},${sqlItem}");
        var workOrderItem = WorkOrder(
          id: item["id"], 
          maintenancePlanNum:item['maintenancePlanNum'],
          deptId: item["deptId"], 
          deptName: item["deptName"], 
          deviceGroupId: item["deviceGroupId"], 
          deviceGroupName: item["devicePlanName"], 
          type: item["type"], 
          typeName: item["typeName"], 
          installationSite: item["installationSite"], 
          model: item["model"], 
          manufacturer: item["manufacturer"], 
          deviceNum: item["deviceNum"], 
          phone: item["phone"], 
          contactPhone: item["contactPhone"], 
          factory: item["factory"], 
          deviceFlowId: item["deviceFlowId"], 
          planTime: item["planTime"], 
          week:item["week"], 
          parentGroupId:item["parentGroupId"],
          parentGroupName:item["parentGroupName"],
          isFinish: 0, 
          isUpload: 0,
          isPreview:0,
          finishStep:0,
          finishDate:0
        );
        workOrderList.add(workOrderItem);
        
        // user 执行人格式处理
        item["operator"].forEach((operatorItem){
          userList.add(User(
            woid:item["id"],
            uid:operatorItem["uid"],
            name:operatorItem["name"],
            gender:operatorItem["gender"],
            phone:operatorItem["phone"],
            status:operatorItem["status"],
            role:0,
          ));
        });
        // user 监督人格式处理
        userList.add(User(
          woid:item["id"],
          uid:item["supervision"]["uid"],
          name:item["supervision"]["name"],
          gender:item["supervision"]["gender"],
          phone:item["supervision"]["phone"],
          status:item["supervision"]["status"],
          role:1,
        ));
        // user 审核人格式处理
        userList.add(User(
          woid:item["id"],
          uid:item["pe"]["uid"],
          name:item["pe"]["name"],
          gender:item["pe"]["gender"],
          phone:item["pe"]["phone"],
          status:item["pe"]["status"],
          role:2,
        ));



        stepTakeList.add(StepTakeData(
          woid:item["id"],
          stepName:"设备信息",
          stepField:"deviceInfo",
          id:item["deviceInfo"]["id"],
          operateTime:item["deviceInfo"]["operateTime"],
          operateSignature:"",
          supervisorTime:item["deviceInfo"]["supervisorTime"],
          supervisorSignature:null,
        ));
        stepTakeList.add(StepTakeData(
          woid:item["id"],
          stepName:"先提条件",
          stepField:"condition",
          id:item["condition"]["id"],
          operateTime:item["condition"]["operateTime"],
          operateSignature:"",
          supervisorTime:item["condition"]["supervisorTime"],
          supervisorSignature:null,
        ));
        stepTakeList.add(StepTakeData(
          woid:item["id"],
          stepName:"安全保障",
          stepField:"guarantee",
          id:item["guarantee"]["id"],
          operateTime:item["guarantee"]["operateTime"],
          operateSignature:"",
          supervisorTime:item["guarantee"]["supervisorTime"],
          supervisorSignature:null,
        ));
        stepTakeList.add(StepTakeData(
          woid:item["id"],
          stepName:"工具及备件要求",
          stepField:"guarantee",
          id:item["instrument"]["id"],
          operateTime:item["instrument"]["operateTime"],
          operateSignature:"",
          supervisorTime:item["instrument"]["supervisorTime"],
          supervisorSignature:null,
        ));
        stepTakeList.add(StepTakeData(
          woid:item["id"],
          stepName:"回退计划",
          stepField:"rollbackPlan",
          id:item["rollbackPlan"]["id"],
          operateTime:item["rollbackPlan"]["operateTime"],
          operateSignature:"",
          supervisorTime:item["rollbackPlan"]["supervisorTime"],
          supervisorSignature:null,
        ));
        stepTakeList.add(StepTakeData(
          woid:item["id"],
          stepName:"操作流程",
          stepField:"flow",
          id:item["flow"]["id"],
          operateTime:item["flow"]["operateTime"],
          operateSignature:"",
          supervisorTime:item["flow"]["supervisorTime"],
          supervisorSignature:null,
        ));

        // 整理维护项目数据
        item["deviceInfo"]["details"]?.forEach((deviceInfoItem){
          maintainList.add(MaintainData(
            woid:item["id"],
            stepName:"设备信息",
            id:item["deviceInfo"]["id"],
            content:deviceInfoItem["content"],
            operation:deviceInfoItem["operation"],
            operatorId:deviceInfoItem["operatorId"],
            operatorName:deviceInfoItem["operatorName"],
            operatorResult:deviceInfoItem["operatorResult"],
            operatorTime:deviceInfoItem["operatorTime"] is String?0:deviceInfoItem["operatorTime"],
            supervisorId:deviceInfoItem["supervisorId"],
            supervisorName:deviceInfoItem["supervisorName"],
            supervisorResult:deviceInfoItem["supervisorResult"],
            supervisorTime:deviceInfoItem["supervisorTime"]is String?0:deviceInfoItem["supervisorTime"],
            status:0
          ));
        });
        item["condition"]["details"].forEach((conditionItem){
          var init_gapPrice = List.generate(conditionItem["amount"]??0, (index) => "");
          maintainList.add(MaintainData(
            woid:item["id"],
            stepName:"先提条件",
            id:item["condition"]["id"],
            content:conditionItem["content"],
            operation:conditionItem["operation"],
            operatorId:conditionItem["operatorId"],
            operatorName:conditionItem["operatorName"],
            operatorResult:conditionItem["operatorResult"],
            operatorTime:conditionItem["operatorTime"] is String?0:conditionItem["operatorTime"],
            supervisorId:conditionItem["supervisorId"],
            supervisorName:conditionItem["supervisorName"],
            supervisorResult:conditionItem["supervisorResult"],
            supervisorTime:conditionItem["supervisorTime"] is String?0:conditionItem["supervisorTime"],
            fillingValue:conditionItem["fillingValue"] ?? 0,
            amount:conditionItem["amount"],
            gapPrice:json.encode(init_gapPrice),
            status:0,
            gapPicture:conditionItem["gapPicture"]!= null && conditionItem["gapPicture"].isNotEmpty ? conditionItem["gapPicture"][0] : '',
            fillingPictureValue:conditionItem["fillingPictureValue"]??0,
          ));
        });
        item["guarantee"]["details"].forEach((guaranteeItem){
          var init_gapPrice = List.generate(guaranteeItem["amount"]??0, (index) => "");
          maintainList.add(MaintainData(
            woid:item["id"],
            stepName:"安全保障",
            id:item["guarantee"]["id"],
            content:guaranteeItem["content"],
            operation:guaranteeItem["operation"],
            operatorId:guaranteeItem["operatorId"],
            operatorName:guaranteeItem["operatorName"],
            operatorResult:guaranteeItem["operatorResult"],
            operatorTime:guaranteeItem["operatorTime"]is String?0:guaranteeItem["operatorTime"],
            supervisorId:guaranteeItem["supervisorId"],
            supervisorName:guaranteeItem["supervisorName"],
            supervisorResult:guaranteeItem["supervisorResult"],
            supervisorTime:guaranteeItem["supervisorTime"] is String?0:guaranteeItem["supervisorTime"],
            fillingValue:guaranteeItem["fillingValue"]??0,
            amount:guaranteeItem["amount"],
            gapPrice:json.encode(init_gapPrice),
            status:0,
            gapPicture:guaranteeItem["gapPicture"]!= null && guaranteeItem["gapPicture"].isNotEmpty ? guaranteeItem["gapPicture"][0] : '',
            fillingPictureValue:guaranteeItem["fillingPictureValue"]??0,
          ));
        });
        item["instrument"]["details"].forEach((instrumentItem){
          var init_gapPrice = List.generate(instrumentItem["amount"]??0, (index) => "");
          maintainList.add(MaintainData(
            woid:item["id"],
            stepName:"工具及备件要求",
            id:item["instrument"]["id"],
            content:instrumentItem["content"],
            operation:instrumentItem["operation"],
            operatorId:instrumentItem["operatorId"],
            operatorName:instrumentItem["operatorName"],
            operatorResult:instrumentItem["operatorResult"],
            operatorTime:instrumentItem["operatorTime"] is String?0:instrumentItem["operatorTime"],
            supervisorId:instrumentItem["supervisorId"],
            supervisorName:instrumentItem["supervisorName"],
            supervisorResult:instrumentItem["supervisorResult"],
            supervisorTime:instrumentItem["supervisorTime"] is String?0:instrumentItem["supervisorTime"],
            fillingValue:instrumentItem["fillingValue"]??0,
            amount:instrumentItem["amount"],
            gapPrice:json.encode(init_gapPrice),
            status:0,
            gapPicture:instrumentItem["gapPicture"]!= null && instrumentItem["gapPicture"].isNotEmpty ? instrumentItem["gapPicture"][0] : '',
            fillingPictureValue:instrumentItem["fillingPictureValue"]??0,
          ));
        });
        item["rollbackPlan"]["details"].forEach((rollbackPlanItem){
          // 初始化 gapPrice为map
          var init_gapPrice = List.generate(rollbackPlanItem["amount"]??0, (index) => "");
          maintainList.add(MaintainData(
            woid:item["id"],
            stepName:"回退计划",
            id:item["rollbackPlan"]["id"],
            content:rollbackPlanItem["content"],
            operation:rollbackPlanItem["operation"],
            operatorId:rollbackPlanItem["operatorId"],
            operatorName:rollbackPlanItem["operatorName"],
            operatorResult:rollbackPlanItem["operatorResult"],
            operatorTime:rollbackPlanItem["operatorTime"] is String?0:rollbackPlanItem["operatorTime"],
            supervisorId:rollbackPlanItem["supervisorId"],
            supervisorName:rollbackPlanItem["supervisorName"],
            supervisorResult:rollbackPlanItem["supervisorResult"],
            supervisorTime:rollbackPlanItem["supervisorTime"] is String?0:rollbackPlanItem["supervisorTime"],
            fillingValue:rollbackPlanItem["fillingValue"]??0,
            amount:rollbackPlanItem["amount"],
            gapPrice:json.encode(init_gapPrice),
            status:0,
            gapPicture:rollbackPlanItem["gapPicture"]!= null && rollbackPlanItem["gapPicture"].isNotEmpty ? rollbackPlanItem["gapPicture"][0] : '',
            fillingPictureValue:rollbackPlanItem["fillingPictureValue"]??0,
          ));
        });
        // 操作流程
        item["flow"]["items"].forEach((flowItem){
          flowItem["details"].forEach((flowDetailItem){
            // 初始化 gapPrice为map
            var init_gapPrice = List.generate(flowDetailItem["amount"]??0, (index) => "");
            maintainList.add(MaintainData(
              woid:item["id"],
              stepName:"操作流程",
              id:item["flow"]["id"],
              parentName:flowItem["name"],
              parentId:flowItem["id"],
              content:flowDetailItem["content"],
              operation:flowDetailItem["operation"],
              operatorId:flowDetailItem["operatorId"],
              operatorName:flowDetailItem["operatorName"],
              operatorResult:flowDetailItem["operatorResult"],
              operatorTime:flowDetailItem["operatorTime"] is String?0:flowDetailItem["operatorTime"],
              supervisorId:flowDetailItem["supervisorId"],
              supervisorName:flowDetailItem["supervisorName"],
              supervisorResult:flowDetailItem["supervisorResult"],
              supervisorTime:flowDetailItem["supervisorTime"] is String?0:flowDetailItem["supervisorTime"],
              fillingValue:flowDetailItem["fillingValue"]??0,
              amount:flowDetailItem["amount"],
              gapPrice:json.encode(init_gapPrice),
              status:0,
              gapPicture:flowDetailItem["gapPicture"]!= null && flowDetailItem["gapPicture"].isNotEmpty ? flowDetailItem["gapPicture"][0] : '',
              fillingPictureValue:flowDetailItem["fillingPictureValue"]??0,
            ));
          });
        });
      }
        
    };
    print(jsonEncode(workOrderList));
    upholdSQLController.insertWorkOrder(workOrderList,userId);
    // 插入到 user
    upholdSQLController.insertUser(userList);

    upholdSQLController.insertStepTake(stepTakeList);
    upholdSQLController.insertMaintainItem(maintainList);
  }

  /**体验优化 */
  loadingFun(){
    Get.dialog(
      barrierDismissible:false,
      const SpinKitFadingCircle(
        color: Colors.white,
        size: 50.0,
      ),
    );
  }

  Future fetchIrPermission(String? userId) async {
    if(StringUtil.isEmpty(userId)) return;
    var userDataSource = IrDataSource(retrofitDio , baseUrl: Host.userApi);
    var response = await userDataSource.irPermission(globalController.queryCurrentUserId());
    if(response.success()){
      if(response.data){
        // 有ir权限
        var has = await DBHelper.existIrData(userId!);
        if(!has){
          await IrManager.resetIrData(userId);
        }
      }
    }
  }
  
} 
