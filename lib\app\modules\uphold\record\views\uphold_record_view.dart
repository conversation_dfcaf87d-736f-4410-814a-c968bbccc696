import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../utils/screenutil.dart';
import '../controllers/uphold_record_controller.dart';

class UpholdRecordView extends GetView<UpholdRecordController> {
  const UpholdRecordView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(()=>Scaffold(
      appBar:  PreferredSize(
        preferredSize: Size.fromHeight(MyScreenUtil.height(60)),
        child: AppBar(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text('${controller.titleName}')
            ],
          ),
          centerTitle: true,
        ),
      ),
      
      body:Container(
        child: controller.historyList.isNotEmpty?ListView(
          children: controller.historyList.map((historyItem){
            return worlList(historyItem);
          }).toList().cast<Widget>(),
        ):Container(
          padding: EdgeInsets.all(MyScreenUtil.width(20)),
          child: Text("暂无维护记录"),
        ),
      )

    ));
  }

  // 工单列表
  worlList(historyItem){
    return Container(
      padding: const EdgeInsets.all(10),
      child: Column(
        children: [
          // 内容
          Container(
            padding:EdgeInsets.only(top:MyScreenUtil.height(20),bottom: MyScreenUtil.height(20)),
            decoration: BoxDecoration(
              border: Border.all(
                color:const Color.fromRGBO(238, 238, 238, 1)
              ),
              borderRadius: BorderRadius.circular(MyScreenUtil.radius(10))
            ),
            child:Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("维护工单编号: ${historyItem.maintenancePlanNum}"),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("设备位置: ${historyItem.installationSite}"),
                      ),
                      Container(
                        child:Text("执行人: ${historyItem.operator}"),
                      )
                    ],
                  ),
                ),
                Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("设备名称: ${historyItem.deviceGroupName}"),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("设备编号: ${historyItem.deviceNum}"),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("监督员: ${historyItem.supervision}"),
                      ),
                    ],
                  ),
                ),
                Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("维护类型: ${historyItem.typeName}"),
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:historyItem.week!= null ? Text("维护时间: ${historyItem.typeName}第${historyItem.week}周"):Text("维护时间: ${historyItem.typeName}")
                      ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child:Text("专业工程师: ${historyItem.pe}"),
                      )
                    ],
                  ),
                )
              ],
            ) ,
          )
        ],
      ),
    );
  }


}
