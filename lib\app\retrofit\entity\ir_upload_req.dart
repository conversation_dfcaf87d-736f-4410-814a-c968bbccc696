

class IrUploadReq {

  IrUploadReq();

  String? userId;
  String? occupation;
  String? infraredInspectionTaskId;
  int startTime = 0;
  int endTime = 0;
  String devicesCount = "0";  // int?

  List<IrUploadDeviceReq> deviceList = [];

  IrUploadReq.fromJson(Map<String, dynamic> json){
    userId = json['userId'];
    occupation = json['occupation'];
    infraredInspectionTaskId = json['occupation'];
    startTime = json['startTime'];
    endTime = json['endTime'];
    devicesCount = json['devicesCount'];
    deviceList = json['deviceList'] == null ? [] : (json['deviceList'] as List).map((e) => IrUploadDeviceReq.fromJson(
      e as Map<String,dynamic>
    )).toList();
  }

  Map<String, dynamic> toJson() => {
    'userId':userId,
    'occupation':occupation,
    'infraredInspectionTaskId':infraredInspectionTaskId,
    'startTime':startTime,
    'endTime':endTime,
    'devicesCount':devicesCount,
    'deviceList': deviceList.map((e) => e.toJson()).toList()
  };

  @override
  String toString() {
    return '[ userId: $userId , occupation: ${occupation} , infraredInspectionTaskId: ${infraredInspectionTaskId} , startTime: ${startTime} ,endTime: ${endTime} ,devicesCount : ${devicesCount},'
        ' deviceList: ${deviceList.toString()}';
  }
}

class IrUploadDeviceReq{
   String? code;
   int detectionTime = 0;
   String? deviceId;
   String? infraredDeviceTypeId;
   String? infraredDeviceTypeName;
   String? installationSite;
   String? mark;
   String? maxTemperature;
   String? name;
   String? num;
   String? picture;
   String? project;

   IrUploadDeviceReq();

   IrUploadDeviceReq.fromJson(Map<String, dynamic> json){
     code = json['code'];
     detectionTime = json['detectionTime'];
     deviceId = json['deviceId'];
     maxTemperature = json['maxTemperature'];
     infraredDeviceTypeId = json['infraredDeviceTypeId'];
     infraredDeviceTypeName = json['infraredDeviceTypeName'];
     installationSite = json['installationSite'];
     mark = json['mark'];
     name = json['name'];
     num = json['num'];
     picture = json['picture'];
     project = json['project'];
   }

   Map<String, dynamic> toJson() => {
     'code':code,
     'detectionTime':detectionTime,
     'deviceId':deviceId,
     'maxTemperature':maxTemperature,
     'infraredDeviceTypeId':infraredDeviceTypeId,
     'infraredDeviceTypeName':infraredDeviceTypeName,
     'installationSite': installationSite,
     'mark': mark,
     'name': name,
     'num': num,
     'picture': picture,
     'project': project,
   };

   @override
  String toString() {
    return "[code: $code , detectionTime: $detectionTime ,, maxTemperature: $maxTemperature,  deviceId: $deviceId , infraredDeviceTypeId: $infraredDeviceTypeId,"
        "infraredDeviceTypeName: $infraredDeviceTypeName , installationSite: $installationSite , mark: $mark ,name: $name "
        "num: $num , picture: $picture , project: $project  ]";
  }
}

