

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/string_extension.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/toast.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/login/controllers/login_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/routes/app_pages.dart';

import '../../data/dataVersion.dart';
import '../../model/login/loginModel.dart';
import '../../utils/storage.dart';


class PhoneLoginController extends LoginController {

  @override
  login() async {
    if(userCode.value == "" || userPsWord.value == ""){
      toast("请输入账号和密码");
      return;
    }
    loadingFun();
    var userForm = {
      "password":userPsWord.value,
      "user":userCode.value
    };
    var response =await userApi.login(userForm);

    LoginModel res;
    if(response == null){Get.back();}
    if(response.data['code'] == 0){
      toast(response.data['msg']);

      Get.back();
    }else{
      // 将密码存到user表中,用于后面的数据更新
      response.data["data"]["passWorld"] = userPsWord.value;
      res = LoginModel.fromJson(response.data);


      globalController.setUserInfo(res);
      Storage.setData("userInfo", res);
      Storage.setConfig(SHAREPREFERENCE_LAST_LOGINED_MOBILE, res.data.phone);

      var userInfo = await Storage.getData('userInfo');

      var hasTask = await isHaveUnCompleteTask();
      if(!hasTask){
        await insertUserData(res.data);
      }

      await insertRoomTypeInfo(res.data.roomTypeInfoList , userId: res.data.eid);
      // 获取版本号,如果版本号为空则插入当前版本号并更新数据
      var version = await sqfliteController.findDataVersion("id=0");
      var versionInt =  await getVersion();
      if(version.isEmpty){
        List<DataVersion> dataVersionsList = [];
        dataVersionsList.add(
            DataVersion(
              id:0,
              version:'${versionInt}',
            )
        );
        sqfliteController.insertDataVersion(dataVersionsList);
      }

      await fetchIrPermission(res.data.eid);

      Get.back();
      Get.offNamed(Routes.PHONE_HOME);
    }
  }

  bool hasFiledMobileAndPwd() {
    if(userCode.value.isEmptyOrNull() || userPsWord.value.isEmptyOrNull()){
      return false;
    }
    return true;
  }

}