import 'package:json_annotation/json_annotation.dart';

import '../../model/login/loginModel.dart';

part 'login_response.g.dart';

@JsonSerializable()
class LoginRespData {
  LoginRespData({
    this.eid,
    required this.name,
    required this.phone,
    required this.passWorld,
    this.deptName,
    this.technicalPost,
    this.deptPost,
    required this.gender,
    this.occupation,
    this.roomTypeName,
    this.createTime,
    required this.floor,
    required this.shifts,
    required this.roomType,
    required this.roomTypeInfoList,
    required this.number,
    required this.versionName,
    required this.apkName,
    required this.versionCreateTime,
    required this.description,
    required this.url,
    this.livingPlace,
    required this.companyName,
    required this.companyId,
    required this.deviceMaintainPer,
  });
  late final String? eid;
  late final String name;
  late final String phone;
  late final String? passWorld;
  late final String? deptName;
  late final String? technicalPost;
  late final String? deptPost;
  late final int? gender;
  late final String? occupation;
  late final String? roomTypeName;
  late final int? createTime;
  late final String? floor;
  late final String? shifts;
  late final String? roomType;
  late final String? livingPlace;
  late final String? number;
  late final String? versionName;
  late final String? apkName;
  late final int? versionCreateTime;
  late final String? description;
  late final String? url;
  late final List<RoomTypeInfoList>? roomTypeInfoList;
  late final String? companyName;
  late final String? companyId;
  late final String? deviceMaintainPer;


  factory LoginRespData.fromJson(Map<String, dynamic> json) => _$LoginRespDataFromJson(json);
  Map<String, dynamic> toJson() => _$LoginRespDataToJson(this);

}