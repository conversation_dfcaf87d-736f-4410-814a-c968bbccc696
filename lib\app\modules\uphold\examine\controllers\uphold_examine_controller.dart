import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:sjzx_patrol_system_mobile/app/controllers/global_controller.dart';

import '../../../../api/upholdApi.dart';

class UpholdExamineController extends GetxController {
  GlobalController globalController = Get.find();
  //TODO: Implement UpholdExamineController
  // 维护工单实例
  UpholdApi upholdApi = UpholdApi();
  
  // 响应式数据
  RxString titleName = "工单审核".obs;
  RxList examineWorkList = [].obs; // 待审核列表集合




  final count = 0.obs;
  @override
  void onInit() {
    super.onInit();
    getExamineWork();

    ever(globalController.examineIndex, (newValue) => getExamineWork());
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  /**业务模块 */
  // 获取审核列表
  getExamineWork()async{
    var examineWorkResult = await upholdApi.getAllData(globalController.userInfo.value!.data.eid,2);
    var response = examineWorkResult.data["data"]??[];
    var userPhone = globalController.userInfo.value!.data.phone;
    var filterateExamineWork = [];

    // 过滤当前审核的工单需要与当前登录用户id匹配
    /**因无巡检权限时eid为空 这里只能根据手机号作为过滤条件 */
    response.forEach((item){
      if(item['pe']['phone'] == userPhone){
        filterateExamineWork.add(item);
      }
    });
    examineWorkList.value  = filterateExamineWork.reversed.toList();
    update();
  }
  // 解析执行人
  parseOperator(operatorData){
    var operatorList = [].obs;
    operatorData.forEach((operatorItem){
      operatorList.add(operatorItem['name']);
    });
    return operatorList.join('、');
  } 
  /**时间格式化 */
  formatTime(timeStamp,{format="yyyy-MM-dd HH:mm:ss"}){
    if(timeStamp != null && timeStamp != ""){
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(int.parse(timeStamp));
      String planTime = DateFormat(format).format(dateTime);
      return planTime;
    }else{
      return "正在执行";
    }
    
  } 

  void increment() => count.value++;
}
