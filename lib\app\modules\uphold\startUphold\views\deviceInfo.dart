import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';

import '../../../../controllers/global_controller.dart';
import '../controllers/uphold_start_uphold_controller.dart';

class DeviceInfo extends GetView<UpholdStartUpholdController> {
  DeviceInfo({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
      return Container(
        child:Column(
          children: [
            Obx(() => Container(
              child: Column(
                children:controller.deviceInfoList.value.map((item){
                  return deviceInfo_biserial(item.content??'',item.operation??'');
                }).toList().cast<Widget>(),
              ),
            )),
            Container(
              child: Container(
                child: Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: ElevatedButton(
                        onPressed: (){
                          controller.deviceInfo_finish();
                        },
                        child:const Text("确认设备"),
                        style: ButtonStyle(
                          backgroundColor:MaterialStateProperty.resolveWith((states){
                            return MyScreenUtil.ThemColor();
                          })
                        ),
                      )
                    )
                  ],
                )
              )
            )
          ],
        )
      );
  }

  //设备信息列表渲染模板
  deviceInfo_biserial(String title,String doc,{bool isButton = false}){
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide (
            color: const Color.fromRGBO(238, 238, 238, 1),
            width: 1
          )
        )
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding:const EdgeInsets.only(right: 10),
            child: Text(
              title,
              style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),
            ),
          ),
          Expanded(
            flex: 1,
            child: Row(
              children:[
                Expanded(
                  flex: 1,
                  child: Container(
                    margin: const EdgeInsets.only(right:20),
                    alignment: Alignment.topRight,
                    child: isButton?Text('$doc-${controller.code.value}',style: TextStyle(fontSize: MyScreenUtil.fontSize(18))):Text(doc,style: TextStyle(fontSize: MyScreenUtil.fontSize(18))),
                  ),
                ),
                isButton?ElevatedButton(
                  onPressed: ()async{
                    // 判断是否有相继和外存储的权限,如果没有则申请权限
                    var pstatus = await controller.permissionGroups.request();
                    var hasGrant = true;
                    pstatus.forEach((key, value){
                      if(value.isDenied){
                        hasGrant = false;
                      }
                    });
                    if(hasGrant){
                      controller.startScan();
                    }
                  }, 
                  child:Text("扫描设备二维码",style: TextStyle(fontSize: MyScreenUtil.fontSize(18))),
                  style: ButtonStyle(
                    backgroundColor:MaterialStateProperty.resolveWith((states){
                      return MyScreenUtil.ThemColor();
                    })
                  )
                ):Container()
              ],
            )
          )
        ],
      ),
    );
  }


}