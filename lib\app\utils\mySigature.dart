import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import "package:get/get.dart";
import "dart:convert";

import 'package:signature/signature.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';
import 'dart:typed_data';
import '../controllers/global_controller.dart';
import '../controllers/upholdSqflite_controller.dart';
import "../controllers/socket_client_controller.dart";
import "../controllers/socket_server_controller.dart";

class MySigature extends StatefulWidget {
  final userItem; // 用户信息
  final stepName; //步骤名称
  bool isDB; // 是否存入数据库（主要用于区分审核人签字模块）
  void Function() onClone;
  MySigature({
    super.key,
    required this.onClone,
    this.userItem,
    this.stepName,
    this.isDB=true
  });

  @override
  State<MySigature> createState() => _MySigatureState();
}

class _MySigatureState extends State<MySigature> {
  // GlobalController globalController = Get.find();
  final globalController = Get.put(GlobalController());
  final upholdSQLController = Get.put(UpholdSQLController());
  final socketServerController = Get.put(SocketServerController());
  final socketClientController = Get.put(SocketClientController());

  // 实例化签名函数
  final SignatureController _controller = SignatureController(
    penStrokeWidth: 2,
    penColor: Colors.black,
    exportBackgroundColor: Colors.white,
    exportPenColor: Colors.black,
    onDrawStart: () => log('onDrawStart called!'),
    onDrawEnd: () => log('onDrawEnd called!'),
  );

  @override
  void initState() {
    super.initState();
    _controller.addListener(() => log('Value changed'));
  }
  @override
  void dispose() {
    // IMPORTANT to dispose of the controller
    _controller.dispose();
    super.dispose();
  }
  
  Future<void> exportImage(BuildContext context) async {
    if (_controller.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          key: Key('snackbarPNG'),
          content: Text('请签字后保存'),
        ),
      );
      return;
    }
    final data = await _controller.toPngBytes(height: 500, width: 500);
    final base64Data = base64Encode(data!.toList());
    // 获取保存的时间戳
    DateTime date = DateTime.now();
    if(widget.isDB){
      // 如果 userItem stepName 不为空则是维护板块，将图片保存到库中
      List? signList = [];
      if(widget.userItem != null && widget.stepName != null){
        if(widget.userItem.role == 0){
          var stepTakeData = await upholdSQLController.findStepTake("woid='${widget.userItem.woid}' AND stepName='${widget.stepName}'");
          if(stepTakeData[0].operateSignature != ""){
            signList = json.decode(stepTakeData[0].operateSignature as String);
          }
          //保存的画布大小和导出的格式保存到签字数组中
          var base64Json = json.encode(base64Data);
          var obj = {"user":widget.userItem,"baseImg":base64Json};
          signList?.add(obj);
          upholdSQLController.updateTable(
            "stepTake", 
            {
              "operateSignature":json.encode(signList),
              "operateTime":date.millisecondsSinceEpoch
            }, 
            "woid='${widget.userItem.woid}' AND stepName='${widget.stepName}'"
          );
        }
        // 监督人签字
        if(widget.userItem.role == 1){
          upholdSQLController.updateTable(
            "stepTake", 
            {
              "supervisorSignature":json.encode(base64Data),
              "supervisorTime":date.millisecondsSinceEpoch
            }, 
            "woid='${widget.userItem.woid}' AND stepName='${widget.stepName}'"
          );
        }
        widget.onClone();
      }
      DateTime d = DateTime.now();
      globalController.setSignIndex(d.millisecondsSinceEpoch);
    }else{
      // 如果为 false 则执行审核人逻辑
      globalController.peSign['baseImage'] = base64Data;
      DateTime d = DateTime.now();
      globalController.peSign['date'] = d.millisecondsSinceEpoch;
      widget.onClone();
    }
    
    // final Uint8List decodedData = base64Decode(dataStr);
    // print("逆解析 ${decodedData}");
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Signature(
          key: const Key('signature'),
          controller: _controller,
          height: MyScreenUtil.width(250),
          width: MyScreenUtil.width(500),
          backgroundColor: Colors.grey[300]!,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            IconButton(
              icon: const Icon(Icons.undo),
              color: MyScreenUtil.ThemColor(),
              onPressed: () {
                setState(() => _controller.undo());
              },
              tooltip: 'Undo',
            ),
            IconButton(
              icon: const Icon(Icons.redo),
              color: MyScreenUtil.ThemColor(),
              onPressed: () {
                setState(() => _controller.redo());
              },
              tooltip: 'Redo',
            ),
            //CLEAR CANVAS
            ElevatedButton(
              key: const Key('clear'),
              onPressed: () {
                setState(() => _controller.clear());
              },
              child: Text(
                '清空',
                style: TextStyle(
                  fontSize: MyScreenUtil.fontSize(18)
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() => _controller.clear());
                Get.back();
              },
              child: Text(
                '退出',
                style: TextStyle(
                  fontSize: MyScreenUtil.fontSize(18)
                ),
              ),
            ),
            ElevatedButton(
                key: const Key('exportPNG'),
                onPressed: (){
                  exportImage(context);
                } ,
                child:Text(
                  '保存图片',
                  style: TextStyle(
                    fontSize: MyScreenUtil.fontSize(18)
                  ),
                ),
            ),
          ],
        ),
        
      ],
    );
  }
}