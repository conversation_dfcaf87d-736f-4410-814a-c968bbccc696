import '../utils/HttpsClient.dart';
import 'env_config.dart';

class UpholdApi {
  // static const String baseUrl = 'http://*********:8593';
  // static const String baseUrl = 'http://test.rangeidc.ddbes.com:8080'; //测试
  // static const String baseUrl = "http://*************:80/center"; // 内网
  late HttpsClient httpsClient;
  UpholdApi(){
    httpsClient = HttpsClient();
  }
  // 获取全部工单数据 status 0待维护 1已上传 2已审核
  getAllData(userId,status) async {
    var response = await httpsClient.get("${Host.userApi}/maintain/pad/v2/${userId}/${status}");
    return response;
  }
  // 检查更新版本
  getVersionApi() async {
    var response = await httpsClient.get("${Host.userApi}/device/version/v1");
    return response;
  }
  // 工单上传
  workUploadData(data) async {
    var response = await httpsClient.post("${Host.userApi}/maintain/pad/v1",data);
    return response;
  }
  // 工单审核
  uploadExamine(data) async {
    var response = await httpsClient.post("${Host.userApi}/maintain/pad/approve/v1",data);
    return response;
  }

}