// Generated file. Do not edit.
// This file is generated by the iFlutter

// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars
class AssetsRes {
  AssetsRes._();

  static const String PROJECT_NAME = 'sjzx_patrol_system_mobile';
  static const String PROJECT_VERSION = '1.0.1';
  static const String BACKIMAGE = 'assets/images/backImage.jpg';
  static const String DROP = 'assets/images/drop.png';
  static const String POWER = 'assets/images/icon/Power.png';
  static const String BACK = 'assets/images/icon/back.png';
  static const String BUMEN = 'assets/images/icon/bumen.png';
  static const String DELETE = 'assets/images/icon/delete.png';
  static const String DEPARTMENT_BLUE = 'assets/images/icon/department_blue.png';
  static const String DIAN = 'assets/images/icon/dian.png';
  static const String LEFT = 'assets/images/icon/left.png';
  static const String NEXT = 'assets/images/icon/next.png';
  static const String POSITION_BLUE = 'assets/images/icon/position_blue.png';
  static const String ROOM_TITLE = 'assets/images/icon/room_title.png';
  static const String SET = 'assets/images/icon/set.png';
  static const String SPECIALITY_BLUE = 'assets/images/icon/speciality_blue.png';
  static const String TIME = 'assets/images/icon/time.png';
  static const String ZHIWEI = 'assets/images/icon/zhiwei.png';
  static const String ZHUANYE = 'assets/images/icon/zhuanye.png';
  static const String IR_LAST_DEVICE = 'assets/images/ir_last_device.png';
  static const String IR_NEXT_DEVICE = 'assets/images/ir_next_device.png';
  static const String IR_START = 'assets/images/ir_start.png';
  static const String LOGO = 'assets/images/logo.png';
  static const String OVER = 'assets/images/over.png';
  static const String PATROLA = 'assets/images/patrolA.png';
  static const String PATROLB = 'assets/images/patrolB.png';
  static const String PATROLC = 'assets/images/patrolC.png';
  static const String PATROLD = 'assets/images/patrolD.png';
  static const String PHONE_CHECK_BG = 'assets/images/phone_check_bg.png';
  static const String PHONE_DEVICE_TYPE = 'assets/images/phone_device_type.png';
  static const String PHONE_LOGIN_BG = 'assets/images/phone_login_bg.png';
  static const String PHONE_MOBILE_ICON = 'assets/images/phone_mobile_icon.png';
  static const String PHONE_PWD_ICON = 'assets/images/phone_pwd_icon.png';
  static const String PHONE_RECORD_BG = 'assets/images/phone_record_bg.png';
  static const String PHONE_SETTING_ICON = 'assets/images/phone_setting_icon.png';
  static const String PHONE_TODO_HOT_PIC = 'assets/images/phone_todo_hot_pic.png';
  static const String PHONE_TOOL_BACK = 'assets/images/phone_tool_back.png';
  static const String PHONE_UPDATE_DATA_ICON = 'assets/images/phone_update_data_icon.png';
  static const String SEARCHCODE_BG = 'assets/images/searchCode_bg.png';
  static const String SINGLEA = 'assets/images/singleA.png';
  static const String SINGLEB = 'assets/images/singleB.png';
  static const String SINGLEC = 'assets/images/singleC.png';
  static const String UPDATE = 'assets/images/update.png';
  static const String USERLOGO = 'assets/images/userLogo.png';
}
