import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../controllers/global_controller.dart';
import './screenutil.dart';

import "package:get/get.dart";
class MyTextField extends StatefulWidget {
  final bool isFocusName; // 是否监听焦点,
  final void Function()? enterFocus;  // 调用进入焦点时的方法
  final void Function()? loseFocus;  // 调用失去焦点时的方法
  final String? initialValue; // 输入的值
  final bool isPassWord; // 是否为密码
  final String hintText; // 默认的提示
  final TextInputType? keyboardType; // 弹出数字键盘
  final void Function(String)? onChanged; // changed 事件
  final FocusNode? focusNode;
  final FocusNode? nextFocusNode;
  final String? keyIndex;
  final bool enabled;

  const MyTextField({
    super.key,
    this.initialValue,
    required this.isPassWord,
    required this.hintText,
    this.keyboardType=TextInputType.number,
    this.onChanged,
    this.isFocusName = false,
    this.enterFocus,
    this.loseFocus,
    this.focusNode,
    this.nextFocusNode,
    this.keyIndex,
    this.enabled = true
  });

  @override
  State<MyTextField> createState() => _MyTextFieldState();
}

class _MyTextFieldState extends State<MyTextField> {
  final TextEditingController _controller = TextEditingController();
  // final FocusNode _focusNode = FocusNode();
  @override
  void initState() {
    super.initState();
    // _focusNode.addListener(_onFocusChange);
    _controller.text = widget.initialValue ?? '';
  }
  // 添加回车获取下一个焦点后,监听焦点变化的回调会出现问题
  // void _onFocusChange() {
  //   if(widget.isFocusName){
  //     if (_focusNode.hasFocus) {
  //       widget.enterFocus!();
  //     } else {
  //       widget.loseFocus!();
  //     }
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return Container(
        child:  TextField(
            key: Key('textfield_${widget.keyIndex}'),
            style:TextStyle(
              fontSize: MyScreenUtil.fontSize(20),
              fontWeight: FontWeight.w600
            ),
            // autofocus: true,
            enabled: widget.enabled,
            focusNode: widget.focusNode,
            controller:widget.isFocusName?_controller:null,
            obscureText:widget.isPassWord,
            onEditingComplete:(){
              if(!widget.isFocusName){
                return;
              }
              if(widget.nextFocusNode == null){
                Fluttertoast.showToast(
                  msg: "已经到最后一项",
                  toastLength: Toast.LENGTH_SHORT,
                  gravity: ToastGravity.CENTER,
                  timeInSecForIosWeb: 3,
                  backgroundColor: Colors.black,
                  textColor: Colors.white,
                  fontSize: 16.0
                );
              }
              FocusScope.of(context).requestFocus(widget.nextFocusNode); // 点击完成时将焦点转移到下一个MyTextField所对应的FocusNode
            },
            keyboardType: widget.keyboardType,  //默认弹出数字键盘
            decoration:  InputDecoration(
              hintText: widget.hintText,
              // border: InputBorder.none  //去掉下划线
            ),
            onChanged: widget.onChanged,
        ),
    );
  }
}