import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/controllers/global_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/myTextField.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';

import '../../../utils/NoShadowScrollBehavior.dart';
import '../controllers/login_controller.dart';

import "../../home/<USER>/home_view.dart";
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter/services.dart';


class LoginView extends GetView<LoginController> {
  GlobalController myHome = Get.find();
  LoginView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx((){
      return WillPopScope(
        onWillPop:()async{
          if(controller.lastPopTime.value == null || DateTime.now().difference(controller.lastPopTime.value) > Duration(seconds: 2)){
            controller.setLastPopTime();
            Fluttertoast.showToast(
              msg: "在按一次退出应用",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.black,
              textColor: Colors.white,
              fontSize: 16.0
            );
            return false;
          }else{
            controller.setLastPopTime();
            // 退出app
            await SystemChannels.platform.invokeMethod('SystemNavigator.pop');
            return false;
          }
        },
        child: Scaffold(
          body: Container(
            decoration: const BoxDecoration(
              // color: Colors.red,
              image:DecorationImage(
                image:AssetImage("assets/images/backImage.jpg"),
                fit: BoxFit.cover
              )
            ),
            child: Center(
              child:ScrollConfiguration(
                behavior: NoShadowScrollBehavior(),
                child: SingleChildScrollView(
                  physics: ClampingScrollPhysics(),
                  child:Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: MyScreenUtil.width(400),
                        height: MyScreenUtil.width(500),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(MyScreenUtil.radius(25)),
                          color: Colors.white,
                        ),
                        child: Column(
                          children: [
                            Container(
                              margin: EdgeInsets.only(top:MyScreenUtil.width(30)),
                              width: MyScreenUtil.width(55),
                              height: MyScreenUtil.height(55),
                              child: Image.asset(
                                "assets/images/logo.png",
                                fit: BoxFit.contain,
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(top:MyScreenUtil.width(30)),
                              child: Text(
                                "${controller.pageName}",
                                style: TextStyle(
                                  fontSize: MyScreenUtil.fontSize(26),
                                  color:Color.fromRGBO(39, 33, 69, 1),
                                  fontWeight: FontWeight.w500
                                ),
                              ),
                            ),
                            // 手机号
                            Container(
                              margin: EdgeInsets.only(top:MyScreenUtil.width(40),bottom: MyScreenUtil.width(20)),
                              width: MyScreenUtil.width(300),
                              child: TextField(
                                  inputFormatters: <TextInputFormatter>[
                                    LengthLimitingTextInputFormatter(11) //限制长度
                                  ],
                                style: TextStyle(
                                  fontSize: MyScreenUtil.fontSize(18),
                                  fontWeight: FontWeight.w500
                                ),
                                controller: controller.textEditingController,
                                decoration: InputDecoration(
                                  prefixIcon: Icon(
                                    Icons.phone_android,
                                    size:MyScreenUtil.fontSize(22)
                                  ),
                                  hintText: "请输入手机号",
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.only(
                                    top: MyScreenUtil.height(10),
                                    bottom:MyScreenUtil.height(10),
                                    left:MyScreenUtil.height(10),
                                    right: MyScreenUtil.height(10),
                                  )
                                ),
                                onChanged: (value) {
                                  controller.setUserCode(value);
                                }
                              ),
                            ),
                            // 密码
                            Container(
                              margin: EdgeInsets.only(bottom: MyScreenUtil.width(10)),
                              width: MyScreenUtil.width(300),
                              child: TextField(
                                obscureText:true,
                                style: TextStyle(
                                  fontSize: MyScreenUtil.fontSize(18),
                                  fontWeight: FontWeight.w500
                                ),
                                decoration: InputDecoration(
                                  prefixIcon: Icon(
                                    Icons.lock_outline,
                                    size:MyScreenUtil.fontSize(22)
                                  ),
                                  hintText: "请输入密码",
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.only(
                                    top: MyScreenUtil.height(10),
                                    bottom:MyScreenUtil.height(10),
                                    left:MyScreenUtil.height(10),
                                    right: MyScreenUtil.height(10),
                                  )
                                ),
                                onChanged: (value) {
                                  controller.setUserPassword(value);
                                }
                              )
                            ),
                            // 登录
                            Container(
                              margin: EdgeInsets.only(top: MyScreenUtil.width(20)),
                              width:  MyScreenUtil.width(300),
                              child: ElevatedButton(
                                onPressed: (){
                                  // 隐藏键盘
                                  FocusScopeNode currentFocus = FocusScope.of(context);
                                  if (!currentFocus.hasPrimaryFocus) {
                                    currentFocus.unfocus();
                                  }
                                  controller.login();
                                },
                                child: const Text("登录"),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  )
                ),
              )
              
            ),
          )
        ) , 
      );

    });
  }
}


