class ItemData {
  ItemData({
    required this.woid,
    required this.stepName,
    required this.id,
     this.parentName,
     this.parentId,
    required this.content,
    required this.operation,
    required this.operatorId,
    required this.operatorName,
    required this.operatorResult,
     this.operatorTime,
    required this.supervisorId,
    required this.supervisorName,
    required this.supervisorResult,
    required this.supervisorTime,
    required this.fillingValue,
    required this.amount,
    required this.gapPrice,
    required this.status,
    required this.gapPicture,
    required this.fillingPictureValue,
  });
  late final String woid;
  late final String stepName;
  late final String id;
  late final String? parentName;
  late final String? parentId;
  late final String content;
  late final String operation;
  late final String operatorId;
  late final String operatorName;
  late final int operatorResult;
  late final String? operatorTime;
  late final String supervisorId;
  late final String supervisorName;
  late final int supervisorResult;
  late final int supervisorTime;
  late final int fillingValue;
  late final int amount;
  late final String gapPrice;
  late final int status;
  late final String gapPicture;
  late final int fillingPictureValue;
  
  ItemData.fromJson(Map<String, dynamic> json){
    woid = json['woid'];
    stepName = json['stepName'];
    id = json['id'];
    parentName = json['parentName'];
    parentId = json['parentId'];
    content = json['content'];
    operation = json['operation'];
    operatorId = json['operatorId'];
    operatorName = json['operatorName'];
    operatorResult = json['operatorResult'];
    operatorTime = json['operatorTime'];
    supervisorId = json['supervisorId'];
    supervisorName = json['supervisorName'];
    supervisorResult = json['supervisorResult'];
    supervisorTime = json['supervisorTime'];
    fillingValue = json['fillingValue'];
    amount = json['amount'];
    gapPrice = json['gapPrice'];
    status = json['status'];
    gapPicture = json['gapPicture'];
    fillingPictureValue = json['fillingPictureValue'];
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['woid'] = woid;
    _data['stepName'] = stepName;
    _data['id'] = id;
    _data['parentName'] = parentName;
    _data['parentId'] = parentId;
    _data['content'] = content;
    _data['operation'] = operation;
    _data['operatorId'] = operatorId;
    _data['operatorName'] = operatorName;
    _data['operatorResult'] = operatorResult;
    _data['operatorTime'] = operatorTime;
    _data['supervisorId'] = supervisorId;
    _data['supervisorName'] = supervisorName;
    _data['supervisorResult'] = supervisorResult;
    _data['supervisorTime'] = supervisorTime;
    _data['fillingValue'] = fillingValue;
    _data['amount'] = amount;
    _data['gapPrice'] = gapPrice;
    _data['status'] = status;
    _data['gapPicture'] = gapPicture;
    _data['fillingPictureValue'] = fillingPictureValue;
    return _data;
  }
}