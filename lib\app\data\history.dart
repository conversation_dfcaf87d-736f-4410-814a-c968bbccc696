

class History {
  final String eid;
  final String mobile;
  final String name;
  final String avatar;
  final String? patrolStartTime;
  final String? patrolEndTime;
  final int patrolStartTimeStamp;
  final int patrolEndTimeStamp;
  String? userId;

  History({
    required this.eid,
    required this.mobile,
    required this.name,
    required this.avatar,
    this.patrolStartTime, 
    this.patrolEndTime,
    required this.patrolStartTimeStamp,
    required this.patrolEndTimeStamp,
    this.userId
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      "eid" : eid,
      "mobile" : mobile,
      "name" : name,
      "avatar" : avatar,
      "patrolStartTime" : patrolStartTime,
      "patrolEndTime" : patrolEndTime,
      "patrolStartTimeStamp" : patrolStartTimeStamp,
      "patrolEndTimeStamp" : patrolEndTimeStamp,
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      "eid" : eid,
      "mobile" : mobile,
      "name" : name,
      "avatar" : avatar,
      "patrolStartTime" : patrolStartTime,
      "patrolEndTime" : patrolEndTime,
      "patrolStartTimeStamp" : patrolStartTimeStamp,
      "patrolEndTimeStamp" : patrolEndTimeStamp,
    };
  }

}
