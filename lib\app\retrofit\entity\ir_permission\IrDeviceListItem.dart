import 'dart:convert';
/// deviceTypeId : "28NQfIapgvU"
/// name : "高压柜"
/// deviceList : [{"deviceId":"28NQhnJZAxI","name":"高压柜-1","infraredDeviceTypeId":"28NQfIapgvU","infraredDeviceTypeName":"高压柜","code":"01010101","num":"1#","installationSite":"位置","project":"A路","upperLimitValue":"38"},{"deviceId":"28NvefKPImx","name":"高压柜-2","infraredDeviceTypeId":"28NQfIapgvU","infraredDeviceTypeName":"高压柜","code":"01010102","num":"2#","installationSite":"2F","project":"A路","upperLimitValue":"30"},{"deviceId":"28NvfQYCrvP","name":"高压柜-3","infraredDeviceTypeId":"28NQfIapgvU","infraredDeviceTypeName":"高压柜","code":"01010103","num":"3#","installationSite":"2F","project":"A路","upperLimitValue":"30"}]

IrDeviceListItem irDeviceListItemFromJson(String str) => IrDeviceListItem.fromJson(json.decode(str));
String irDeviceListItemToJson(IrDeviceListItem data) => json.encode(data.toJson());


class IrDeviceListItem {
  IrDeviceListItem({
      String? deviceTypeId, 
      String? name, 
      List<DeviceList>? deviceList,}){
    _deviceTypeId = deviceTypeId;
    _name = name;
    _deviceList = deviceList;
}

  IrDeviceListItem.fromJson(dynamic json) {
    _deviceTypeId = json['deviceTypeId'];
    _name = json['name'];
    if (json['deviceList'] != null) {
      _deviceList = [];
      json['deviceList'].forEach((v) {
        _deviceList?.add(DeviceList.fromJson(v));
      });
    }
  }
  String? _deviceTypeId;
  String? _name;
  List<DeviceList>? _deviceList;
IrDeviceListItem copyWith({  String? deviceTypeId,
  String? name,
  List<DeviceList>? deviceList,
}) => IrDeviceListItem(  deviceTypeId: deviceTypeId ?? _deviceTypeId,
  name: name ?? _name,
  deviceList: deviceList ?? _deviceList,
);
  String? get deviceTypeId => _deviceTypeId;
  String? get name => _name;
  List<DeviceList>? get deviceList => _deviceList;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['deviceTypeId'] = _deviceTypeId;
    map['name'] = _name;
    if (_deviceList != null) {
      map['deviceList'] = _deviceList?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

/// deviceId : "28NQhnJZAxI"
/// name : "高压柜-1"
/// infraredDeviceTypeId : "28NQfIapgvU"
/// infraredDeviceTypeName : "高压柜"
/// code : "01010101"
/// num : "1#"
/// installationSite : "位置"
/// project : "A路"
/// upperLimitValue : "38"

DeviceList deviceListFromJson(String str) => DeviceList.fromJson(json.decode(str));
String deviceListToJson(DeviceList data) => json.encode(data.toJson());
class DeviceList {
  DeviceList({
      String? deviceId, 
      String? name, 
      String? infraredDeviceTypeId, 
      String? infraredDeviceTypeName, 
      String? code, 
      String? num, 
      String? installationSite, 
      String? project, 
      String? upperLimitValue,}){
    _deviceId = deviceId;
    _name = name;
    _infraredDeviceTypeId = infraredDeviceTypeId;
    _infraredDeviceTypeName = infraredDeviceTypeName;
    _code = code;
    _num = num;
    _installationSite = installationSite;
    _project = project;
    _upperLimitValue = upperLimitValue;
}

  DeviceList.fromJson(dynamic json) {
    _deviceId = json['deviceId'];
    _name = json['name'];
    _infraredDeviceTypeId = json['infraredDeviceTypeId'];
    _infraredDeviceTypeName = json['infraredDeviceTypeName'];
    _code = json['code'];
    _num = json['num'];
    _installationSite = json['installationSite'];
    _project = json['project'];
    _upperLimitValue = json['upperLimitValue'];
  }
  String? _deviceId;
  String? _name;
  String? _infraredDeviceTypeId;
  String? _infraredDeviceTypeName;
  String? _code;
  String? _num;
  String? _installationSite;
  String? _project;
  String? _upperLimitValue;
DeviceList copyWith({  String? deviceId,
  String? name,
  String? infraredDeviceTypeId,
  String? infraredDeviceTypeName,
  String? code,
  String? num,
  String? installationSite,
  String? project,
  String? upperLimitValue,
}) => DeviceList(  deviceId: deviceId ?? _deviceId,
  name: name ?? _name,
  infraredDeviceTypeId: infraredDeviceTypeId ?? _infraredDeviceTypeId,
  infraredDeviceTypeName: infraredDeviceTypeName ?? _infraredDeviceTypeName,
  code: code ?? _code,
  num: num ?? _num,
  installationSite: installationSite ?? _installationSite,
  project: project ?? _project,
  upperLimitValue: upperLimitValue ?? _upperLimitValue,
);
  String? get deviceId => _deviceId;
  String? get name => _name;
  String? get infraredDeviceTypeId => _infraredDeviceTypeId;
  String? get infraredDeviceTypeName => _infraredDeviceTypeName;
  String? get code => _code;
  String? get num => _num;
  String? get installationSite => _installationSite;
  String? get project => _project;
  String? get upperLimitValue => _upperLimitValue;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['deviceId'] = _deviceId;
    map['name'] = _name;
    map['infraredDeviceTypeId'] = _infraredDeviceTypeId;
    map['infraredDeviceTypeName'] = _infraredDeviceTypeName;
    map['code'] = _code;
    map['num'] = _num;
    map['installationSite'] = _installationSite;
    map['project'] = _project;
    map['upperLimitValue'] = _upperLimitValue;
    return map;
  }

}