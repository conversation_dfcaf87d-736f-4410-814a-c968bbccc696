import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_device.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/common/image_loader.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/common/phone_tab_bar.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/common/phone_tool_bar.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/device_list/ir_device_list_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/routes/app_pages.dart';
import 'package:sjzx_patrol_system_mobile/app/routes/route_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';

class IrDeviceListView extends StatelessWidget {

  const IrDeviceListView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<IrDeviceListController>(builder: (_){
      return PhoneTabBar(
        title: '设备管理',
        tabs: _.tabList.map((e) => e.label).toList(),
        body:Container(
          width: double.infinity,
          height: double.infinity,
          child: Column(
            children: [
              Container(
                height: 55,
                padding: EdgeInsets.only(left: 10,right: 10,bottom: 5),
                decoration: BoxDecoration(color: Colors.white),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Expanded(child: Container(
                      padding: EdgeInsets.all(5),
                      child: TextField(
                        cursorColor: const Color(0xff4F70FD),
                        controller: _.searchController,
                        decoration: InputDecoration(
                          hintText: "请输入搜索内容",
                          hintStyle: TextStyle(fontSize: 14 , color: Color(0xff86909C)),
                          focusColor: const Color(0xff4F70FD),

                          focusedBorder:OutlineInputBorder(
                            borderSide: BorderSide(color: const Color(0xff4F70FD)),
                            borderRadius: BorderRadius.all(Radius.circular(5)),
                          ) ,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(5)),
                          ),
                        ),
                      ),
                    )
                    ),
                    TextButton(
                        onPressed: (){
                          _.update();
                        },
                        child:Text("搜索",
                          style: TextStyle(fontSize: 15 ,
                              color: const Color(0xff4F70FD)),
                        )
                    )
                  ],
                ),
              ),
              Expanded(child: TabBarView(
                children: _.tabList.mapIndexed((index, e) {
                  List<IrChestDevice> list = _.getScreenDeviceList(index);
                  return  list.isEmpty ?  Container(
                    alignment: Alignment.center,
                    child: const Text('没有设备'),
                  ):_buildNewUI(context , _, list);
                }
                ).toList(),
              )
              )
            ],
          ),
        ) ,
      );
    });
  }

  Widget _buildNewUI(BuildContext context ,IrDeviceListController controller, List<IrChestDevice> list) {

    return Column(
      children: [
        Expanded(child: ListView.builder(
            itemCount: list.length,
            itemBuilder: (ctx , index){
              var item = list[index];
              return _buildListItem(item, controller);
        }))
      ],
    );
  }

  _buildTabList(IrDeviceListController controller) {
    return Container(
      color: Colors.white,
      height: 60,
      padding: EdgeInsets.all(13),
      child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: controller.tabList.length,
          itemBuilder: (ctx , index){
            var item = controller.tabList[index];
            return _buildTabView(item , (){
              controller.resetScreenType(index);
            });
          }),
    );
  }

  _buildTabView(DeviceTab body, VoidCallback click) {
    return InkWell(
      highlightColor: Colors.transparent,
      splashColor: Colors.transparent,
      onTap: () => click.call(),
      child: Container(
        width: (MyScreenUtil.getScreenWidth() - 30)/3,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 4),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: !body.selected ? Color(0xFFFFFFFF): Color(0xffE8EDFF),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(body.label , style: TextStyle(fontSize: 15 ,color: body.selected ? const Color(0xff4F70FD) :
          const Color(0xff4E5969)),),
        ),
      ),
    );
  }

  Widget _buildListItem(IrChestDevice device , IrDeviceListController controller) {
    return Container(
      margin: const EdgeInsets.only(left: 16 ,right: 16 ,top: 8 , bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16 ,vertical: 18),
      decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.1),
              offset: Offset(0, 0),
              blurRadius: 5,
              spreadRadius: 0,
            ),
          ],
          borderRadius:
          BorderRadius.circular(12)),
      child: Column(
        children: [
          // 柜子设备名称， 编号
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      text: TextSpan(
                          children: [
                            TextSpan(text: device.secondName() ?? '' , style: TextStyle(fontSize: 20,color: Colors.black, fontWeight: FontWeight.bold,))
                          ]
                      )),
                  4.gap,
                  Text('${device.num}', style: const TextStyle(fontSize: 15,color: Colors.black45)),
                ],
              )
            ],
          ),
          5.gap,
          Row(
            children: [
               Column(
                 crossAxisAlignment: CrossAxisAlignment.start,
                 children: [
                   Text('上限值(度)：${device.upperLimitValue}', style: const TextStyle(fontSize: 14,color: Colors.black),),
                   5.gap,
                   Row(
                     children: [
                       const Text('最高温度(度): ', style: TextStyle(fontSize: 14,color: Colors.black),),
                       Text(device.checkTemperature, style: const TextStyle(fontSize: 14,color: Color(0xff4ADC21)),),
                     ],
                   )
                 ],
               ),
               Spacer(),
                InkWell(
                child: ImageLoader(
                  width: 106,
                  height: 54,
                  url: device.path,
                  isCircle: false,
                  placeHolder: AssetsRes.PHONE_TODO_HOT_PIC,
                ),
                onTap: (){
                  FocusManager.instance.primaryFocus?.unfocus();
                  route(Routes.PHONE_CAMERA , params: {
                    'device': device
                  });
                  // controller.testAdd();
                },
              ),
            ],
            // 热温的图片
          ),
          20.gap,
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: const Color(0xffF2F3F5),
            ),
            child: TextField(
                keyboardType: TextInputType.multiline,
                maxLines: 3,
                style: const TextStyle(fontSize: 13, height: 1.5),
                controller: device.textEditingController,
                decoration: const InputDecoration(
                    hintText: "备注：请输入",
                    hintStyle: TextStyle(fontSize: 14 , color: Color(0xff86909C)),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.only(
                      top: 10,
                      bottom:10,
                      left: 10,
                      right: 10,
                    )
                ),
                onChanged: (value) {
                  controller.onCommentChanged(device,value);
                }
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLabelWidget(String label, String? value) {
    return Container(
      alignment: Alignment.centerLeft,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(label, style: TextStyle(fontSize: 13),),
          const Text(':'),
          4.gap,
          Text(value ?? '', style: TextStyle(fontSize: 13),),
        ],
      ),
    );
  }

}
