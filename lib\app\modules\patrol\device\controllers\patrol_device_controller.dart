import 'dart:async';
import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sjzx_patrol_system_mobile/app/model/speciality/speciality.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';
import 'package:flutter_scankit/flutter_scankit.dart';
import 'package:get_storage/get_storage.dart';

import '../../../../controllers/global_controller.dart';
import "../../../../controllers/sqflite_controller.dart";

class PatrolDeviceController extends GetxController {
  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();

  //路由传参
  RxString name = "".obs;
  RxString roomName = "".obs;
  RxString roomType = "".obs;
  RxString roomId = "".obs;
  RxInt enterType = 1.obs;
  RxList specialityList = [].obs;

  // 弹窗参数
  final isTasteValue = 0.obs;
  final isSoundValue = 0.obs;

  //房间列表
  RxList roomData = [].obs;

  // 设备列表
  RxList deviceList = [].obs;
  // 巡检数据列表
  RxList patrolList = [].obs;
  RxList patrolRadioList = [].obs;
  RxList patrolTextList = [].obs;
  RxList focusNodeList = [].obs;
  List patrolControllerList = [];

  // 菜单下标
  RxInt menuIndex = 0.obs;

  // 设备开关
  RxBool switchValue = true.obs;

  //键盘开关
  final box = GetStorage();
  RxBool keyBoard = true.obs;

  @override
  void onInit() {
    super.onInit();
    bool? storageBoard = box.read('keyBoard');
    if (storageBoard != null) {
      keyBoard.value = storageBoard!;
    }
    getSpecialityList();
  }

  @override
  void onReady() async {
    super.onReady();
    // Get.until((route) => Get.currentRoute == '/start');
    roomName.value = Get.arguments["roomName"];
    name.value = Get.arguments["name"];
    roomType.value = Get.arguments["roomType"];
    roomId.value = Get.arguments["roomId"];
    // enterType.value = Get.arguments["enterType"];
    // 查找当前房间room表的数据项
    await findRoomData();
    await findAllRoomData();
    // 查找房间的设备数据
    await findDeviceData();
    await findDeviceFormData(deviceList[0]['formId']);
  }

  @override
  void onClose() {
    super.onClose();
  }

  @override
  void dispose() {
    super.dispose();
    print('永久注销');
  }

  // 菜单选中下标
  menuIndexFun(index, deviceListItem) {
    menuIndex.value = index;
    findDeviceFormData(deviceListItem['formId']);
    findDeviceData();
    deviceProgress();
    update();
  }

  // 查询设备列表
  findDeviceData() async {
    var deviceData = await sqfliteController.findDevices(
        "roomType = '${roomType.value}' AND roomId = '${roomId.value}'");
    final List<Map<String, dynamic>> deviceDataList =
        deviceData.map((room) => room.toJson()).toList();
    deviceList.value = deviceDataList;
    update();
  }

  // 查询巡设备关联的巡检项列表
  findDeviceFormData(formId) async {
    patrolList.value = [];
    patrolTextList.value = [];
    patrolControllerList = [];
    patrolRadioList.value = [];
    var deviceFormData =
        await sqfliteController.findDeviceForm("formId='${formId}'");
    final List<Map<String, dynamic>> deviceFormList =
        deviceFormData.map((deviceForm) => deviceForm.toJson()).toList();
    patrolList.value = deviceFormData;
    patrolRadioList.value = deviceFormData;
    for (var item in patrolList) {
      patrolTextList.add(item.inputValue);
      final TextEditingController _controller = TextEditingController();
      patrolControllerList.add(_controller);
    }
    for (var i = 0; i < patrolList.length; i++) {
      if (patrolList[i].inputValue != null) {
        patrolControllerList[i].text = patrolList[i].inputValue;
      }
    }

    //循环遍历数据库数据,渲染回显输入框

    // 循环巡检项创建表单所需要的聚焦节点,传给MyTextFiled组件用回车时获取下一个表单
    var focusNodeArr = [];
    var Arr = [];
    deviceFormData.forEach((item) {
      focusNodeArr.add(FocusNode());
    });
    focusNodeList.value = focusNodeArr;
    final context = Get.context;
    Timer(Duration(milliseconds: 300), () {
      Get.focusScope?.requestFocus(focusNodeList[0]);
    });

    update();
  }

  //单独更改单选框值
  findDeviceFormList(formId) async {
    var deviceFormData =
        await sqfliteController.findDeviceForm("formId='${formId}'");
    final List<Map<String, dynamic>> deviceFormList =
        deviceFormData.map((deviceForm) => deviceForm.toJson()).toList();
    patrolRadioList.value = deviceFormData;
    // for (var item in patrolList) {
    //   patrolTextList.add(item.inputValue);
    //   final TextEditingController _controller = TextEditingController();
    //   patrolControllerList.add(_controller);
    // }
    // for (var i = 0; i < patrolList.length; i++) {
    //   if (patrolList[i].inputValue != null) {
    //     patrolControllerList[i].text = patrolList[i].inputValue;
    //   }
    // }

    //循环遍历数据库数据,渲染回显输入框

    // 循环巡检项创建表单所需要的聚焦节点,传给MyTextFiled组件用回车时获取下一个表单
    // var focusNodeArr = [];
    // var Arr = [];
    // deviceFormData.forEach((item) {
    //   focusNodeArr.add(FocusNode());
    // });
    // focusNodeList.value = focusNodeArr;
    // final context = Get.context;
    // Timer(Duration(milliseconds: 300), () {
    //   Get.focusScope!.requestFocus(focusNodeList[0]);
    // });

    update();
  }

  // 更新设备关联的巡检项表
  updateDeviceFormData(deviceFormItem, value) async {
    sqfliteController.updateTable('deviceForm',
        {"inputValue": value, 'inputActive': 1}, "id=${deviceFormItem.id}");
  }

  // 巡检进度查询--当切换设备列表时查询下面的巡检数据是否完成
  deviceProgress() async {
    for (var deviceItem in deviceList) {
      var deviceProgressData = await sqfliteController
          .findDeviceForm("formId='${deviceItem['formId']}'");
      var isFinish = deviceProgressData
          .every((item) => item.inputValue != null && item.inputValue != '');
      // 更新当前设备表中的状态 by userId
      sqfliteController.updateTable("device", {"isFinish": isFinish ? 1 : 0},
          "deviceId='${deviceItem['deviceId']}'");
    }
    await findDeviceData();
  }

  // 房间异常弹窗
  roomAbnormalAlter() {
    Get.dialog(
        barrierDismissible: false,
        AlertDialog(
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: const [Text("异常提醒")],
            ),
            content: Obx(() => Container(
                width: MyScreenUtil.width(200),
                height: MyScreenUtil.width(250),
                child: Column(
                  children: [
                    Container(
                      child: Row(
                        children: [
                          Text("异味"),
                          Expanded(
                              child: Container(
                                  child: RadioListTile(
                            title: Text("否"),
                            value: 0,
                            onChanged: (value) {
                              isTasteValue.value = value!;
                            },
                            groupValue: isTasteValue.value,
                          ))),
                          Expanded(
                              child: Container(
                                  child: RadioListTile(
                            title: Text("是"),
                            value: 1,
                            onChanged: (value) {
                              isTasteValue.value = value!;
                            },
                            groupValue: isTasteValue.value,
                          ))),
                        ],
                      ),
                    ),
                    Container(
                      child: Row(
                        children: [
                          Text("异响"),
                          Expanded(
                              child: Container(
                                  child: RadioListTile(
                            title: Text("否"),
                            value: 0,
                            onChanged: (value) {
                              isSoundValue.value = value!;
                            },
                            groupValue: isSoundValue.value,
                          ))),
                          Expanded(
                              child: Container(
                                  child: RadioListTile(
                            title: Text("是"),
                            value: 1,
                            onChanged: (value) {
                              isSoundValue.value = value!;
                            },
                            groupValue: isSoundValue.value,
                          ))),
                        ],
                      ),
                    ),
                    Container(
                      width: MyScreenUtil.width(200),
                      child: ElevatedButton(
                          onPressed: () {
                            sqfliteController.updateTable(
                                'room',
                                {
                                  "isTaste": isTasteValue.value,
                                  "isSound": isSoundValue.value,
                                },
                                "roomId='${roomId.value}' AND type='${roomType.value}'");
                            Get.back();
                          },
                          child: const Text("确定"),
                          style: ButtonStyle(backgroundColor:
                              MaterialStateProperty.resolveWith((states) {
                            return MyScreenUtil.ThemColor();
                          }))),
                    )
                  ],
                )))));
  }

  // 查询当前房间的数据项将异味和异响状态添加在room列中
  findRoomData() async {
    var roomItemData = await sqfliteController
        .findRoomData("roomId='${roomId.value}' AND type='${roomType.value}'");
    final List<Map<String, dynamic>> roomItem =
        roomItemData.map((room) => room.toJson()).toList();
    print(jsonEncode(roomItemData));
    if (roomItemData[0].goTime == null) {
      int currentTimeStamp = DateTime.now().millisecondsSinceEpoch;
      sqfliteController.updateTable(
          'room',
          {
            "goTime": currentTimeStamp,
          },
          "roomId='${roomId.value}' AND type='${roomType.value}'");
    }
    if (roomItemData[0].isTaste == null) {
      // 弹窗提醒
      roomAbnormalAlter();
    }
  }

  //查询当前相同房号房间列表
  findAllRoomData() async {
    roomData.clear();
    //查询当前管理员房间类型权限
    var roomTypeInfoData = await sqfliteController.findRoomTypeInfo("");
    var roomList =
        await sqfliteController.findRoomData("roomId='${roomId.value}'");
    // print('${roomId.value}${roomList}');
    roomList.forEach((item) {
      roomTypeInfoData.forEach((info) {
        if (info.roomType == item.type) {
          if (name.value != item.name) {
            roomData.add({
              'name': item.name,
              'id': item.roomId,
              'type': item.type,
              'isActive': 0
            });
          } else {
            roomData.add({
              'name': item.name,
              'id': item.roomId,
              'type': item.type,
              'isActive': 1
            });
          }
        }
      });
    });
    print(jsonEncode(roomData));
  }

  // 巡检完成校验提醒
  inspectFinishBtn() async {
    await deviceProgress();
    var result = deviceList.every((item) {
      return item['isFinish'] == 1;
    });
    var message = "";
    if (result) {
      message = "该房间所有设备巡检完成,是否退出该房间";
    } else {
      message = "该房间巡检未完成,是否退出该房间";
    }
    Get.dialog(
        barrierDismissible: false,
        AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: const [Text("提醒")],
          ),
          content: Row(
            children: [Text("$message")],
          ),
          actions: [
            ElevatedButton(
                onPressed: () {
                  DateTime timeStamp = DateTime.now();
                  Get.back();
                  Get.back();
                  // 用于返回时可以刷新即将进入前台的页面
                  DateTime d = DateTime.now();
                  globalController.setCheckRoom(d.millisecondsSinceEpoch);
                },
                child: const Text("确定"),
                style: ButtonStyle(backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return MyScreenUtil.ThemColor();
                }))),
            ElevatedButton(
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all<Color>(
                      Color.fromRGBO(143, 147, 153, 1)),
                ),
                onPressed: () {
                  Get.back();
                },
                child: const Text("我在想想")),
          ],
        ));
  }

  // 表单获取焦点
  /*进入焦点的时候将 inputActive 状态改成0，标识正在输入中 */
  enterFocus(item) {
    sqfliteController.updateTable("deviceForm", {'inputActive': 0},
        "id=${item.id} AND formId='${item.formId}'");
    findDeviceFormData(item.formId);
    Future.delayed(Duration(seconds: 1), () {
      sqfliteController.updateTable("deviceForm", {'inputActive': 1},
          "id=${item.id} AND formId='${item.formId}'");
    });
  }

  // 表单失去焦点
  /*进入焦点的时候将 inputActive 状态改成1，标识已保存 */
  loseFocus(item) {
    sqfliteController.updateTable("deviceForm", {'inputActive': 1},
        "id=${item.id} AND formId='${item.formId}'");
    findDeviceFormData(item.formId);
  }

  // 焦点事件
  void onFocusChange(deviceFormData, focusNodeItem, index) {
    if (focusNodeItem.hasFocus) {
      enterFocus!(deviceFormData[index]);
    } else {
      loseFocus!(deviceFormData[index]);
    }
  }

  // 设备切换
  changeDevice(operate) {
    if (operate == "back") {
      if (menuIndex.value <= 0) {
        toastFun("已经是第一个");
      } else {
        menuIndex.value = menuIndex.value - 1;
        menuIndexFun(menuIndex.value, deviceList[menuIndex.value]);
      }
    }
    if (operate == "next") {
      if (menuIndex.value >= deviceList.length - 1) {
        toastFun("已经是最后一个");
      } else {
        menuIndex.value = menuIndex.value + 1;
        menuIndexFun(menuIndex.value, deviceList[menuIndex.value]);
      }
    }
  }

  // 弹窗封装
  toastFun(text) {
    Fluttertoast.showToast(
        msg: text,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  // 判断当前设备设否开启
  isOpenFun(index) {
    if (deviceList.value.isNotEmpty) {
      return deviceList[index]['isOpen'] == 1 ? true : false;
    } else {
      return false;
    }
  }

  // 如果设备未开启则填充 fillDeviceForm 的 inputvalue 为 0
  fillDeviceForm(value, index) async {
    // 更改 device 表中设备开关的状态
    sqfliteController.updateTable("device", {"isOpen": value ? 1 : 0},
        "formId='${deviceList[index]["formId"]}'");
    // 如果设备关闭 则默认所有巡检项为 0
    if (!value) {
      sqfliteController.updateTable("deviceForm", {"inputValue": 0},
          "formId='${deviceList[index]["formId"]}'");
    } else {
      sqfliteController.updateTable("deviceForm", {"inputValue": null},
          "formId='${deviceList[index]["formId"]}'");
    }
    menuIndexFun(index, deviceList[index]);
    update();
  }

  getSpecialityList() async {
    var userInfo = globalController.userInfo.value!.data;
    List<String>? numberStrings = userInfo?.roomType?.split(',');
    List<int> numbers = numberStrings?.map((String numStr) => int.parse(numStr)).toList() ?? [];
    // print(numbers);
    var specialityData = await sqfliteController.findSpecialityList();
    
    List<Speciality> result = specialityData.where((element) => numbers.contains(int.parse(element.roomType))).toList();
    print('00000000${jsonEncode(specialityData)}');
    print('-----------${jsonEncode(result)}');
    List<Speciality> dataList = filterArray(specialityData, numbers);
    dataList.forEach((item) {
      specialityList.add({
        'roomType': item.roomType,
        'roomTypeName':item.roomTypeName,
        'isActive': Color.fromRGBO(246, 248, 250, 1),
        'fontColor':Colors.black,
      });
    });
    changeSpeciality(0);
  } 

  changeSpeciality(num) async{
    specialityList.value.forEach((item){
      item['isActive'] = Color.fromRGBO(246, 248, 250, 1);
      item['fontColor'] = Colors.black;
    });
    specialityList.value[num]['isActive'] = Colors.white;
    specialityList.value[num]['fontColor'] = MyScreenUtil.FontColor();
    roomType.value = specialityList.value[num]['roomType'];
    specialityList.add({});
    specialityList.removeLast();
    await findDeviceData();
    if (deviceList.isNotEmpty) {
      await findDeviceFormData(deviceList[0]['formId'] ?? '');
    } else {
      patrolList.value = [];
      patrolTextList.value = [];
      patrolControllerList = [];
      patrolRadioList.value = [];
    }
    
  }

  openKeyBoard(value) async {
    // 更改 device 表中设备开关的状态
    // print(value);
    keyBoard.value = value;
    box.write('keyBoard', value);
  }

  List<Speciality> filterArray(
      List<Speciality> arrayA, List<int> arrayB) {
    List<Speciality> result = [];

    for (var element in arrayA) {
      if (arrayB.contains(int.parse(element.roomType))) {
        result.add(element);
      }
    }

    return result;
  }

  updateInput(num) {
    for (int i = 0; i < focusNodeList.length; i++) {
      if (focusNodeList[i].hasFocus) {
        patrolControllerList[i].text = "${patrolControllerList[i].text}${num}";
        TextSelection newSelection = TextSelection.collapsed(
            offset: patrolControllerList[i].text.length); // 将光标位置设置为第五个字符后面
        patrolControllerList[i].selection = newSelection;
        updateDeviceFormData(patrolList[i], patrolControllerList[i].text);
      }
    }
  }

  BackInput(num) {
    for (int i = 0; i < focusNodeList.length; i++) {
      if (focusNodeList[i].hasFocus) {
        patrolControllerList[i].text = patrolControllerList[i]
            .text
            .substring(0, patrolControllerList[i].text.length - 1);
        TextSelection newSelection = TextSelection.collapsed(
            offset: patrolControllerList[i].text.length); // 将光标位置设置为第五个字符后面
        patrolControllerList[i].selection = newSelection;
        updateDeviceFormData(patrolList[i], patrolControllerList[i].text);
      }
    }
  }

  EnterInput(context) {
    for (int i = 0; i < focusNodeList.length; i++) {
      if (focusNodeList[i].hasFocus && i + 1 == focusNodeList.length) {
        toastFun('已经是最后一个了');
      } else if (focusNodeList[i].hasFocus) {
        FocusScope.of(context).requestFocus(focusNodeList[i + 1]);
        // 将光标位置设置为第五个字符后面
        TextSelection newSelection = TextSelection.collapsed(
            offset: patrolControllerList[i].text.length);
        patrolControllerList[i].selection = newSelection;
      }
    }
  }

  searchCode() async {
    var scanKit1;
    /**扫一扫相关配置 */
    // 相机和外部存储权限(获取安卓权限)
    final permissions = const [
      Permission.storage,
      Permission.camera,
    ];
    // 相机和外部存储权限(获取iod权限)
    final permissionGroup = const [
      Permission.camera,
      Permission.photos,
    ];

    final permissionGroup1 = const [
      Permission.camera,
      Permission.photos,
      Permission.storage,
    ];

    RxString results = ''.obs;
    scanKit1 = ScanKit();
    scanKit1.addResultListen((val) async {
      var checkItem = [];
      for (var i = 0; i < deviceList.length; i++) {
        if (deviceList[i]['deviceId'] == val) {
          // print('${deviceList[i]['deviceId']},${i}');
          menuIndex.value = i;
          menuIndexFun(menuIndex.value, deviceList[i]);
          toastFun('设备切换成功');
          checkItem.add(deviceList[i]);
        }
      }
      if (checkItem.length < 1) {
        toastFun('查无此设备');
      }
    });


    var pstatus = await permissionGroup1.request();
    var hasGrant = true;
    pstatus.forEach((key, value){
      if(value.isDenied){
        hasGrant = false;
      }
    });
    if (!hasGrant) {
      permissionGroup1.request();

    } else {
      try {
        await scanKit1.startScan();
      } on PlatformException {
        print("扫一扫报错");
      }
    }
  }
}
