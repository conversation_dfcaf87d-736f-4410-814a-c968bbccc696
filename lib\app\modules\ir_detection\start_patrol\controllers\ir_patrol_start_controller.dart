import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_ir_plugin/ir/surface_platform_plugin.dart';
import 'package:flutter_scankit/flutter_scankit.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sjzx_patrol_system_mobile/app/db/db_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/toast.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/ir_detection/widget/ir_dialog.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/time_helper.dart';

import '../../../../controllers/global_controller.dart';
import '../../../../controllers/sqflite_controller.dart';
import "../../../../data/user.dart";
import '../../../../utils/logger.dart';
import '../../../../utils/screenutil.dart';
import '../../../../utils/string_util.dart';
// import 'package:flutter_ir_plugin/SurfacePlatformPlugin.dart';


class IrPatrolStartController extends GetxController {
  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();

  // 用户基本信息
  Rx<UserInfo> userInfo = Rx<UserInfo>(const UserInfo(
    eid: "",
    avatar: "",
    name: "",
    phone: "",
    passWorld: "",
    deptName: "",
    technicalPost: "",
    livingPlace: "",
    gender: 0,
    occupation: "",
    roomTypeName: "",
    createTime: 0,
    floor: "",
    shifts: "",
    roomType: "",
    isUpload: 0,
    patrolStartTimeStamp: 0,
    patrolEndTimeStamp: 0,
    companyId:"",
    companyName:"",
    deviceMaintainPer: 0
  ));

  late ScanKit scanKit;
  /**扫一扫相关配置 */
  // 相机和外部存储权限(获取安卓权限)
  final permissions = const [
    Permission.storage,
    Permission.camera,
    // Permissions.READ_EXTERNAL_STORAGE,
    // Permissions.CAMERA
  ];
  // 相机和外部存储权限(获取iod权限)
  final permissionGroup = const [
    Permission.camera,
    Permission.photos,
    // PermissionGroup.Photos
  ];

  // 房间类型列表
  RxList roomTypeInfoList = [].obs;

  final count = 0.obs;

  RxBool isStartModel = true.obs;

  String userId = "";

  RxString irStartTime = "".obs;
  RxString irEndTime = "".obs;

  @override
  void onInit() async{
    super.onInit();
    userId = globalController.queryCurrentUserId() ?? '';
    if(userId == ""){
      toast('当前未登录');
      Get.back();
      return;
    }
    _checkIsStartIr();

    insertPatrolStartTime();

    queryIrChestList();

    findUserInfo();

    _checkIrIsOver();
  }

  _checkIsStartIr() {
    isStartModel.value = Get.arguments['isStartModel'] ?? true;
    update();
  }

  /// db 查询 （列头柜）
  queryIrChestList() async {
    var chestList = await DBHelper.getIrChestList(userId);
    roomTypeInfoList.value = chestList;
  }


  @override
  void onReady() async {
    super.onReady();

    DBHelper.listenChestList(userId).listen((event) {
      queryIrChestList();
    });
  }

  @override
  void onClose() {
    super.onClose();
  }

  _checkIrIsOver() async {
    var state = await DBHelper.currentIrState(userId);
    if(state == null) return;
    var hasData = await _hasUnIrData();
    if(state.endTimeStamp > 0 && hasData){
      ifFinish();
    }
  }

  // 根据storage中的用户id 查询user表中的用户信息
  findUserInfo() async {
    var userInfoData = await sqfliteController
        .findUsers("eid='${globalController.userInfo.value!.data.eid}'");
    final List<Map<String, dynamic>> userInfolist =
        userInfoData.map((userInfoItem) => userInfoItem.toJson()).toList();
    userInfo.value = userInfoData[0];
    update();
  }

  // 插入巡检时间
  Future insertPatrolStartTime() async {

    var irState = await DBHelper.currentIrState(userId);
    if(irState == null) return;
    if(irState.startTimeStamp > 0) {
      irStartTime.value = TimeHelper.timeStamp2FullFormat(irState.startTimeStamp);
    }else {
      DateTime date = DateTime.now();
      await DBHelper.updateIrStateStartTime(userId, start : date.millisecondsSinceEpoch , end: 0);
      irStartTime.value = TimeHelper.timeStamp2FullFormat(date.millisecondsSinceEpoch);
    };
    if(irState.endTimeStamp > 0) {
      irEndTime.value = TimeHelper.timeStamp2FullFormat(irState.endTimeStamp);
    }
    update();
  }


  // 判断是否已经巡检结束
  ifFinish() async {
    Get.dialog(
        barrierDismissible: false,
        AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: const [Text("提示")],
          ),
          content: Row(
            children: const [Text("请将此次巡检数据上传后在重新巡检")],
          ),
          actions: [
            ElevatedButton(
                onPressed: () {
                  Get.back();
                  Get.back();
                },
                child: const Text("返回首页"),
                style: ButtonStyle(backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return MyScreenUtil.ThemColor();
                }))),
          ],
        ));
  }

  void endIrPatrol () async {
      var state = await DBHelper.currentIrState(userId);
      if(state == null) return;

      var hasData = await _hasUnIrData();
      if(!hasData) {
        toast('您还未开始巡检设备，不能结束巡检');
        return;
      }


      // 查询所有设备进度是否 都是100%
      var hasUnIr = await DBHelper.findUnIrChestList(userId);

      if(state.endTimeStamp > 0) {
        toast('巡检已结束，请上传数据');
        return;
      }

      var message = "此次巡检未完成,是否结束巡检";

      if(!hasUnIr) {
        message = "是否结束此次巡检";
      }

      IrDialog.showDialog('提示', '$message', '确定', '我再想想' , confirmCall: (){
        var date = DateTime.now();
        DBHelper.updateIrStateStartTime(userId , start:state.startTimeStamp , end: date.millisecondsSinceEpoch);
        Get.back();
      });
  }

  Future<bool> _hasUnIrData() async {
    var todoList = await DBHelper.getIrDeviceListByUser(userId);
    var datas = todoList.where((e) => !StringUtil.isEmpty(e.checkTemperature)).toList();
    return datas.isNotEmpty;
  }

}
