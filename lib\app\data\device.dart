

class Device {
  final String deviceId;
  final String formId;
  final String deviceTypeName;
  final String deviceType;
  final String deviceCode;
  final String roomId;
  final String roomType;
  final int isFinish;
  final int isOpen;
  final String? userId;

  const Device({
    required this.deviceId,
    required this.formId,
    required this.deviceTypeName,
    required this.deviceType,
    required this.deviceCode,
    required this.roomId, 
    required this.roomType, 
    required this.isFinish, 
    required this.isOpen, 
    required this.userId,
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      "deviceId" : deviceId,
      "formId" : formId,
      "deviceTypeName" : deviceTypeName,
      "deviceType" : deviceType,
      "deviceCode" : deviceCode,
      "roomId" : roomId,
      "roomType" : roomType,
      "isFinish" : isFinish,
      "isOpen" : isOpen,
      "userId": userId
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  // @override
  Map<String, dynamic> toJson() {
    return {
      "deviceId" : deviceId,
      "formId" : formId,
      "deviceTypeName" : deviceTypeName,
      "deviceType" : deviceType,
      "deviceCode" : deviceCode,
      "roomId" : roomId,
      "roomType" : roomType,
      "isFinish" : isFinish,
      "isOpen" : isOpen,
    };
  }
}